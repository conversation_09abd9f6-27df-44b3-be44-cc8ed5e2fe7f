{"mcpServers": {"puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"], "env": {"PUPPETEER_LAUNCH_OPTIONS": "{ \"headless\": true }", "ALLOW_DANGEROUS": "false"}}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/home/<USER>/Projects/attendifyapp"]}, "postgres": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres", "postgresql://localdeveloper:jednorozec@localhost/attendifyapp_development"]}, "gmail": {"command": "/home/<USER>/.npm-global/bin/mcp-gmail", "args": []}, "linear": {"command": "npx", "args": ["-y", "@tacticlaunch/mcp-linear"], "env": {"LINEAR_API_TOKEN": "************************************************"}}}}