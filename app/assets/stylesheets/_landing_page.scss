.landing {
  min-height: 100vh;
  background: white;
}

.cta-subtitle {
  margin-bottom: 1.5rem;
  color: #4b5563;
}

.landing-footer {
  padding: 2rem 1rem;
  // border-top: 1px solid #e5e7eb;
}

.footer-links {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1.5rem;
}

.footer-links a {
  color: #4b5563;
  text-decoration: none;
}

.footer-links a:hover {
  color: #27A844;
  text-decoration: underline;
}





.reality-check {
  margin-bottom: 4rem;
  text-align: center;
 }
 
 .reality-check h2 {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 1.5rem;
 }
 
 .reality-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
  margin-bottom: 2rem;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
 }
 
 .reality-card {
  background: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  text-align: left;
 }
 
 .reality-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #1F2937;
 }
 
 .reality-card ul {
  padding-left: 1.5rem;
 }
 
 .reality-card li {
  list-style-type: disc;
  margin-bottom: 0.75rem;
  color: #4B5563;
 }
 
 .co-creation {
  background-color: #f9fafb;
  border-radius: 0.75rem;
  padding: 2rem;
  margin-top: 2rem;
 }
 
 .co-creation h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
 }
 
 .co-creation p {
  color: #4B5563;
  margin-bottom: 1.5rem;
  max-width: 36rem;
  margin-left: auto;
  margin-right: auto;
 }


 
 .faq-section {
  max-width: 35rem;
  margin: 3rem auto;
  padding: 0 1rem;
}

.faq-section h2 {
  text-align: center;
  margin-bottom: 2rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.faq-container {
  width: 100%;
}

.faq-item {
  border-top: 1px solid #E5E7EB;
  margin-top: 0.5rem;
}

.faq-item summary {
  padding: 0.75rem 0;
  font-weight: 500;
  cursor: pointer;
  list-style: none;
  position: relative;
  padding-right: 1.5rem;
  outline: none;
}

.faq-item summary::-webkit-details-marker {
  display: none;
}

.faq-item summary::after {
  content: "+";
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  color: #27A844;
  font-size: 1.25rem;
}

.faq-item[open] summary::after {
  content: "−";
}

.faq-answer {
  padding-bottom: 1rem;
  color: #4B5563;
}

@media (max-width: 640px) {
  .faq-section {
    margin: 2rem auto;
  }
}

.btn-icon {
  width: 20px;
  height: 20px;
  margin-left: 8px;
  vertical-align: middle;
}

@media (max-width: 768px) {
  .testimonials-grid {
    grid-template-columns: 1fr;
  }
  
  .footer-links {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }
  
  .faq-question {
    font-size: 1rem;
  }
}
