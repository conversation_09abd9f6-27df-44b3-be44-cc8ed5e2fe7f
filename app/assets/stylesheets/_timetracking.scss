// time-tracking.scss
// Core variables
$header-height: 108px; // Combined height for top bar and nav
$top-bar-height: 60px;
$nav-height: 48px;
$mobile-header-height: 140px;
$footer-height: 60px;

// Colors
$primary: #27A844;
$primary-dark: #1e7e34;
$neutral-dark: #333;
$neutral-light: #f8f9fa;
$border: #dee2e6;
$text: #333;
$text-muted: #666;

// Breakpoints
$tablet: 768px;
$desktop: 1200px;

// time-tracking.scss

.time-tracking {
  max-width: 600px;
  margin: 0 auto;
  padding: 0;

  &-card {
    background: white;
    border-radius: 12px;
    /* box-shadow: 0 2px 4px rgba(0,0,0,0.1); */
    padding: 1.5rem;
    margin-bottom: 1rem;
  }

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;

  }
  &-description {
    display: inline-block;  
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 0.5rem;
    margin-bottom: 1rem;
  }

  &-location-select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 8px;
    margin-bottom: 1rem;
    font-size: 1rem;
    background: white;
  }

  &-timer {
    text-align: center;
    margin-bottom: 1rem;

    &-display {
      font-size: 2rem;
      font-weight: 700;
      margin-bottom: 0.25rem;
    }

  }

  &-button {
    width: 100%;
    padding: 0.875rem;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    border: none;
    margin-bottom: 0.75rem;

    &--start {
      background: $primary;
      color: white;
      &:hover { background: darken($primary, 5%); }
    }

    &--end {
      background: #dc3545;
      color: white;
      &:hover { background: darken(#dc3545, 5%); }
    }

    &--activity {
      background: white;
      border: 2px solid $primary;
      color: $primary;
      &:hover { background: #f8f9fa; }
    }
  }
}

.activity-section {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  /* box-shadow: 0 2px 4px rgba(0,0,0,0.1); */
  margin-top: 1rem;

  &-header {
    width: 100%;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    border: none;
    background: none;
    text-align: left;
    border-bottom: 1px solid #eee;

    &:hover {
      background-color: #f5f5f5;
    }
  }

  &-title {
    font-weight: 500;
  }

  &-content {
    padding: 1rem;
    border-top: 1px solid #eee;
  }

  &-add-activity {
    width: 100%;
    padding: 0.5rem;
    margin-top: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    color: #007BFF;
    background: none;
    border: none;
    cursor: pointer;
    font-weight: 500;

    &:hover {
      background-color: #f0f7ff;
      border-radius: 4px;
    }
  }

  &-log {
    display: flex;
    padding: 0.75rem 0;
    border-bottom: 1px solid #eee;

    &:last-child {
      border-bottom: none;
    }

    &-time {
      width: 5rem;
      color: #666;
    }

    &-details {
      flex: 1;

      h4 {
        font-weight: 500;
        margin-bottom: 0.25rem;
      }

      p {
        color: #666;
        font-size: 0.875rem;
      }
    }
  }
}

.activity-modal {
  position: fixed;
  inset: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;

  &-content {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    width: 100%;
    max-width: 600px;
  }

  &-title {
    font-size: 1.25rem;
    font-weight: 500;
    margin-bottom: 1rem;
  }

  &-textarea {
    width: 100%;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 0.5rem;
    margin-bottom: 1rem;
    font-family: inherit;
    resize: vertical;
  }

  &-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
  }

  &-button {
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    border: none;

    &--cancel {
      background: #f8f9fa;
      color: #666;
      &:hover { background: darken(#f8f9fa, 5%); }
    }

    &--save {
      background: #007BFF;
      color: white;
      &:hover { background: darken(#007BFF, 5%); }
    }
  }
}