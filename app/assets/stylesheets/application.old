// NOTE: Tailwind CSS is now loaded through Vite via main.css
// This file contains legacy SCSS styles and will be replaced in the full SPA

// Týmbox Desktop Layout - Core Styles
// Base Variables
$primary: #3b82f6; // Blue-600
$primary-hover: #2563eb; // Blue-700
$primary-light: #dbeafe; // Blue-100
$primary-text: #2563eb; // Blue-600
$success: #22c55e; // Green-500
$success-light: #dcfce7; // Green-100
$warning: #eab308; // Yellow-500
$warning-light: #fef9c3; // Yellow-100
$danger: #ef4444; // Red-500
$danger-light: #fee2e2; // Red-100
$gray-50: #f9fafb;
$gray-100: #f3f4f6;
$gray-200: #e5e7eb;
$gray-300: #d1d5db;
$gray-400: #9ca3af;
$gray-500: #6b7280;
$gray-600: #4b5563;
$gray-700: #374151;
$gray-800: #1f2937;
$gray-900: #111827;

// Reset & Base
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

// Reset button
button, .button_to, button_to {
  // appearance: none;
  // -webkit-appearance: none;
  line-height: 1;
  margin: 0;
  padding: 0;
  border: none;
  background: none;
  cursor: pointer;
}

body {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  color: $gray-800;
  line-height: 1.5;
  background-color: $gray-50;
}

a {
  text-decoration: none;
  color: inherit;
}

.brand-logo {
  height: 46px;
}

// Layout Classes
.app-container {
  display: flex;
  flex-direction: column; 
  min-height: 100vh;
  overflow-x: hidden;

  @media (min-width: 768px) {
    flex-direction: row; 
  }
}

// Sidebar
// Update sidebar for mobile-first
.sidebar {
  width: 100%;
  height: auto;
  min-height: 0;
  position: fixed;
  z-index: 50;
  background-color: white;
  transform: translateX(-100%); 
  transition: transform 0.3s ease, width 0.3s ease;
  display: flex;
  flex-direction: column;

  &.open {
    transform: translateX(0);
  }

  &.collapsed {
    width: 4rem;
  }

  @media (min-width: 768px) {
    transform: translateX(0);
    width: 17rem;
    height: 100vh;
    position: relative;
    border-right: 1px solid $gray-200;

    &.collapsed {
      width: 4rem;
    }
  }
}

.sidebar-header {
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid $gray-100;
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem 0;
}

.sidebar-footer {
  border-top: 1px solid $gray-100;
  padding: 1rem;
}

.sidebar-divider {
  height: 1px;
  background-color: $gray-100;
  margin: 0.5rem 0;
}

// Time tracking quick-access
.time-tracker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid $gray-100;

  &-collapsed {
    justify-content: center;
  }
}

.time-toggle {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  cursor: pointer;

  &-active {
    background-color: $success;
    color: white;
  }

  &-inactive {
    background-color: $gray-200;
    color: $gray-500;
  }
}

// Navigation
.nav-section {
  margin-bottom: 1.5rem;

  &-title {
    font-size: 0.75rem;
    font-weight: 500;
    color: $gray-500;
    padding: 0 1.25rem;
    margin-bottom: 0.5rem;
  }
}

.nav-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.625rem 0.75rem;
  margin: 0 0.5rem 0.25rem 0.5rem;
  border-radius: 0.375rem;
  cursor: pointer;
  color: $gray-600;
  transition: all 0.15s ease;

  &-icon {
    color: currentColor;
    margin-right: 0.75rem;
  }

  &-text {
    font-size: 0.875rem;
    font-weight: 500;
  }

  &-active {
    background-color: $primary-light;
    color: $primary;
  }

  &:hover:not(&-active) {
    background-color: $gray-100;
  }

  &-collapsed {
    justify-content: center;
    padding: 0.625rem;
    margin: 0 0.5rem 0.25rem 0.5rem;

    .nav-item-icon {
      margin-right: 0;
    }
  }
}

.nav-badge {
  font-size: 0.7rem;
  font-weight: 500;
  padding: 0.1rem 0.5rem;
  border-radius: 0.25rem;
  background-color: $primary-light;
  color: $primary;
  margin-right: 0.5rem;
}

// Main Content Area
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  margin-top: 4rem; 

  @media (min-width: 768px) {
    margin-top: 0;
  }
}

// Top Bar
// .topbar {
//   position: fixed;
//   top: 0;
//   left: 0;
//   right: 0;
//   z-index: 40;
//   height: 4rem;
//   background-color: white;
//   border-bottom: 1px solid $gray-200;
//   display: flex;
//   align-items: center;
//   justify-content: space-between;
//   padding: 0 1rem;

//   @media (min-width: 768px) {
//     position: relative;
//     padding: 0 1.5rem;
//   }
// }

// Update company selector for mobile
.company-selector {
  display: flex;
  align-items: center;
  background: white;
  border: 1px solid $gray-300;
  border-radius: 0.375rem;
  padding: 0.375rem 0.5rem; 
  font-weight: 500;
  color: $gray-700;
  font-size: 0.875rem;
  gap: 0.25rem;

  @media (min-width: 768px) {
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    gap: 0.5rem;
  }
}

.plan-badge {
  display: flex;
  align-items: center;
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  background-color: $primary-light;
  color: $primary;
}

.topbar-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;

  .topbar-btn {
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.375rem;
    color: $gray-600;
    cursor: pointer;
    position: relative;

    &:hover {
      background-color: $gray-100;
    }

    .notification-badge {
      position: absolute;
      top: 0.25rem;
      right: 0.25rem;
      width: 1rem;
      height: 1rem;
      border-radius: 9999px;
      background-color: $danger;
      color: white;
      font-size: 0.75rem;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

// Content Area
// Update content area for mobile-first
.content-area {
  flex: 1;
  overflow-y: auto;
  // margin-top: 64px;
  padding: 0;

  @media (min-width: 768px) {
    margin-top: 0;
    padding: 1.5rem;
  }

  // CUSTOM GRID LAYOUT
  .grid {
    display: grid;
    // gap: 1rem; 

    @media (min-width: 768px) {
      // gap: 1.5rem;
    }
  }

  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .grid-cols-2 {
    @media (min-width: 1111px) { 
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  .grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
    
    @media (min-width: 1165px) { 
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
    @media (min-width: 1536px) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  .col-span-1 {
    grid-column: span 1 / span 1;
  }

  .col-span-2 {
    grid-column: span 1 / span 1;

    @media (min-width: 768px) { 
      grid-column: span 2 / span 2;
    }
  }
  
  .card {
    // min-width: 380px;
    max-width: 500px;
  }
}


// Cards & Panels
// Update card styles for mobile
.card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 0.5rem;

  @media (min-width: 768px) {
    border-radius: 0.75rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  &-header {
    padding: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid $gray-100;
    gap: 0.5rem;

    @media (min-width: 768px) {
      padding: 1rem;
      gap: 1rem;
    }

    h2 {
      font-size: 1rem;
      font-weight: 600;
      color: $gray-800;
      margin: 0;

      @media (min-width: 768px) {
        font-size: 1.125rem;
      }
    }

    span {
        white-space: nowrap;
        flex-shrink: 0;
    }
  }

  &-content {
    padding: 0.5rem;

    @media (min-width: 768px) {
      padding: 1rem;
    }
  }

  &-footer {
    padding: 0.5rem;
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;

    @media (min-width: 768px) {
        padding: 1rem;
    }
  }
}


.panel {
  padding: 1rem;
  background-color: white;
  border-radius: 0.5rem;
  border: 1px solid $gray-100;
  margin-bottom: 1rem;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: $gray-50;
  }
}

// Status indicators
.status-indicator {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background-color: $gray-300;

  &-active {
    background-color: $success;
  }

  &-away {
    background-color: $warning;
  }
}


// Badges
.badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;

  &-success {
    background-color: $success-light;
    color: darken($success, 10%);
  }

  &-warning {
    background-color: $warning-light;
    color: darken($warning, 10%);
  }

  &-danger {
    background-color: $danger-light;
    color: darken($danger, 10%);
  }

  &-gray {
    background-color: $gray-100;
    color: $gray-800;
  }
}

// Buttons
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;

  &-primary {
    background-color: $primary;
    color: white;

    &:hover {
      background-color: $primary-hover;
    }
  }

  &-secondary {
    background-color: $gray-100;
    color: $gray-600;

    &:hover {
      background-color: $gray-200;
    }
  }

  &-outline {
    border: 1px solid $gray-300;
    background-color: transparent;
    color: $gray-700;

    &:hover {
      background-color: $gray-50;
    }
    &:disabled {
      background-color: $gray-200;
      color: $gray-400;
      cursor: not-allowed;
    }
  }

  &-light {
    background-color: $primary-light;
    color: $primary;

    &:hover {
      background-color: darken($primary-light, 5%);
    }
  }
  &-danger-light {
    background-color: $danger-light;
    color: $danger;

    &:hover {
      background-color: darken($danger-light, 5%);
    }
  }
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}



// Add mobile menu toggle button
.mobile-menu-toggle {
  display: block;
  padding: 0.5rem;
  margin-right: 0.5rem;
  background: none;
  border: none;
  cursor: pointer;
  color: $gray-600; 

  &:hover {
     color: $gray-800;
  }

  @media (min-width: 768px) {
    display: none;
  }
}

// Update the mobile overlay style
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 45; // Should be below the sidebar (z-index: 50)
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;

  &.active { 
      opacity: 1;
      visibility: visible;
  }

  @media (min-width: 768px) {
    display: none;
  }
}

// Modal styles for application-wide use
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9990; 
}

.modal-container {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 550px; 
  max-height: 90vh;
  min-height: 600px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.8rem;
  cursor: pointer;
  color: #666;
  padding: 0;
  line-height: 1;
}

.close-btn:hover {
  color: #333;
}

.central-modal-content {
  padding: 20px;
  overflow-y: auto; 
  flex-grow: 1;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
  padding: 15px 20px;
  border-top: 1px solid #eee;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  font-size: 14px;
}

// NEW FORM STYLES
.form-section {
  margin-bottom: 1.5rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  margin-bottom: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: $gray-700;
}

.form-input,
.form-textarea,
.form-select {
  display: block;
  width: 100%;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: $gray-900;
  background-color: white;
  border: 1px solid $gray-300;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

  &:focus {
    outline: none;
    border-color: $primary;
    box-shadow: 0 0 0 3px rgba($primary, 0.2);
  }

  &::placeholder {
    color: $gray-400;
  }
}

.form-textarea {
  min-height: 80px;
  resize: vertical;
}

.form-checkbox-group {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
}

.form-checkbox {
  height: 1rem;
  width: 1rem;
  border-radius: 0.25rem;
  border: 1px solid $gray-300;
  color: $primary;
  margin-right: 0.5rem;
  cursor: pointer;
  flex-shrink: 0;

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba($primary, 0.2);
  }

  &:checked {
    background-color: $primary;
    border-color: $primary;
  }
}

.form-checkbox-label {
  font-size: 0.875rem;
  color: $gray-700;
  cursor: pointer;
}

// Add styles for radio buttons
.form-radio-group {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem; // Consistent spacing
}

.form-radio {
  // Appearance might vary slightly, aiming for consistency
  appearance: none;
  -webkit-appearance: none;
  height: 1rem;
  width: 1rem;
  border-radius: 50%; // Radio buttons are circular
  border: 1px solid $gray-300;
  margin-right: 0.5rem;
  cursor: pointer;
  flex-shrink: 0;
  position: relative; // For the inner circle

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba($primary, 0.2);
  }

  &:checked {
    border-color: $primary;
    // Add inner circle
    &::after {
      content: "";
      display: block;
      width: 0.5rem; // Size of the inner dot
      height: 0.5rem;
      border-radius: 50%;
      background-color: $primary;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
}

.form-radio-label {
  font-size: 0.875rem;
  color: $gray-700;
  cursor: pointer;
}
// End radio button styles

.form-file-input {
  display: block;
  width: 100%;
  font-size: 0.875rem;
  color: $gray-700;

  &::file-selector-button {
    padding: 0.375rem 0.75rem;
    margin-right: 0.75rem;
    border: 1px solid $gray-300;
    border-radius: 0.375rem;
    background-color: white;
    color: $gray-700;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.15s ease;

    &:hover {
      background-color: $gray-50;
    }
  }
}

.form-error-message {
  color: $danger;
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

.form-button {
  @extend .btn;
  @extend .btn-primary;
}

.form-button-secondary {
  @extend .btn;
  @extend .btn-secondary;
}

.form-button-outline {
  @extend .btn;
  @extend .btn-outline;
}

// Style for small hint text below form inputs
.form-hint {
  display: block; // Ensure it takes its own line if needed
  font-size: 0.75rem; // Smaller text
  color: $gray-500; // Lighter color
  margin-top: 0.25rem; // Space above
}

// END NEW FORM STYLES

// Form Error Styling
.form-error-container {
  background-color: $danger-light;
  border: 1px solid darken($danger-light, 10%);
  border-radius: 0.375rem; // Match form input radius
  padding: 0.75rem; // Add some padding
}

.form-error-item {
  color: darken($danger, 10%);
  font-size: 0.875rem;
  margin-bottom: 0.25rem;

  &:last-child {
    margin-bottom: 0;
  }
}

// NEW Content Panel Class
.content-panel {
  background-color: white;
  border-radius: 0.5rem;
  border: 1px solid $gray-200;
  padding: 1rem; 
  margin-bottom: 1.5rem;
}

// For the public sites
.public-layout {
  min-height: 100vh;
  background: white;

  .main-content {
    max-width: 56rem;
    min-height: calc(100vh - 99px);
    margin: 0 auto;
    padding: 1rem 1rem 0 1rem;
    
    @media (max-width: 768px) {
      padding: 2rem 0.5rem;
    }
  }
  
  .footer {
    background-color: #f8f9fa;
    padding: 1rem 1rem;
    margin-top: 3rem;
    border-top: 1px solid #e9ecef;
    text-align: center;
  }
  // .footer {
  //   background-color: $gray-800;
  //   color: white;
  //   padding: 1rem;
  //   text-align: center;
  //   font-size: 0.875rem;
  // }
  .beta-tag {
    background-color: $primary;
    color: white;
    font-size: 0.625rem;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    vertical-align: super;
    margin-left: 0.25rem;
  }
}

