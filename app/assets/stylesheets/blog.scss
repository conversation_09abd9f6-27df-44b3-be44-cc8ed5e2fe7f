// Colors
$primary: #27A844;
$primary-hover: #27a84391;
$blue: #007BFF; 
$red: #dc3545;
$text: #1F2937;
$text-light: #4B5563;
$text-lighter: #6B7280;
$border: #E5E7EB;
$bg-light: #F9FAFB;
$text-muted: #666;
$neutral-light: #f8f9fa;
$purple: #9747FF;

$font-size-base: 14px;

/* Blog Post Styles */
.blog-post {
  max-width: 800px;
  margin: 0 auto;
  padding: 1.5rem;
  font-size: 16px;
  line-height: 1.4;
  color: $text;
  p {
    margin-bottom: 0.5rem;
  }
}

.blog-header {
  margin-bottom: 2rem;
}

.post-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.75rem;
  font-size: 0.85rem;
  color: $text-lighter;
}

.post-category {
  background-color: $primary;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 500;
}

.post-title {
  font-size: 2rem;
  margin-bottom: 1.5rem;
  font-weight: 700;
  
  @media (max-width: 768px) {
    font-size: 1.5rem;
  }
}

.post-author {
  display: flex;
  align-items: center;
  margin-top: 1.5rem;
}

.author-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  margin-right: 1rem;
}

.author-info {
  display: flex;
  flex-direction: column;
}

.author-name {
  font-weight: 600;
}

.author-role {
  font-size: 0.85rem;
  color: $text-lighter;
}

.feature-image-container {
  margin: 2rem -1.5rem;
  
  @media (min-width: 768px) {
    margin: 2rem 0;
    border-radius: 8px;
    overflow: hidden;
  }
}

.feature-image {
  width: 100%;
  height: auto;
  display: block;
}

.post-content {
  margin-top: 2rem;
}

.post-intro {
  font-size: 1.2rem;
  margin-bottom: 1.5rem;
  color: $text;
  font-weight: 500;
  
  @media (max-width: 768px) {
    font-size: 1.1rem;
  }
}

.post-section {
  margin: 2.5rem 0;
}

.post-section h2 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
  color: $text;
  
  @media (max-width: 768px) {
    font-size: 1.3rem;
  }
}

.post-quote {
  font-size: 1.4rem;
  color: $primary;
  font-weight: 600;
  font-style: italic;
  padding: 0 1.5rem;
  border-left: 4px solid $primary;
  margin: 2rem 0;
  
  @media (max-width: 768px) {
    font-size: 1.2rem;
  }
}

.comparison-box {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin: 1.5rem 0;
  
  .comparison-item {
    flex: 1;
    min-width: 250px;
    background-color: $bg-light;
    border-radius: 8px;
    padding: 1.25rem;
    
    h4 {
      margin-bottom: 1rem;
      text-align: center;
      font-weight: 600;
    }
  }
}

.comparison-list {
  list-style: none;
  padding: 0;
  margin: 1rem;
  
  li {
    padding-left: 1.5rem;
    position: relative;
    margin-bottom: 0.5rem;
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      width: 1rem;
      height: 1rem;
      background-size: contain;
      background-repeat: no-repeat;
    }
  }
  
  &.negative li::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23dc3545' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E");
  }
  
  &.positive li::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%2327A844' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
  }
}

.info-box {
  background-color: #e9f7ff;
  padding: 2rem;
  margin: 1.5rem 0;
  border-radius: 0 8px 8px 0;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  
  p {
    margin: 0;
  }
}

.image-with-caption {
  margin: 1.5rem 0;
  
  .section-image {
    width: 100%;
    border-radius: 8px;
    overflow: hidden;
  }
  
  .image-caption {
    text-align: center;
    font-style: italic;
    color: $text-lighter;
    font-size: 0.9rem;
    margin-top: 0.5rem;
  }
}

.key-points {
  background-color: $bg-light;
  padding: 1.5rem;
  border-radius: 8px;
  margin: 1.5rem 0;
  
  h3 {
    margin-bottom: 1rem;
    font-weight: 600;
  }
}

.checkmark-list {
  list-style: none;
  padding: 0;
  margin: 0;
  
  li {
    padding-left: 1.75rem;
    position: relative;
    margin-bottom: 0.75rem;
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      width: 1.25rem;
      height: 1.25rem;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 24 24' fill='none' stroke='%2327A844' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M22 11.08V12a10 10 0 1 1-5.93-9.14'%3E%3C/path%3E%3Cpolyline points='22 4 12 14.01 9 11.01'%3E%3C/polyline%3E%3C/svg%3E");
      background-size: contain;
      background-repeat: no-repeat;
    }
  }
}

.conclusion {
  border-top: 1px solid $border;
  padding-top: 2rem;
}

.share-container {
  margin: 3rem 0;
  text-align: center;
  
  h4 {
    margin-bottom: 1rem;
    font-weight: 600;
  }
  
  .share-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
  }
  
  .share-button {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    color: white;
    
    &.facebook {
      background-color: #3b5998;
    }
    
    &.twitter {
      background-color: #1da1f2;
    }
    
    &.linkedin {
      background-color: #0077b5;
    }
    
    &.email {
      background-color: #666;
    }
  }
}

.related-posts {
  margin: 3rem 0;
  
  h3 {
    text-align: center;
    margin-bottom: 1.5rem;
    font-weight: 600;
  }
  
  .related-posts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
  }
  
  .related-post-card {
    text-decoration: none;
    color: $text;
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: transform 0.3s, box-shadow 0.3s;
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 15px rgba(0,0,0,0.1);
    }
    
    .related-post-image {
      width: 100%;
      height: 150px;
      object-fit: cover;
    }
    
    h4 {
      padding: 1rem;
      margin: 0;
      font-size: $font-size-base;
      font-weight: 600;
    }
  }
}

.numbered-list-container {
  padding: 1rem 0;
}

.numbered-list {
  padding-left: 2rem;
  margin-bottom: 1.5rem;
}

.numbered-list li {
  margin-bottom: 0.75rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .blog-post {
    padding: 1rem;
  }
  
  .share-container {
    margin: 2rem 0;
  }
  
  .related-posts-grid {
    grid-template-columns: 1fr;
  }
}

.info-box.prominent {
  background-color: #fffbe6;
  color: $text;
}

.simple-list {
  list-style: none;
  padding-left: 1.5rem;
  margin: 1.25rem 0;
  
  li {
    position: relative;
    padding-left: 0.75rem;
    margin-bottom: 0.75rem;
    line-height: 1.5;
    
    &::before {
      content: '';
      position: absolute;
      left: -1rem;
      top: 0.5rem;
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background-color: $primary;
    }
  }
}