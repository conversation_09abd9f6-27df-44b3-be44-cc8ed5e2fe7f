/* Core variables */
:root {
--primary-dark: #18973c;
--neutral-dark: #2a2d34;
--text: #2a2d34;
--text-muted: #707580;
}

/* Terms and Conditions Styles */
.terms {
  list-style-type: decimal;
  padding-left: 1.5em;
  font-size: 14px;
  color: var(--text);
  line-height: 1.5;
}

.terms > li {
  margin-bottom: 1em;
}

.terms ol {
  list-style-type: lower-alpha;
  padding-left: 1.5em;
}

.terms ol ol {
  list-style-type: decimal;
  padding-left: 1.5em;
}

h2 {
  font-size: 24px;
  color: var(--neutral-dark);
  margin-top: 0.875em;
  margin-bottom: 0.5em;
}

.my-1 {
  margin: 1em 0;
}