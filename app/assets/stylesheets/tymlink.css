
@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Outfit:wght@100..900&display=swap');

/* =============================== */
/* Variables */
/* =============================== */
:root {
  /* --primary: #22C55E; */
  --primary: #27A844;
  --primary-hover: #16A34A;
  --blue1: #0056D2; 
  --red: #dc3545;
  --text: #1F2937;
  --text-light: #4B5563;
  --text-lighter: #6B7280;
  --border: #E5E7EB;
  --bg-light: #F9FAFB;
  --text-muted: #666;
  --neutral-light: #f8f9fa;
  --purple: #9747FF;
}

/* =============================== */
/* Base Styles */
/* =============================== */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* Reset button */
button, .button_to, button_to {
  /* appearance: none; */
  /* -webkit-appearance: none; */
  line-height: 1;
  margin: 0;
  padding: 0;
  border: none;
  background: none;
  cursor: pointer;
}

body {
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  line-height: 1.25;
  color: var(--text);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.landing {
  min-height: 100vh;
  background: white;
}

/* =============================== */
/* Layout Components */
/* =============================== */
.main-content {
  /* max-width: 56rem; */
  min-height: calc(100vh - 62px - 83px);
  margin: 0 auto;
  padding: 4rem 1rem 0 1rem;
}

@media (max-width: 768px) {
  .main-content {
    padding: 2rem 0.5rem;
  }
}


/* =============================== */
/* Navigation and Header */
/* =============================== */

.brand-logo {
  height: 46px;
}

.footer, .landing-footer {
  border-top: 1px solid var(--border);
  padding: 2rem;
  text-align: center;
  color: var(--text-lighter);
}

.footer-links {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.footer-links a {
  color: var(--text-light);
  text-decoration: none;
}
  
.footer-links a:hover {
  color: var(--primary);
}

@media (max-width: 768px) {
  .footer-links {
    flex-direction: column;
    gap: 1rem;
  }
}

/* Fucking textarea user agent override. */
textarea {
  all: initial;
  font-family: "Inter" !important;
  box-sizing: border-box !important;
  display: block !important;
  width: 100% !important;
  min-height: 100px !important;
  padding: 0.75rem !important;
  border: 1px solid #dee2e6 !important; /* Note: Using hardcoded color, not a variable */
  border-radius: 8px !important;
  background-color: white !important;
  color: black !important;
  resize: vertical !important;
}


/* Primary Actions - Need Attention */
.badge.pending, .badge.new {
  background-color: #d84c26;
  color: #ffffff;
} 

/* Active States - Currently In Process */
.badge.in_progress {
  background-color: #f1a24f;
  color: #ffffff;
}

/* Scheduled / Confirmed */
.badge.scheduled, .badge.confirmed {
  background-color: #2a81b7;
  color: #ffffff;
}

/* Terminal States - Reduced Visual Importance */
.badge.completed, .badge.inactive, .badge.cancelled {
  background-color: #9e9e9e;
  color: #ffffff;
}

/* Exception States */
.badge.exception {
  background-color: #f8d7da;
  color: #d32f2f;
}

/* Note: .cancelled selector might conflict with .badge.cancelled if used on the same element */
.cancelled {
  background-color: #f8d7da;
  color: #d32f2f;
} 