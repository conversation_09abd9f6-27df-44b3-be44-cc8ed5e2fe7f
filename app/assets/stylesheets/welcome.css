:root {
  --primary: #27A844;
  --primary-hover: #27a84391;
  --blue: #007BFF;
  --red: #dc3545;
  --text: #1F2937;
  --text-light: #4B5563;
  --text-lighter: #6B7280;
  --border: #E5E7EB;
  --bg-light: #F9FAFB;
  --text-muted: #666;
  --neutral-light: #f8f9fa;
  --purple: #9747FF;
  --orange: #FF8800;
  --orange-hover: #E67A00; /* Calculated darken */
  --blue-darker: #006bdd; /* Calculated darken */
  --red-darker: #c82333; /* Calculated darken */
  --primary-darker: #218838; /* Calculated darken */

  --font-size-base: 14px;
}

/* =============================== */
/* Blog Styles (from blog.scss)  */
/* =============================== */

.blog-post {
  max-width: 800px;
  margin: 0 auto;
  padding: 1.5rem;
  font-size: 16px;
  line-height: 1.4;
  color: var(--text);
}
.blog-post p {
  margin-bottom: 0.5rem;
}

.blog-header {
  margin-bottom: 2rem;
}

.post-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.75rem;
  font-size: 0.85rem;
  color: var(--text-lighter);
}

.post-category {
  background-color: var(--primary);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 500;
}

.post-title {
  font-size: 2rem;
  margin-bottom: 1.5rem;
  font-weight: 700;
}

@media (max-width: 768px) {
  .post-title {
    font-size: 1.5rem;
  }
}

.post-author {
  display: flex;
  align-items: center;
  margin-top: 1.5rem;
}

.author-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  margin-right: 1rem;
}

.author-info {
  display: flex;
  flex-direction: column;
}

.author-name {
  font-weight: 600;
}

.author-role {
  font-size: 0.85rem;
  color: var(--text-lighter);
}

.feature-image-container {
  margin: 2rem -1.5rem;
}

@media (min-width: 768px) {
  .feature-image-container {
    margin: 2rem 0;
    border-radius: 8px;
    overflow: hidden;
  }
}

.feature-image {
  width: 100%;
  height: auto;
  display: block;
}

.post-content {
  margin-top: 2rem;
}

.post-intro {
  font-size: 1.2rem;
  margin-bottom: 1.5rem;
  color: var(--text);
  font-weight: 500;
}

@media (max-width: 768px) {
  .post-intro {
    font-size: 1.1rem;
  }
}

.post-section {
  margin: 2.5rem 0;
}

.post-section h2 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
  color: var(--text);
}

@media (max-width: 768px) {
  .post-section h2 {
    font-size: 1.3rem;
  }
}

.post-quote {
  font-size: 1.4rem;
  color: var(--primary);
  font-weight: 600;
  font-style: italic;
  padding: 0 1.5rem;
  border-left: 4px solid var(--primary);
  margin: 2rem 0;
}

@media (max-width: 768px) {
  .post-quote {
    font-size: 1.2rem;
  }
}

.comparison-box {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin: 1.5rem 0;
}

.comparison-box .comparison-item {
  flex: 1;
  min-width: 250px;
  background-color: var(--bg-light);
  border-radius: 8px;
  padding: 1.25rem;
}

.comparison-box .comparison-item h4 {
  margin-bottom: 1rem;
  text-align: center;
  font-weight: 600;
}

.comparison-list {
  list-style: none;
  padding: 0;
  margin: 1rem;
}

.comparison-list li {
  padding-left: 1.5rem;
  position: relative;
  margin-bottom: 0.5rem;
}

.comparison-list li::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 1rem;
  height: 1rem;
  background-size: contain;
  background-repeat: no-repeat;
}

.comparison-list.negative li::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23dc3545' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E");
}

.comparison-list.positive li::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%2327A844' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
}

.info-box {
  background-color: #e9f7ff; /* Note: Color not in variables */
  padding: 2rem;
  margin: 1.5rem 0;
  border-radius: 0 8px 8px 0;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.info-box p {
  margin: 0;
}

.image-with-caption {
  margin: 1.5rem 0;
}

.image-with-caption .section-image {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.image-with-caption .image-caption {
  text-align: center;
  font-style: italic;
  color: var(--text-lighter);
  font-size: 0.9rem;
  margin-top: 0.5rem;
}

.key-points {
  background-color: var(--bg-light);
  padding: 1.5rem;
  border-radius: 8px;
  margin: 1.5rem 0;
}

.key-points h3 {
  margin-bottom: 1rem;
  font-weight: 600;
}

.checkmark-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.checkmark-list li {
  padding-left: 1.75rem;
  position: relative;
  margin-bottom: 0.75rem;
}

.checkmark-list li::before {
  content: '';
  position: absolute;
  left: 0;
  top: 4px; /* Adjust vertical alignment */
  width: 1rem;
  height: 1rem;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%2327A844' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
}

.post-conclusion {
  margin-top: 2.5rem;
  padding-top: 2rem;
  border-top: 1px solid var(--border);
}

.conclusion-title {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.related-posts {
  margin-top: 3rem;
}

.related-posts-title {
  text-align: center;
  font-size: 1.4rem;
  margin-bottom: 2rem;
  font-weight: 600;
}

.related-posts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.related-post-card {
  border: 1px solid var(--border);
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.3s, box-shadow 0.3s;
}

.related-post-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.related-post-card img {
  width: 100%;
  height: 150px;
  object-fit: cover;
}

.related-post-content {
  padding: 1rem;
}

.related-post-category {
  font-size: 0.8rem;
  color: var(--primary);
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.related-post-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text);
  text-decoration: none;
}

.related-post-title:hover {
  text-decoration: underline;
}

.related-post-excerpt {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-bottom: 1rem;
}

.read-more-link {
  font-size: 0.9rem;
  color: var(--primary);
  text-decoration: none;
  font-weight: 500;
}

.read-more-link:hover {
  text-decoration: underline;
}

/* =============================== */
/* Welcome Styles (Original welcome.scss) */
/* =============================== */

/* Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', sans-serif;
  font-smooth: always;
  font-style: normal;
  font-size: var(--font-size-base);
  line-height: 1.25;
}

.landing {
  min-height: 100vh;
  background: white;
  /* Added padding-top here from nested style */
  padding-top: 62px;
}

/* Layout Components */
.main-content {
  max-width: 56rem;
  min-height: calc(100vh - 62px - 83px);
  margin: 0 auto;
  padding: 4rem 1rem 0 1rem;
}

@media (max-width: 768px) {
  .main-content {
    padding: 2rem 0.5rem;
  }
}

.section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
  align-items: center;
}

.box {
  max-width: 500px;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin: 0 auto;
}

.landing-footer {
  background-color: var(--neutral-light);
  padding: 2rem 1rem;
  margin-top: 3rem;
  border-top: 1px solid var(--border);
}

.footer-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
}

.footer-column {
  width: 100%;
  margin-bottom: 1.5rem;
  padding: 0 1rem;
}

.footer-column h4 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #333; /* Note: Color not in variables */
}

.footer-column ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-column ul li {
  margin-bottom: 0.5rem;
}

.footer-column ul li a {
  color: var(--text-muted);
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.2s ease;
}

.footer-column ul li a:hover {
  color: var(--blue);
  text-decoration: underline;
}

.social-links {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.social-links a {
  color: var(--text-muted);
  transition: color 0.2s ease;
}

.social-links a:hover {
  color: var(--blue);
}

.newsletter-text {
  font-size: 0.85rem;
  margin-bottom: 0.5rem;
  color: var(--text-muted);
}

.footer-bottom {
  text-align: center;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--border);
  font-size: 0.85rem;
  color: var(--text-muted);
}

/* Responsive adjustments */
@media (min-width: 768px) {
  .footer-column {
    width: 50%;
  }
}

@media (min-width: 992px) {
  .footer-column {
    width: 25%;
  }
}

/* Navigation and Header */
.top-nav {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  padding: 0.5rem;
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.top-nav .nav-wrapper {
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.top-nav .brand-logo {
  height: 32px;
}

.top-nav .nav-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

/* Common Components */

/* Hero sections */
.hero {
  text-align: center;
  margin-bottom: 4rem;
}

.hero-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 2rem;
  }
}

.hero-text {
  font-size: 1.25rem;
  color: var(--text-light);
  margin-bottom: 2rem;
  max-width: 36rem;
  margin-left: auto;
  margin-right: auto;
}

@media (max-width: 768px) {
  .hero-text {
    font-size: 1.125rem;
  }
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  font-weight: 500;
  text-decoration: none;
  border-radius: 8px;
  border: none;
  cursor: pointer;
}

.btn-primary {
  background: var(--primary);
  border: 2px solid var(--primary);
  color: white;
  padding: 0.5rem 1rem;
}

.btn-primary:hover {
  background: var(--primary-hover);
  border-color: var(--primary-hover);
}

.btn-blue {
  background: var(--blue);
  color: white;
  padding: 0.5rem 1rem;
}

.btn-blue:hover {
  background: var(--blue-darker);
}

.btn-red {
  background: var(--red);
  color: white;
  padding: 0.5rem 1rem;
}

.btn-red:hover {
  background: var(--red-darker);
}

.btn-outline {
  background: white;
  border: 1px solid var(--primary);
  color: var(--primary);
  padding: 0.5rem 1rem;
}

.btn-outline:hover {
  background: rgba(39, 168, 68, 0.05); /* Primary with 0.05 opacity */
}

.btn-text {
  color: var(--text-light);
}

.btn-text:hover {
  color: var(--text);
}

.btn-lg {
  font-size: 1.125rem;
  padding: 0.75rem 1.5rem;
}

.btn-icon {
  margin-left: 0.5rem;
}

/* Feature cards */
.features {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin-bottom: 4rem;
}

@media (max-width: 768px) {
  .features {
    grid-template-columns: 1fr;
  }
}

.feature-card {
  padding: 1.5rem;
  border: 1px solid var(--border);
  border-radius: 0.75rem;
  transition: transform 0.3s, box-shadow 0.3s;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.feature-card .feature-icon {
  color: var(--primary);
  width: 3rem;
  height: 3rem;
  margin-bottom: 1rem;
}

.feature-card .feature-heading {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text);
}

.feature-card .feature-text {
  color: var(--text-light);
}

/* Features Grid (2-column) */
.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
  margin-bottom: 4rem;
}

@media (max-width: 768px) {
  .features-grid {
    grid-template-columns: 1fr;
  }
}

/* Section titles */
.section-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 2rem;
  text-align: center;
}

/* How it works section */
.how-it-works {
  background-color: var(--bg-light);
  padding: 2rem;
  border-radius: 0.5rem;
  margin-bottom: 4rem;
}

.how-it-works-title {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 1.5rem;
  text-align: center;
}

.how-it-works-steps {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.how-it-works-step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

@media (max-width: 768px) {
  .how-it-works-step {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
}

.how-it-works-icon {
  flex-shrink: 0;
  width: 2rem;
  height: 2rem;
  background-color: var(--primary);
  color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.how-it-works-step-title {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.how-it-works-description {
  color: var(--text-light);
}

/* CTA sections */
.cta {
  text-align: center;
  padding: 4rem 0;
  background-color: var(--bg-light);
  border-radius: 0.5rem;
  margin-bottom: 2rem;
}

.cta-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.cta-subtitle {
  color: var(--text-light);
  margin-bottom: 2rem;
}

.cta-alt-link {
  margin-top: 1rem;
}

.cta-alt-link a {
  color: var(--text-light);
  text-decoration: underline;
}

.cta-alt-link a:hover {
  color: var(--primary);
}

/* FAQ sections */
.faq-section {
  margin-bottom: 4rem;
}

.faq-section h2 {
  text-align: center;
  margin-bottom: 2rem;
  font-size: 1.5rem;
}

.faq-item {
  margin-bottom: 1rem;
  border: 1px solid var(--border);
  border-radius: 0.5rem;
  overflow: hidden;
}

.faq-question {
  padding: 1rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.faq-question::after {
  content: '+';
  position: absolute;
  right: 1.25rem;
  transition: transform 0.3s;
}

details[open] .faq-question::after {
  content: '−';
}

.faq-answer {
  padding: 0 1rem 1rem;
  color: var(--text-light);
}

/* =============================== */
/* Page-Specific Styles */
/* =============================== */

/* === Features Page === */
.features-hero {
  background-color: #f1f9ff; /* Note: Color not in variables */
  border-radius: 12px;
  padding: 3rem 2rem;
  margin-bottom: 2rem;
  position: relative;
}

.features-banner {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: var(--blue);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  font-weight: 600;
  font-size: 0.85rem;
}

.features-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  border-bottom: 1px solid var(--border);
  padding-bottom: 1rem;
}

@media (max-width: 768px) {
  .features-tabs {
    flex-wrap: wrap;
  }
}

.features-tab {
  background: none;
  border: 1px solid var(--border);
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.features-tab.active {
  background: var(--blue);
  color: white;
  border-color: var(--blue);
}

.features-tab:hover:not(.active) {
  background: var(--bg-light);
}

.features-tab-content {
  display: none;
}

.features-tab-content.active {
  display: block;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.feature-card-plus {
  border-color: var(--blue);
}

.feature-card-plus .plus-icon { /* Assuming .plus-icon exists */
  color: var(--blue);
}

.feature-card-premium {
  border-color: var(--purple);
}

.feature-card-premium .premium-icon { /* Assuming .premium-icon exists */
  color: var(--purple);
}

.features-comparison {
  margin: 4rem 0;
}

.table-container {
  overflow-x: auto;
}

/* === Mobile Page === */
.mobile-banner {
  background: linear-gradient(to right, var(--primary), var(--primary-hover));
  height: 8px;
  width: 100%;
  position: relative;
}

.mobile-badge {
  position: absolute;
  top: 8px;
  left: 1rem;
  background: var(--primary);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  font-weight: 600;
  font-size: 0.85rem;
  /* Duplicate definition, using earlier one */
  /* background: var(--purple); */
  /* display: inline-block; */
  /* margin-bottom: 1rem; */
}

.mobile-hero {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 4rem;
}

@media (max-width: 768px) {
  .mobile-hero {
    flex-direction: column;
  }
}

.mobile-hero-content {
  flex: 1;
}

.mobile-hero-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

@media (max-width: 768px) {
  .mobile-hero-title {
    font-size: 2rem;
  }
}

.mobile-hero-text {
  font-size: 1.25rem;
  color: var(--text-light);
  margin-bottom: 2rem;
}

@media (max-width: 768px) {
  .mobile-hero-text {
    font-size: 1.125rem;
  }
}

.mobile-hero-image {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.phone-mockup {
  width: 220px;
  height: 440px;
  background: #111; /* Note: Color not in variables */
  border-radius: 30px;
  padding: 10px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  position: relative;
}

.phone-screen {
  background: white;
  width: 100%;
  height: 100%;
  border-radius: 20px;
  overflow: hidden;
}

.mock-interface {
  padding: 1rem;
}

.mock-header {
  height: 40px;
  background: var(--primary);
  border-radius: 8px;
  margin-bottom: 1rem;
}

.mock-button {
  height: 50px;
  background: var(--bg-light);
  border-radius: 8px;
  margin-bottom: 1rem;
}

.mock-text {
  height: 20px;
  background: var(--bg-light);
  border-radius: 4px;
  margin-bottom: 1rem;
  width: 70%;
}

.mobile-section-title {
  text-align: center;
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 2rem;
  color: var(--text);
}

.mobile-steps {
  margin-bottom: 4rem;
}

.step-timeline {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  position: relative;
}

.step-timeline::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 25px;
  width: 2px;
  background-color: var(--primary);
  z-index: 0;
}

.step-item {
  display: flex;
  gap: 1.5rem;
  position: relative;
  z-index: 1;
}

.step-number {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.step-title {
  font-weight: 600;
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
}

.step-description {
  color: var(--text-light);
}

.mobile-advantages {
  margin-bottom: 4rem;
}

.advantages-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

@media (max-width: 768px) {
  .advantages-grid {
    grid-template-columns: 1fr;
  }
}

.advantage-card {
  padding: 1.5rem;
  border: 1px solid var(--border);
  border-radius: 12px;
  transition: transform 0.3s, box-shadow 0.3s;
}

.advantage-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.advantage-icon {
  color: var(--primary);
  width: 2.5rem;
  height: 2.5rem;
  margin-bottom: 1rem;
}

.advantage-title {
  font-weight: 600;
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
}

.advantage-text {
  color: var(--text-light);
  font-size: 0.9rem;
}

.mobile-faq {
  margin-bottom: 4rem;
}

.mobile-accordion {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.mobile-accordion-item {
  border: 1px solid var(--border);
  border-radius: 8px;
  overflow: hidden;
}

.mobile-accordion-header {
  padding: 1.25rem;
  font-weight: 600;
  cursor: pointer;
  position: relative;
}

.mobile-accordion-header::after {
  content: '+';
  position: absolute;
  right: 1.25rem;
  transition: transform 0.3s;
}

.mobile-accordion-item[open] .mobile-accordion-header::after {
  content: '−';
}

.mobile-accordion-content {
  padding: 0 1.25rem 1.25rem;
  color: var(--text-light);
}

.mobile-cta {
  margin-bottom: 4rem;
}

.mobile-cta-card {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-hover) 100%);
  color: white;
  padding: 3rem 2rem;
  border-radius: 12px;
  text-align: center;
}

.mobile-cta-title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.mobile-cta-text {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

/* === Pricing Page === */
.pricing-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin-bottom: 4rem;
}

@media (max-width: 768px) {
  .pricing-grid {
    grid-template-columns: 1fr;
  }
}

.pricing-card {
  border: 1px solid var(--border);
  border-radius: 0.75rem;
  padding: 1.5rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: transform 0.3s, box-shadow 0.3s;
}

.pricing-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.pricing-card.featured {
  border: 2px solid var(--primary);
  box-shadow: 0 4px 12px rgba(39, 168, 68, 0.15); /* Primary with 0.15 opacity */
  position: relative;
}

.pricing-badge {
  position: absolute;
  top: -12px;
  right: 20px;
  background: #FFD700; /* Note: Color not in variables */
  color: var(--text);
  font-size: 0.8rem;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
}

.pricing-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.pricing-subtitle {
  color: var(--text-light);
  margin-bottom: 1.5rem;
}

.pricing-price {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.pricing-period {
  color: var(--text-lighter);
  font-size: 0.9rem;
  margin-bottom: 1.5rem;
}

.pricing-annual {
  font-size: 0.9rem;
  color: var(--primary-hover);
  font-weight: 600;
  margin-bottom: 1.5rem;
}

.pricing-features {
  flex-grow: 1;
  margin-bottom: 1.5rem;
}

.pricing-feature {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0.75rem;
  font-size: 0.95rem;
}

.pricing-feature-icon {
  flex-shrink: 0;
  color: var(--primary);
  margin-right: 0.5rem;
}

.pricing-feature-text {
  color: var(--text);
}

.pricing-feature.dim {
  opacity: 0.6;
}

.pricing-cta {
  margin-top: auto;
}

.comparison-table {
  width: 100%;
  border-collapse: collapse;
  margin: 2rem 0;
}

.comparison-table th,
.comparison-table td {
  padding: 0.75rem;
  text-align: center;
  border-bottom: 1px solid var(--border);
}

.comparison-table th {
  font-weight: 600;
  background: var(--bg-light);
}

.comparison-table tr:hover {
  background: var(--bg-light);
}

.comparison-table td:first-child {
  text-align: left;
  font-weight: 500;
}

.check-icon {
  color: var(--primary);
}

.faq-pricing {
  max-width: 800px;
  margin: 0 auto 4rem;
}

/* =============================== */
/* Form Styles */
/* =============================== */
.form-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.form-group {
  margin: 5px;
}

.styled-form {
  width: 370px;
  max-width: 100%;
  display: flex;
  flex-direction: column;
  text-align: center;
}

input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="tel"],
input[type="url"],
input[type="search"],
input[type="date"],
input[type="time"],
textarea,
select {
  width: 100%;
  padding: 0.75rem;
  font-size: 1rem;
  border: 1px solid var(--border);
  border-radius: 8px;
  transition: border-color 0.2s, box-shadow 0.2s;
  background-color: white;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="number"]:focus,
input[type="tel"]:focus,
input[type="url"]:focus,
input[type="search"]:focus,
input[type="date"]:focus,
input[type="time"]:focus,
textarea:focus,
select:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(39, 168, 68, 0.2); 
  outline: none;
}

input[type="text"]::placeholder,
input[type="email"]::placeholder,
input[type="password"]::placeholder,
input[type="number"]::placeholder,
input[type="tel"]::placeholder,
input[type="url"]::placeholder,
input[type="search"]::placeholder,
input[type="date"]::placeholder,
input[type="time"]::placeholder,
textarea::placeholder,
select::placeholder {
  color: var(--text-muted);
}

input[type="text"]:disabled,
input[type="email"]:disabled,
input[type="password"]:disabled,
input[type="number"]:disabled,
input[type="tel"]:disabled,
input[type="url"]:disabled,
input[type="search"]:disabled,
input[type="date"]:disabled,
input[type="time"]:disabled,
textarea:disabled,
select:disabled {
  background-color: var(--neutral-light);
  cursor: not-allowed;
}

textarea {
  /* Resetting styles for textarea specifically */
  all: initial;
  font-family: "Inter", sans-serif !important; 
  box-sizing: border-box !important;
  display: block !important;
  width: 100% !important;
  min-height: 100px !important;
  padding: 0.75rem !important;
  border: 1px solid var(--border) !important;
  border-radius: 8px !important;
  color: black !important;
  resize: vertical !important;
  /* Apply focus styles from above */
  transition: border-color 0.2s, box-shadow 0.2s;
}
textarea:focus {
  border-color: var(--primary) !important;
  box-shadow: 0 0 0 2px rgba(39, 168, 68, 0.2) !important; 
  outline: none !important;
}
textarea::placeholder {
   color: var(--text-muted) !important;
}
textarea:disabled {
  background-color: var(--neutral-light) !important;
  cursor: not-allowed !important;
}


select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%23666'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 1rem;
  padding-right: 2.5rem;
}

[type="submit"],
button[type="submit"] {
  padding: 0.875rem;
  background: var(--primary);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
}

[type="submit"]:hover,
button[type="submit"]:hover {
  background: var(--primary-darker);
}

[type="submit"]:disabled,
button[type="submit"]:disabled {
  opacity: 0.65;
  cursor: not-allowed;
}

.actions {
  display: flex;
  justify-content: flex-end;
}

/* Password visibility toggle styles */
.pwd-input-wrapper {
  position: relative;
  width: 100%;
}

.pwd-input {
  width: 100%;
  padding-right: 3.5rem;
  box-sizing: border-box;
}

.pwd-field {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 1rem;
  background: transparent;
  border: none;
  color: var(--text-muted);
  font-size: 0.9em;
  cursor: pointer;
}

/* Added from near end of file */
.advantage-section {
  margin-bottom: 4rem;
}

.newsletter-link {
  display: inline-block;
  padding: 0.5rem 1rem;
  background-color: #4a86e8; /* Note: Color not in variables */
  color: white;
  border-radius: 4px;
  text-decoration: none;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-container {
  background-color: white;
  border-radius: 4px;
  width: 90%;
  max-width: 400px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.modal-header {
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eee; /* Note: Color not in variables */
}

.modal-body {
  padding: 1rem;
}

.modal-footer {
  padding: 1rem;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #eee; /* Note: Color not in variables */
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
}

.input-field {
  width: 100%;
  padding: 0.5rem;
  margin-bottom: 0.5rem;
  border: 1px solid #ddd; /* Note: Color not in variables */
  border-radius: 4px;
}

.error-text {
  color: red; /* Note: Color not in variables */
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

body.modal-open {
  overflow: hidden;
}

.consent-group {
  padding: 1rem;
}

/* =============================== */
/* Utility Classes */
/* =============================== */
.text-primary { color: var(--primary); }
.text-muted { color: var(--text-muted); }
.bg-white { background: white; }
.shadow { box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
.rounded { border-radius: 4px; }
.center { text-align: center; }
.right { text-align: right; }
.my-1 { margin-top: 10px; margin-bottom: 10px; }
.w100 { width: 100%; }
.col { flex-direction: column; }

.beta-box {
  display: inline-block;
}
.beta-tag {
  display: inline-block;
  top: -5px; /* Adjust as needed */
  right: -21px; /* Adjust as needed */
  color: var(--blue);
  font-size: 11px;
  padding: 0 1px;
  font-weight: bold;
  text-transform: lowercase;
  /* Removed position: relative/absolute as beta-box is inline-block */
}
