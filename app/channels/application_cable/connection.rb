module ApplicationCable
  class Connection < ActionCable::Connection::Base
    identified_by :current_user, :current_tenant

    def connect
      self.current_user = find_verified_user
      self.current_tenant = set_tenant_from_jwt
      logger.add_tags 'ActionCable', current_user.email, "Company:#{current_tenant&.id}"
    end

    private

    def find_verified_user
      if verified_user = authenticate_from_jwt
        verified_user
      else
        reject_unauthorized_connection
      end
    end

    def authenticate_from_jwt
      token = extract_jwt_token
      return nil unless token

      begin
        # Decode the JWT token
        payload = JwtService.decode(token)
        return nil unless payload

        # Check if token is revoked
        if JwtRevocationStrategy.new.jwt_revoked?(payload, nil)
          logger.error "[JWT] Action Cable: Revoked token attempted"
          return nil
        end

        # Find the user
        user = User.find_by(id: payload['user_id'])
        
        if user
          logger.info "[JWT] Action Cable: Authenticated user #{user.email}"
          @jwt_payload = payload  # Store for tenant extraction
          user
        else
          logger.error "[JWT] Action Cable: User not found for ID #{payload['user_id']}"
          nil
        end
      rescue JWT::ExpiredSignature
        logger.error "[JWT] Action Cable: Expired token"
        nil
      rescue JWT::DecodeError => e
        logger.error "[JWT] Action Cable: Decode error - #{e.message}"
        nil
      # TODO (Future Enhancement): Refine exception handling for better observability
      # - Catch specific exceptions (JWT::DecodeError, JWT::ExpiredSignature, ActiveRecord::RecordNotFound)
      # - Allow unforeseen errors to fail loudly for external error monitoring
      # - Distinguish between expected auth failures and actual bugs
      rescue StandardError => e
        logger.error "[JWT] Action Cable: Authentication error - #{e.message}"
        nil
      end
    end

    def extract_jwt_token
      # Try multiple sources for the JWT token
      
      # 1. From query parameters (preferred for WebSocket connections)
      token = request.params[:token]
      if token.present?
        logger.info "[JWT] Action Cable: Token found in query params"
        return token
      end

      # 2. From Authorization header (if supported by the WebSocket implementation)
      auth_header = request.headers['Authorization']
      if auth_header.present? && auth_header.start_with?('Bearer ')
        token = auth_header.split(' ').last
        logger.info "[JWT] Action Cable: Token found in Authorization header"
        return token
      end

      # 3. From a custom header (alternative approach)
      token = request.headers['X-Auth-Token']
      if token.present?
        logger.info "[JWT] Action Cable: Token found in X-Auth-Token header"
        return token
      end

      logger.warn "[JWT] Action Cable: No token found in request"
      nil
    end

    def set_tenant_from_jwt
      return nil unless @jwt_payload && @jwt_payload['company_id']

      begin
        company = Company.find_by(id: @jwt_payload['company_id'])
        
        if company && current_user.companies.include?(company)
          ActsAsTenant.current_tenant = company
          logger.info "[JWT] Action Cable: Set tenant to Company ##{company.id}"
          company
        else
          logger.warn "[JWT] Action Cable: Invalid company_id #{@jwt_payload['company_id']} for user #{current_user.id}"
          nil
        end
      rescue StandardError => e
        logger.error "[JWT] Action Cable: Error setting tenant - #{e.message}"
        nil
      end
    end
  end
end