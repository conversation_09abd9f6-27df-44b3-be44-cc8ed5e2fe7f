class NotificationChannel < ApplicationCable::Channel
  def subscribed
    # Stream notifications for the current user
    stream_for current_user
    
    # If tenant is set, also stream company-wide notifications
    if current_tenant
      stream_for current_tenant
    end
    
    logger.info "[NotificationChannel] User #{current_user.email} subscribed to notifications"
  end

  def unsubscribed
    logger.info "[NotificationChannel] User #{current_user.email} unsubscribed from notifications"
  end

  def mark_as_read(data)
    notification = current_user.notifications.find_by(id: data['notification_id'])
    
    if notification
      notification.update(read: true)
      # Broadcast the update back to the user
      NotificationChannel.broadcast_to(
        current_user,
        {
          type: 'notification_read',
          notification_id: notification.id
        }
      )
    end
  end
end