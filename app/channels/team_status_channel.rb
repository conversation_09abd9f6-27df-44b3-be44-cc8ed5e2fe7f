# ABOUTME: Real-time team status channel for broadcasting employee work activity updates
# ABOUTME: Provides company-scoped streaming for managers to see live team status changes

class TeamStatusChannel < ApplicationCable::Channel
  def subscribed
    # Company-scoped streaming for managers to see team status updates
    if current_tenant && can_view_team_status?
      stream_for current_tenant
      logger.info "[TeamStatusChannel] User #{current_user.email} subscribed to team status for company #{current_tenant.name}"
    else
      reject
      logger.warn "[TeamStatusChannel] User #{current_user.email} rejected - insufficient permissions or no tenant"
    end
  end

  def unsubscribed
    logger.info "[TeamStatusChannel] User #{current_user.email} unsubscribed from team status"
  end

  private

  def can_view_team_status?
    # Allow all employees and managers to see team status updates
    # This enables real-time collaboration between all team members
    # - managers can see all employees' status
    # - employees can see other employees' status  
    # - employees can see managers' status
    # User just needs to be part of the company
    current_user.contracts.exists?(company: current_tenant) ||
    current_user.has_role?(:owner, current_tenant) ||
    current_user.has_role?(:supervisor, current_tenant) ||
    current_user.has_role?(:admin, current_tenant)
  end
end