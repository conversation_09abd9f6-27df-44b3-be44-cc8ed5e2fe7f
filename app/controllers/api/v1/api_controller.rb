module Api
  module V1
    class ApiController < ApplicationController
      include JwtAuthenticatable
      
      # SECURITY: Handle authorization failures with proper responses
      # Added in Chunk 29 for enhanced security
      rescue_from ActionPolicy::Unauthorized, with: :handle_authorization_error
      
      # skip_before_action :verify_authenticity_token
      # CSRF protection is disabled globally in ApplicationController via `skip_forgery_protection`.
      # The redundant `skip_before_action :verify_authenticity_token` was removed from here
      # as it was unnecessary and caused errors during Rails' eager loading process.
      
      # Skip ApplicationController's session-based authentication
      skip_before_action :require_login
      
      # Use our own dual authentication
      before_action :authenticate_user!
      # Set @company for convenience after authentication sets tenant
      before_action :set_tenant_company
      
      # API responses should be JSON
      respond_to :json
      
      # Add around_action to log API requests
      around_action :log_api_request
      # Track authentication method used
      after_action :track_auth_method
      
      # TODO: (FUTURE ENHANCEMENT - LOW PRIORITY) Consider centralizing contract requirement 
      # enforcement across controllers to ensure consistency and avoid duplication as more 
      # controllers are refactored.
      # Target implementation: Post-JWT-migration Phase 8+ (Final Touches)
      # See: docs/jwt_implementation_notes_chunk_19_final.md - "Potential Future Enhancements #2"
      
      # TODO: (FUTURE ENHANCEMENT - LOW PRIORITY) Explore standardizing error payloads 
      # for different types of failures (validation errors, authorization errors, business 
      # logic errors) across API endpoints.
      # Target implementation: Post-JWT-migration Phase 8+ (Final Touches)
      # See: docs/jwt_implementation_notes_chunk_19_final.md - "Potential Future Enhancements #3"
      
      # CHUNK 46: The `current_user` method is now exclusively JWT-based.
      # The `|| super` fallback to Devise's session-based `current_user` has been
      # removed to enforce a strict JWT-only context. All API authentication
      # now relies solely on the `@current_jwt_user` established via the JWT token.
      def current_user
        @current_user ||= @current_jwt_user
      end

      # SECURITY: Provide JWT context to authorization policies
      # Added in Chunk 29 for enhanced tenant validation
      def authorization_context
        super.merge(
          current_jwt_user: @current_jwt_user,
          jwt_company_id: @jwt_company_id
        )
      end
      
      private
      
      # CHUNK 46: JWT-only authentication - session fallback removed
      # All API requests now require JWT authentication exclusively
      def authenticate_user!
        # Try JWT authentication (non-destructive - doesn't render on failure)
        authenticate_user_from_jwt_silent
        
        if @current_jwt_user
          # JWT authentication successful
          Rails.logger.info "API authenticated via JWT for user: #{@current_jwt_user.id}"
          return
        end
        
        # No valid JWT provided - authentication failed
        Rails.logger.info "API authentication failed: No valid JWT provided"
        render_unauthorized("JWT authentication required")
      end
      
      # Silent JWT authentication that doesn't render on failure
      def authenticate_user_from_jwt_silent
        token = extract_jwt_from_header || extract_jwt_from_session_cookie
        return unless token
        
        payload = decode_and_validate_token(token)
        return unless payload
        
        user = find_user_from_payload(payload)
        return unless user
        
        # Check if token has been revoked
        return if token_revoked?(payload, user)
        
        @current_jwt_user = user
        
        # Extract and set tenant (company) context from JWT
        unless set_tenant_from_jwt(payload, user)
          # Invalid tenant context - clear the JWT user
          @current_jwt_user = nil
          Rails.logger.warn "JWT authentication failed silently: Invalid tenant context"
          return
        end
        
        AuthHealthCheck.log_auth_event('jwt_authentication', success: true)
      rescue => e
        # Enhanced error logging with partial backtrace for debugging
        backtrace_snippet = e.backtrace&.first(3)&.join("\n  ")
        Rails.logger.error "JWT authentication error: #{e.class.name} - #{e.message}\n  Backtrace:\n  #{backtrace_snippet}"
        nil
      end
      
      def render_json_error(message, status = :unprocessable_entity)
        render json: { error: message }, status: status
      end
      
      def render_json_success(data = {})
        render json: { success: true }.merge(data)
      end

      # SECURITY: Handle ActionPolicy::Unauthorized exceptions
      # Added in Chunk 29 for enhanced tenant isolation security
      def handle_authorization_error(exception)
        # Log security event for authorization failure
        AuthHealthCheck.log_security_event('authorization_denied', {
          severity: 'medium',
          user_id: current_user&.id,
          controller: controller_name,
          action: action_name,
          policy: exception.policy.class.name,
          rule: exception.rule,
          record_class: exception.record.class.name,
          record_id: exception.record.respond_to?(:id) ? exception.record.id : 'collection',
          jwt_authenticated: @current_jwt_user.present?,
          current_tenant_id: ActsAsTenant.current_tenant&.id,
          jwt_company_id: @jwt_company_id
        })

        # Return 403 Forbidden for authorization failures
        # Using 403 instead of 404 for transparency (user is authenticated but not authorized)
        render json: { 
          error: 'Access denied',
          message: 'You do not have permission to access this resource'
        }, status: :forbidden
      end
      
      def log_api_request
        start_time = Time.current
        error = nil
        success = true
        
        begin
          yield
        rescue => e
          error = e.message
          success = false
          raise
        ensure
          # Log the API request
          duration_ms = ((Time.current - start_time) * 1000).round
          AuthHealthCheck.log_auth_event(
            'api_request', 
            success: success && response.successful?,
            duration_ms: duration_ms,
            error: error || (response.server_error? ? "Server error: #{response.status}" : nil)
          )
        end
      end
      
      # Set @company instance variable for convenience
      # This centralizes the logic that was repeated in multiple controllers
      def set_tenant_company
        @company = ActsAsTenant.current_tenant
      end
      
      # Track JWT authentication method
      # TODO: Consolidate duplicated JWT authentication logic with ApplicationController into JwtAuthenticatable concern
      def track_auth_method
        return unless current_user # Only track authenticated requests
        
        if @current_jwt_user
          # Request was authenticated via JWT
          AuthHealthCheck.increment_jwt_request_count
          Rails.logger.debug "[AuthMetrics] JWT request tracked for user: #{@current_jwt_user.id}"
        end
      rescue => e
        # Don't let metrics tracking break the request
        Rails.logger.error "[AuthMetrics] Error tracking auth method: #{e.message}"
      end
    end
  end
end