module Api
  module V1
    class AuthController < ApiController
      include JwtLoginHelpers
      
      # Skip the ApiController's authentication for auth endpoints
      skip_before_action :authenticate_user!, only: [:login, :jwt_login, :jwt_logout, :refresh_token, :restore_session, :jwt_register, :request_password_reset, :password_reset, :confirm_email, :resend_confirmation, :accept_invitation, :invitation_details]
      skip_before_action :set_current_tenant, only: [:login, :jwt_login, :jwt_logout, :refresh_token, :restore_session, :jwt_register, :request_password_reset, :password_reset, :confirm_email, :resend_confirmation, :accept_invitation, :invitation_details]
      
      def login
        start_time = Time.current
        user = User.find_by(email: params[:email])
        
        if user&.valid_password?(params[:password])
          # For now, we'll still use session-based auth
          # JWT implementation can be added later if needed
          sign_in(user)
          
          # Log successful login
          duration_ms = ((Time.current - start_time) * 1000).round
          AuthHealthCheck.log_auth_event('login', success: true, duration_ms: duration_ms)
          
          render json: {
            success: true,
            user: {
              email: user.email,
              role: user.company_user_roles.find_by(company: current_tenant)&.role&.name || 'employee',
              roles: user.company_user_roles.where(company: current_tenant).joins(:role).pluck('roles.name'),
              permissions: user_permissions(user)
            }
          }
        else
          # Log failed login
          duration_ms = ((Time.current - start_time) * 1000).round
          AuthHealthCheck.log_auth_event('login', success: false, duration_ms: duration_ms, error: 'Invalid credentials')
          
          render json: { error: I18n.t('front.invalid_credentials') }, status: :unauthorized
        end
      end
      
      def logout
        sign_out(current_user) if user_signed_in?
        render json: { success: true }
      end
      
      # JWT Login endpoint - operates alongside session login
      def jwt_login
        start_time = Time.current
        user = User.find_by(email: params[:email])
        
        if user&.valid_password?(params[:password])
          # CHUNK 57: Check if email is confirmed OR user accepted invitation before allowing login
          unless user.confirmed_at.present? || user.invitation_accepted_at.present?
            duration_ms = ((Time.current - start_time) * 1000).round
            AuthHealthCheck.log_auth_event('jwt_login', success: false, duration_ms: duration_ms, error: 'Email not confirmed')
            
            render json: { 
              error: I18n.t('front.email_not_confirmed'),
              email_not_confirmed: true,
              email: user.email
            }, status: :forbidden
            return
          end
          
          handle_successful_jwt_login(user, 
            event_name: 'jwt_login', 
            start_time: start_time
          )
        else
          # Log failed JWT login
          duration_ms = ((Time.current - start_time) * 1000).round
          AuthHealthCheck.log_auth_event('jwt_login', success: false, duration_ms: duration_ms, error: 'Invalid credentials')
          
          render json: { error: I18n.t('front.invalid_credentials') }, status: :unauthorized
        end
      end
      
      # JWT Logout endpoint - revokes the provided token
      # SECURITY FIX (Chunk 37 Follow-up): Simplified token revocation with single user resolution 
      # to prevent potential security issues from user ID divergence between tokens.
      # 
      # Key improvements:
      # - Single user resolution instead of redundant lookups
      # - User ID consistency validation between tokens
      # - Enhanced security audit logging with IP/User-Agent tracking
      # - Single JwtRevocationStrategy instance for efficiency
      # 
      # See: docs/jwt_logout_security_fix_chunk_37.md for complete implementation details
      def jwt_logout
        user = nil
        access_token_payload = nil
        refresh_token_payload = nil
        access_token_revoked = false
        refresh_token_revoked = false
        
        begin
          # Extract and decode access token from Authorization header
          auth_header = request.headers['Authorization']
          if auth_header.present? && auth_header.start_with?('Bearer ')
            token = auth_header.split(' ').last
            access_token_payload = JwtService.decode(token)
          end
          
          # Extract and decode refresh token from HttpOnly cookie
          refresh_token = SecureCookieHelper.get_refresh_token_cookie(cookies)
          if refresh_token.present?
            refresh_token_payload = JwtService.decode(refresh_token)
          end
          
          # Single user resolution: prefer access token, fallback to refresh token
          primary_payload = access_token_payload || refresh_token_payload
          if primary_payload
            user = User.find_by(id: primary_payload['user_id'])
            
            # Security audit log: record logout attempt details
            Rails.logger.info "[SECURITY] JWT logout attempt - User: #{primary_payload['user_id']}, " \
                             "IP: #{request.remote_ip}, User-Agent: #{request.user_agent}"
          end
          
          # Proceed with token revocation only if we have a valid user
          if user
            strategy = JwtRevocationStrategy.new
            
            # Revoke access token if present
            if access_token_payload
              # Validate user ID consistency for security
              if access_token_payload['user_id'] != user.id
                Rails.logger.error "[SECURITY] User ID mismatch in access token during logout - " \
                                  "Expected: #{user.id}, Found: #{access_token_payload['user_id']}"
              else
                strategy.revoke_jwt(access_token_payload, user)
                access_token_revoked = true
                Rails.logger.info "[SECURITY] Access token revoked for user #{user.id}"
              end
            end
            
            # Revoke refresh token if present
            if refresh_token_payload
              # Validate user ID consistency for security
              if refresh_token_payload['user_id'] != user.id
                Rails.logger.error "[SECURITY] User ID mismatch in refresh token during logout - " \
                                  "Expected: #{user.id}, Found: #{refresh_token_payload['user_id']}"
              else
                strategy.revoke_jwt(refresh_token_payload, user)
                refresh_token_revoked = true
                Rails.logger.info "[SECURITY] Refresh token revoked for user #{user.id}"
              end
            end
          else
            # Log security event for logout attempt without valid user context
            Rails.logger.warn "[SECURITY] JWT logout attempt without valid user context - " \
                             "IP: #{request.remote_ip}, User-Agent: #{request.user_agent}"
          end
          
        rescue => e
          # Enhanced error logging with security context
          Rails.logger.error "[SECURITY] Error during token revocation in logout: #{e.class.name} - #{e.message} - " \
                            "User: #{user&.id || 'unknown'}, IP: #{request.remote_ip}"
        ensure
          # Always clear the refresh token cookie, regardless of revocation success
          begin
            SecureCookieHelper.delete_refresh_token_cookie(cookies)
            Rails.logger.info "[SECURITY] Refresh token cookie cleared"
          rescue => e
            Rails.logger.error "[SECURITY] Failed to clear refresh token cookie: #{e.message}"
          end
          
          # CHUNK 36.5: Destroy Redis session and clear session cookie
          begin
            session_cookie = cookies.signed[:jwt_session_id]
            if session_cookie
              # Use helper to parse cookie consistently
              user_id, session_id = parse_jwt_session_cookie(session_cookie)
              # Use resolved user from token validation for consistency
              resolved_user_id = user&.id || user_id || current_user&.id
              
              if session_id && resolved_user_id
                JwtSessionService.destroy_session(resolved_user_id, session_id)
                Rails.logger.info "[SECURITY] Redis JWT session destroyed for user #{resolved_user_id}"
              end
            end
            
            # Clear session cookie (use same parameters as when cookie was set)
            cookies.delete(:jwt_session_id)
          rescue => e
            Rails.logger.error "[SECURITY] Failed to clear JWT session: #{e.message}"
          end
        end
        
        # Enhanced logout logging with security audit trail
        logout_success = access_token_revoked || refresh_token_revoked
        if logout_success
          AuthHealthCheck.log_auth_event('jwt_logout', success: true)
          Rails.logger.info "[SECURITY] JWT logout completed successfully - User: #{user&.id || 'unknown'}, " \
                           "Tokens revoked: access=#{access_token_revoked}, refresh=#{refresh_token_revoked}"
        else
          # Log unsuccessful logout attempts for security monitoring
          Rails.logger.warn "[SECURITY] JWT logout completed without token revocation - " \
                           "IP: #{request.remote_ip}, User-Agent: #{request.user_agent}"
        end
        
        # Always return success - security best practice
        # Don't reveal whether token was valid or not
        render json: { success: true }, status: :ok
      end
      
      # Refresh token endpoint - issues new access token using valid refresh token
      def refresh_token
        # Get refresh token from HttpOnly cookie (primary method)
        refresh_token = SecureCookieHelper.get_refresh_token_cookie(cookies)
        
        # JWT-Only Mode: Strict refresh token sourcing - only HttpOnly cookies allowed
        if FeatureFlags.jwt_only_mode_enabled?
          # In JWT-only mode, ONLY accept refresh tokens from HttpOnly cookies
          if refresh_token.blank?
            Rails.logger.warn "JWT-only mode: Refresh token request without HttpOnly cookie"
            render json: { error: I18n.t('front.missing_cookie') }, status: :unauthorized
            return
          end
        else
          # Dual-auth mode: Allow fallback mechanisms during transition period
          # TODO: (REMOVAL PLANNED - Chunk 37 Implementation) Remove these fallback mechanisms 
          # when JWT_ONLY_MODE_ENABLED becomes permanently true and dual-auth transition is complete.
          # Target removal: Phase 6 (Final JWT Migration & Session Removal)
          # See: docs/jwt_implementation_notes_chunk_37.md for JWT-only mode details
          # Fallback: check request body for backward compatibility during transition
          if refresh_token.blank?
            refresh_token = params[:refresh_token]
          end
          
          # Final fallback: check Authorization header for backward compatibility
          if refresh_token.blank?
            auth_header = request.headers['Authorization']
            if auth_header.present? && auth_header.start_with?('Bearer ')
              refresh_token = auth_header.split(' ').last
            end
          end
        end
        
        if refresh_token.blank?
          render json: { error: I18n.t('front.required') }, status: :unauthorized
          return
        end
        
        # Decode the refresh token
        payload = JwtService.decode(refresh_token)
        
        if payload.nil?
          render json: { error: I18n.t('front.invalid_or_expired') }, status: :unauthorized
          return
        end
        
        # Enhanced refresh token validation
        unless JwtService.valid_for_rotation?(payload)
          Rails.logger.warn "Invalid refresh token for rotation: #{payload.inspect}"
          render json: { error: I18n.t('front.invalid_format') }, status: :unauthorized
          return
        end
        
        # Check if refresh token is revoked
        user = User.find_by(id: payload['user_id'])
        if user.nil?
          render json: { error: I18n.t('front.user_not_found') }, status: :unauthorized
          return
        end
        
        strategy = JwtRevocationStrategy.new
        if strategy.jwt_revoked?(payload, user)
          # SECURITY BREACH DETECTED: Token reuse indicates potential theft
          family_id = payload['refresh_family_id']
          Rails.logger.error "[SECURITY] Refresh token reuse detected - User: #{user.id}, Family: #{family_id}, IP: #{request.remote_ip}"
          
          # AGGRESSIVE SECURITY RESPONSE: Revoke all tokens in this family
          # This prevents attackers from using stolen tokens even if they win the race
          if family_id.present?
            JwtRevocationStrategy.revoke_family(family_id, user)
          end
          
          render json: { 
            error: I18n.t('front.revoked'),
            breach_detected: true 
          }, status: :unauthorized
          return
        end
        
        # Generate new tokens
        new_payload = user.jwt_payload
        new_access_token = JwtService.encode_access_token(new_payload)
        
        # Enhanced refresh token rotation for security
        # Revoke old refresh token
        strategy.revoke_jwt(payload, user)
        
        # Issue new refresh token with rotation tracking
        new_refresh_token = JwtService.rotate_refresh_token(payload, user)
        
        # Log rotation for security monitoring
        rotation_count = (payload['rotation_count'] || 0) + 1
        family_id = payload['refresh_family_id']
        Rails.logger.info "[SECURITY] Refresh token rotated - User: #{user.id}, Family: #{family_id}, Rotation: #{rotation_count}"
        refresh_expires_at = Time.current + JwtService::REFRESH_TOKEN_EXPIRATION_DURATION
        SecureCookieHelper.set_refresh_token_cookie(cookies, new_refresh_token, refresh_expires_at)
        
        # CHUNK 36.5: Update Redis session with new access token
        session_cookie = cookies.signed[:jwt_session_id]
        if session_cookie
          # Use helper to parse cookie consistently
          _, session_id = parse_jwt_session_cookie(session_cookie)
          
          if session_id
            JwtSessionService.update_session_token(user.id, session_id, new_access_token)
            Rails.logger.info "Redis JWT session updated with new access token"
          end
        end
        
        # Log successful token refresh
        AuthHealthCheck.log_auth_event('jwt_refresh', success: true)
        
        render json: {
          success: true,
          access_token: new_access_token,
          # NOTE: refresh_token no longer returned in response for security
          expires_in: JwtService::ACCESS_TOKEN_EXPIRATION_DURATION.to_i,
          user: {
            id: user.id,
            email: user.email,
            company_id: user.primary_company&.id
          }
        }
      rescue => e
        Rails.logger.error "Token refresh error: #{e.message}"
        render json: { error: I18n.t('front.failed') }, status: :unauthorized
      end
      
      # Restore JWT session - retrieves current access token from Redis session
      # Used during app initialization to restore JWT token to Vuex store
      def restore_session
        Rails.logger.info "[DEBUG] restore_session: Attempting to restore JWT token from session"
        
        # Get session cookie
        session_cookie = cookies.signed[:jwt_session_id]
        if session_cookie.blank?
          Rails.logger.info "[DEBUG] restore_session: No session cookie found"
          render json: { error: 'No session found' }, status: :unauthorized
          return
        end
        
        # Parse session cookie to get user_id and session_id
        user_id, session_id = parse_jwt_session_cookie(session_cookie)
        if user_id.blank? || session_id.blank?
          Rails.logger.info "[DEBUG] restore_session: Invalid session cookie format"
          render json: { error: 'Invalid session cookie' }, status: :unauthorized
          return
        end
        
        # Validate user exists
        user = User.find_by(id: user_id)
        if user.nil?
          Rails.logger.info "[DEBUG] restore_session: User not found for ID: #{user_id}"
          render json: { error: 'User not found' }, status: :unauthorized
          return
        end
        
        # Retrieve session from Redis with context validation
        validation_context = {
          ip_address: request.remote_ip,
          user_agent: request.user_agent
        }
        
        begin
          session_data = JwtSessionService.find_session(user_id, session_id, validate_context: validation_context)
          if session_data.nil?
            Rails.logger.info "[DEBUG] restore_session: No valid session found in Redis"
            render json: { error: 'Session not found or invalid' }, status: :unauthorized
            return
          end
          
          # Get access token from session
          access_token = session_data[:access_token]
          if access_token.blank?
            Rails.logger.info "[DEBUG] restore_session: No access token in session"
            render json: { error: 'No access token in session' }, status: :unauthorized
            return
          end
          
          # Validate the access token
          payload = JwtService.decode(access_token)
          if payload.nil?
            Rails.logger.info "[DEBUG] restore_session: Access token expired, attempting refresh"
            
            # Get refresh token from the session data
            refresh_token = session_data[:refresh_token]
            if refresh_token.blank?
              Rails.logger.info "[DEBUG] restore_session: No refresh token available"
              render json: { error: 'No refresh token available' }, status: :unauthorized
              return
            end
            
            # Use the existing refresh_token logic to get new tokens
            begin
              # Validate refresh token
              refresh_payload = JwtService.decode(refresh_token)
              if refresh_payload.nil?
                Rails.logger.info "[DEBUG] restore_session: Refresh token is invalid or expired"
                render json: { error: 'Invalid refresh token' }, status: :unauthorized
                return
              end
              
              # Check if refresh token is valid for rotation
              unless JwtService.valid_for_rotation?(refresh_payload)
                Rails.logger.warn "[DEBUG] restore_session: Refresh token not valid for rotation"
                render json: { error: 'Invalid refresh token' }, status: :unauthorized
                return
              end
              
              # Check if refresh token is revoked
              strategy = JwtRevocationStrategy.new
              if strategy.jwt_revoked?(refresh_payload, user)
                Rails.logger.error "[DEBUG] restore_session: Refresh token is revoked"
                render json: { error: 'Refresh token revoked' }, status: :unauthorized
                return
              end
              
              # Generate new access token
              new_payload = user.jwt_payload
              new_access_token = JwtService.encode_access_token(new_payload)
              
              # Update session with new access token
              JwtSessionService.update_session_token(user_id, session_id, new_access_token)
              
              # Use the new access token for the response
              access_token = new_access_token
              Rails.logger.info "[DEBUG] restore_session: Successfully refreshed expired token for user: #{user.id}"
              
              # Continue with the normal flow using the new access token
            rescue => e
              Rails.logger.error "[DEBUG] restore_session: Error refreshing token: #{e.message}"
              render json: { error: 'Failed to refresh token' }, status: :unauthorized
              return
            end
          end
          
          Rails.logger.info "[DEBUG] restore_session: Successfully restored JWT token for user: #{user.id}"
          
          # Return the access token and user info
          render json: {
            success: true,
            access_token: access_token,
            expires_in: JwtService::ACCESS_TOKEN_EXPIRATION_DURATION.to_i,
            user: {
              id: user.id,
              email: user.email,
              company_id: user.primary_company&.id
            }
          }
          
        rescue => e
          Rails.logger.error "[DEBUG] restore_session: Error retrieving session: #{e.message}"
          render json: { error: 'Failed to restore session' }, status: :unauthorized
        end
      end
      
      # JWT Registration endpoint - creates new user and returns JWT tokens
      # This enables complete JWT-only authentication flow including registration
      # CHUNK 57: Updated JWT registration with email confirmation requirement
      # Users must confirm their email before they can log in
      def jwt_register
        start_time = Time.current
        user = User.new(user_params)
        
        if user.save
          # Set up user workspace and profile (if your app has these)
          user.create_user_profile if user.respond_to?(:create_user_profile)
          
          # CHUNK 57: Send email confirmation instead of automatically logging in
          send_confirmation_email(user)
          
          # Log successful registration (but not login yet)
          duration_ms = ((Time.current - start_time) * 1000).round
          AuthHealthCheck.log_auth_event('jwt_register', success: true, duration_ms: duration_ms)
          
          render json: {
            success: true,
            message: I18n.t('front.registration_successful'),
            email: user.email
          }, status: :created
        else
          # Log failed JWT registration
          duration_ms = ((Time.current - start_time) * 1000).round
          AuthHealthCheck.log_auth_event('jwt_register', success: false, duration_ms: duration_ms, error: 'Validation failed')
          
          render json: {
            success: false,
            error: I18n.t('front.failed'),
            errors: user.errors
          }, status: :unprocessable_entity
        end
      rescue => e
        Rails.logger.error "JWT registration error: #{e.message}"
        render json: { error: I18n.t('front.failed') }, status: :internal_server_error
      end
      
      # CHUNK 56: Password reset request endpoint
      # Generates and sends password reset token via email
      def request_password_reset
        start_time = Time.current
        email = params[:email]
        
        unless email.present?
          render json: { 
            error: I18n.t('front.email_required')
          }, status: :unprocessable_entity
          return
        end
        
        user = User.find_by(email: email.downcase.strip)
        
        # Always return success for security (don't reveal if email exists)
        # This prevents email enumeration attacks
        if user
          # Generate secure reset token
          reset_token = SecureRandom.urlsafe_base64(32)
          reset_expires_at = 6.hours.from_now
          
          # Store reset token securely in Redis with expiration
          redis_key = "password_reset:#{user.id}"
          reset_data = {
            token: reset_token,
            expires_at: reset_expires_at.to_i,
            created_at: Time.current.to_i,
            user_id: user.id,
            email: user.email,
            ip_address: request.remote_ip,
            user_agent: request.user_agent
          }
          
          RedisService.set(:password_reset, [user.id], reset_data, ttl: 6.hours.to_i)
          
          # Store reverse lookup for efficient token validation  
          RedisService.set(:reset_token, [reset_token], { user_id: user.id }, ttl: 6.hours.to_i)
          
          # Send password reset email
          begin
            PasswordResetMailer.reset_instructions(user, reset_token).deliver_now
            Rails.logger.info "[SECURITY] Password reset email sent to user #{user.id} (#{email})"
            
            # Log successful password reset request
            duration_ms = ((Time.current - start_time) * 1000).round
            AuthHealthCheck.log_auth_event('password_reset_request', success: true, duration_ms: duration_ms)
          rescue => e
            Rails.logger.error "[ERROR] Failed to send password reset email: #{e.message}"
            # Still return success to prevent information disclosure
          end
        else
          Rails.logger.warn "[SECURITY] Password reset requested for non-existent email: #{email} from IP: #{request.remote_ip}"
          
          # Log failed password reset request (invalid email)
          duration_ms = ((Time.current - start_time) * 1000).round
          AuthHealthCheck.log_auth_event('password_reset_request', success: false, duration_ms: duration_ms, error: 'Email not found')
        end
        
        # Always return success message (security best practice)
        render json: {
          success: true,
          message: I18n.t('front.successful')
        }
      rescue => e
        Rails.logger.error "Password reset request error: #{e.message}"
        render json: { error: I18n.t('front.error') }, status: :internal_server_error
      end
      
      # CHUNK 56: Password reset confirmation endpoint  
      # This replaces the legacy Devise password reset flow.
      def password_reset
        start_time = Time.current
        
        # Extract password reset params
        token = params[:reset_password_token]
        password = params[:password]
        password_confirmation = params[:password_confirmation]
        
        # Validate required parameters
        unless token.present? && password.present? && password_confirmation.present?
          render json: { 
            error: I18n.t('front.missing_params'),
            details: {
              reset_password_token: token.blank? ? 'is required' : nil,
              password: password.blank? ? 'is required' : nil,
              password_confirmation: password_confirmation.blank? ? 'is required' : nil
            }.compact
          }, status: :unprocessable_entity
          return
        end
        
        # Validate and use custom JWT-based reset token
        user = validate_and_find_user_by_reset_token(token)
        
        unless user
          render json: { 
            error: I18n.t('front.invalid_or_expired_token')
          }, status: :unprocessable_entity
          return
        end
        
        # Update password using ActiveRecord validations
        user.password = password
        user.password_confirmation = password_confirmation
        
        if user.save
          # Password reset successful - clear token here after successful save
          clear_password_reset_token(user)
          
          # automatically log the user in via JWT
          handle_successful_jwt_login(user,
            event_name: 'password_reset',
            start_time: start_time,
            message: I18n.t('front.successful')
          )
        else
          # Password reset failed - return errors
          duration_ms = ((Time.current - start_time) * 1000).round
          AuthHealthCheck.log_auth_event('password_reset', success: false, duration_ms: duration_ms, error: 'Invalid token or password')
          
          # Format error messages
          error_messages = user.errors.full_messages
          
          render json: { 
            error: I18n.t('front.failed'),
            messages: error_messages
          }, status: :unprocessable_entity
        end
      rescue => e
        Rails.logger.error "Password reset error: #{e.message}"
        render json: { error: I18n.t('front.error') }, status: :internal_server_error
      end
      
      # Custom password change endpoint to replace Devise's broken password change
      # Requires JWT authentication
      def change_password
        start_time = Time.current
        
        # Extract password change params
        current_password = params[:current_password]
        new_password = params[:password]
        password_confirmation = params[:password_confirmation]
        
        # Validate required parameters
        unless current_password.present? && new_password.present? && password_confirmation.present?
          render json: { 
            error: I18n.t('front.missing_params'),
            details: {
              current_password: current_password.blank? ? 'is required' : nil,
              password: new_password.blank? ? 'is required' : nil,
              password_confirmation: password_confirmation.blank? ? 'is required' : nil
            }.compact
          }, status: :unprocessable_entity
          return
        end
        
        # Get current user from JWT authentication
        user = current_user
        unless user
          render json: { error: I18n.t('front.auth_required') }, status: :unauthorized
          return
        end
        
        # Verify current password
        unless user.valid_password?(current_password)
          duration_ms = ((Time.current - start_time) * 1000).round
          AuthHealthCheck.log_auth_event('password_change', success: false, duration_ms: duration_ms, error: 'Invalid current password')
          
          render json: { 
            error: I18n.t('front.incorrect_current_password')
          }, status: :unprocessable_entity
          return
        end
        
        # Check password confirmation matches
        unless new_password == password_confirmation
          render json: { 
            error: I18n.t('front.password_mismatch')
          }, status: :unprocessable_entity
          return
        end
        
        # Update password
        if user.update(password: new_password, password_confirmation: password_confirmation)
          # Log successful password change
          duration_ms = ((Time.current - start_time) * 1000).round
          AuthHealthCheck.log_auth_event('password_change', success: true, duration_ms: duration_ms)
          
          render json: {
            success: true,
            message: I18n.t('front.successful')
          }
        else
          # Log failed password change
          duration_ms = ((Time.current - start_time) * 1000).round
          AuthHealthCheck.log_auth_event('password_change', success: false, duration_ms: duration_ms, error: 'Validation failed')
          
          render json: {
            error: I18n.t('front.failed'),
            messages: user.errors.full_messages
          }, status: :unprocessable_entity
        end
      rescue => e
        Rails.logger.error "Password change error: #{e.message}"
        render json: { error: I18n.t('front.error') }, status: :internal_server_error
      end
      
      # CHUNK 57: Email confirmation endpoints for JWT-based registration flow
      
      # Confirm email address using token from confirmation email
      def confirm_email
        start_time = Time.current
        
        # Extract confirmation token from params
        token = params[:confirmation_token]
        
        unless token.present?
          render json: { 
            error: I18n.t('front.token_required')
          }, status: :unprocessable_entity
          return
        end
        
        # Validate and find user by confirmation token
        user = validate_and_find_user_by_confirmation_token(token)
        
        unless user
          render json: { 
            error: I18n.t('front.invalid_or_expired_token')
          }, status: :unprocessable_entity
          return
        end
        
        # Check if user is already confirmed
        if user.confirmed_at.present?
          render json: { 
            message: I18n.t('front.already_confirmed')
          }, status: :ok
          return
        end
        
        # Confirm the user's email
        user.confirmed_at = Time.current
        
        if user.save
          # Clear the confirmation token from Redis
          clear_confirmation_token(user)
          
          # Email confirmation successful - automatically log the user in via JWT
          handle_successful_jwt_login(user,
            event_name: 'email_confirmation',
            start_time: start_time,
            message: I18n.t('front.successful')
          )
        else
          # Email confirmation failed
          duration_ms = ((Time.current - start_time) * 1000).round
          AuthHealthCheck.log_auth_event('email_confirmation', success: false, duration_ms: duration_ms, error: 'Failed to confirm email')
          
          render json: {
            error: I18n.t('front.failed'),
            errors: user.errors.full_messages
          }, status: :unprocessable_entity
        end
      rescue => e
        Rails.logger.error "Email confirmation error: #{e.message}"
        render json: { error: I18n.t('front.failed_to_confirm') }, status: :internal_server_error
      end
      
      # Resend confirmation email
      def resend_confirmation
        start_time = Time.current
        email = params[:email]
        
        unless email.present?
          render json: { 
            error: I18n.t('front.email_required')
          }, status: :unprocessable_entity
          return
        end
        
        user = User.find_by(email: email.downcase.strip)
        
        # Always return success for security (don't reveal if email exists)
        # This prevents email enumeration attacks
        if user
          if user.confirmed_at.present?
            # User already confirmed
            Rails.logger.info "[SECURITY] Confirmation resend requested for already confirmed user #{user.id} (#{email})"
          else
            # Send new confirmation email
            send_confirmation_email(user)
            Rails.logger.info "[SECURITY] Confirmation email resent to user #{user.id} (#{email})"
            
            # Log successful resend
            duration_ms = ((Time.current - start_time) * 1000).round
            AuthHealthCheck.log_auth_event('resend_confirmation', success: true, duration_ms: duration_ms)
          end
        else
          Rails.logger.warn "[SECURITY] Confirmation resend requested for non-existent email: #{email} from IP: #{request.remote_ip}"
        end
        
        # Always return success (anti-enumeration)
        render json: {
          success: true,
          message: I18n.t('front.successful')
        }, status: :ok
      end
      
      private
      
      def user_params
        params.require(:user).permit(:email, :password, :password_confirmation)
      end
  
      def user_permissions(user)
        role = user.company_user_roles.find_by(company: current_tenant)&.role
        return {} unless role
        
        {
          can_manage_contracts: ['owner', 'admin'].include?(role.name),
          can_manage_company: ['owner', 'admin'].include?(role.name),
          can_view_reports: ['owner', 'admin', 'supervisor'].include?(role.name)
        }
      end
      
      
      # Password reset token validation helpers
      def validate_and_find_user_by_reset_token(token)
        return nil if token.blank?
        
        # Search for the reset token in Redis across all users
        user = find_user_by_reset_token(token)
        return nil unless user
        
        # Validate token hasn't expired
        reset_data = RedisService.get(:password_reset, [user.id])
        return nil unless reset_data
        
        begin
          # Check if token matches and hasn't expired
          if reset_data['token'] == token && reset_data['expires_at'] > Time.current.to_i
            return user
          end
        rescue => e
          Rails.logger.error "Error processing reset token data: #{e.message}"
        end
        
        nil
      end
      
      def find_user_by_reset_token(token)
        # Use reverse lookup for efficient token validation
        token_data = RedisService.get(:reset_token, [token])
        
        return nil unless token_data&.dig('user_id')
        User.find_by(id: token_data['user_id'])
      end
      
      def clear_password_reset_token(user)
        # Get the token to clear reverse lookup
        reset_data = RedisService.get(:password_reset, [user.id])
        if reset_data
          begin
            token = reset_data['token']
            
            # Clear reverse lookup
            if token
              RedisService.delete(:reset_token, [token])
            end
          rescue => e
            # Continue with cleanup even if processing fails
            Rails.logger.warn "Error processing reset data during cleanup: #{e.message}"
          end
        end
        
        # Clear main reset data
        RedisService.delete(:password_reset, [user.id])
        Rails.logger.info "[SECURITY] Password reset token cleared for user #{user.id}"
      end
      
      # Email confirmation helper methods
      def send_confirmation_email(user)
        # Generate secure confirmation token
        confirmation_token = SecureRandom.urlsafe_base64(32)
        confirmation_expires_at = 24.hours.from_now
        
        # Store confirmation token securely in Redis with expiration
        confirmation_data = {
          token: confirmation_token,
          expires_at: confirmation_expires_at.to_i,
          created_at: Time.current.to_i,
          user_id: user.id,
          email: user.email,
          ip_address: request.remote_ip,
          user_agent: request.user_agent
        }
        
        RedisService.set(:email_confirmation, [user.id], confirmation_data, ttl: 24.hours.to_i)
        
        # Store reverse lookup for efficient token validation  
        RedisService.set(:confirmation_token, [confirmation_token], { user_id: user.id }, ttl: 24.hours.to_i)
        
        # Send confirmation email
        begin
          EmailConfirmationMailer.confirmation_instructions(user, confirmation_token).deliver_now
          Rails.logger.info "[SECURITY] Email confirmation sent to user #{user.id} (#{user.email})"
        rescue => e
          Rails.logger.error "[ERROR] Failed to send email confirmation: #{e.message}"
          raise e
        end
      end
      
      def validate_and_find_user_by_confirmation_token(token)
        return nil unless token.present?
        
        # Get user via reverse lookup
        user = find_user_by_confirmation_token(token)
        return nil unless user
        
        # Get confirmation data for validation
        confirmation_data = RedisService.get(:email_confirmation, [user.id])
        return nil unless confirmation_data
        
        # Validate token matches and hasn't expired
        stored_token = confirmation_data['token']
        expires_at = confirmation_data['expires_at']
        
        # Check token match
        unless stored_token == token
          Rails.logger.warn "[SECURITY] Confirmation token mismatch for user #{user.id}"
          return nil
        end
        
        # Check expiration
        if Time.current.to_i > expires_at
          Rails.logger.warn "[SECURITY] Expired confirmation token used for user #{user.id}"
          clear_confirmation_token(user)
          return nil
        end
        
        user
      end
      
      def find_user_by_confirmation_token(token)
        # Use reverse lookup for efficient token validation
        token_data = RedisService.get(:confirmation_token, [token])
        
        return nil unless token_data&.dig('user_id')
        User.find_by(id: token_data['user_id'])
      end
      
      def clear_confirmation_token(user)
        # Get the token to clear reverse lookup
        confirmation_data = RedisService.get(:email_confirmation, [user.id])
        if confirmation_data
          begin
            token = confirmation_data['token']
            
            # Clear reverse lookup
            if token
              RedisService.delete(:confirmation_token, [token])
            end
          rescue => e
            # Continue with cleanup even if processing fails
            Rails.logger.warn "Error processing confirmation data during cleanup: #{e.message}"
          end
        end
        
        # Clear main confirmation data
        RedisService.delete(:email_confirmation, [user.id])
        Rails.logger.info "[SECURITY] Email confirmation token cleared for user #{user.id}"
      end
      
    end
  end
end