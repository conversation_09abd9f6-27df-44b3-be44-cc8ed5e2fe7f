# app/controllers/api/v1/companies_controller.rb
class Api::V1::CompaniesController < Api::V1::ApiController
  
  include ActionPolicy::Controller
  
  # ApiController already includes JWT authentication with session fallback
  before_action :authenticate_user!
  
  # For company switching, we'll temporarily skip tenant verification
  # since we're changing the tenant context
  skip_before_action :set_current_tenant, only: [:switch_company]
  
  authorize :user, through: :current_user

  # JWT-based company switching endpoint
  # POST /api/v1/companies/switch
  # Body: { "company_id": 123 }
  # Response: { "success": true, "token": "...", "company": {...} }
  def switch_company
    company_id = params[:company_id]
    
    if company_id.blank?
      render json: { 
        success: false, 
        error: 'Company ID is required' 
      }, status: :bad_request
      return
    end

    begin
      # This line does validation: user must have access to the company
      # If user doesn't have access or company doesn't exist, this will raise ActiveRecord::RecordNotFound
      target_company = current_user.companies.find(company_id)
      
      # Set the new tenant context for this request
      ActsAsTenant.current_tenant = target_company
      
      # Update the user's primary company (optional, follows existing pattern)
      current_user.set_primary_company(target_company)
      
      # Generate new JWT with updated company_id in the payload
      new_payload = current_user.jwt_payload
      new_token = JwtService.encode_access_token(new_payload)
      
      # CRITICAL FIX: Update Redis session with new JWT for page refresh persistence
      # Without this, page refresh will load old JWT from Redis with wrong company_id
      session_cookie = cookies.signed[:jwt_session_id]
      if session_cookie
        user_id, session_id = parse_jwt_session_cookie(session_cookie)
        if user_id && session_id
          success = JwtSessionService.update_session_token(user_id, session_id, new_token)
          Rails.logger.info("Updated Redis session with new JWT: #{success ? 'success' : 'failed'}")
        end
      end
      
      # Log the company switch for monitoring
      Rails.logger.info("Company switch: User #{current_user.id} switched to company #{target_company.id}")
      
      # Return new token and company info
      render json: {
        success: true,
        access_token: new_token,  # Standardized field name to match login/refresh endpoints
        company: target_company.as_json( # TODO: Use a dedicated serializer for company response
          only: [:id, :name], 
          methods: [:logo_url]
        ),
        message: "Successfully switched to #{target_company.name}"
      }, status: :ok
      
    rescue ActiveRecord::RecordNotFound
      # This means the user doesn't have access to the requested company
      render json: { 
        success: false, 
        error: 'Company not found or access denied' 
      }, status: :forbidden
      
    rescue => e # TODO: Consider more specific error handling if needed
      # Handle any other errors (JWT generation, database issues, etc.)
      Rails.logger.error("Company switch error: #{e.message}")
      render json: { 
        success: false, 
        error: 'Internal server error during company switch' 
      }, status: :internal_server_error
    end
  end

  # GET /api/v1/companies
  # Returns list of companies user has access to (similar to existing index)
  def index
    ActsAsTenant.without_tenant do
      @company_user_roles = current_user.company_user_roles
                                       .includes(:company, :role)
                                       .where(active: true)  # Only active roles
                                       .order(:created_at) # TODO: Consider ordering by company name or primary status
    end
    
    @current_tenant = ActsAsTenant.current_tenant&.id
    
    render json: {
      company_user_roles: @company_user_roles.as_json(include: { 
        company: { 
          only: [:id, :name],
          methods: [:logo_url]
        }, 
        role: { methods: [:translated_name] } 
      }),
      current_tenant: @current_tenant
    }
  end

  private

  # TODO: Integrate Action Policy for explicit authorization checks if needed beyond the find scope
  def authorize_company_access!(company)
    unless current_user.companies.include?(company)
      raise ActionPolicy::Unauthorized, "Access denied to company #{company.id}"
    end
  end
end