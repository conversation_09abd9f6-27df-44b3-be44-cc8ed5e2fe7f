# ABOUTME: API v1 controller for daily work activity tracking and management
# ABOUTME: Handles work activity start/stop operations with proper JWT authentication
module Api
  module V1
    class DailyActivitiesController < ApiController
      before_action :set_contract
      before_action :set_daily_activity, only: [:end_work_activity, :update]
      before_action :set_work, only: [:start_work_activity]
      
      # GET /api/v1/daily_activities/current_work_activity
      def current_work_activity
        # No explicit authorization needed - users can see their own activities
        activity = DailyActivity.where(
          user: current_user,
          company: @company,
          end_time: nil,
          activity_type: ['travel_to_work', 'work_at_location', 'work_remote']
        ).includes(:work, :work_assignment).first
        
        if activity
          work_session = WorkSession.find_by(daily_activity: activity, status: 'in_progress')
          render json: { 
            activity: activity.as_json(include: [:work, :work_assignment]),
            work_session: work_session
          }
        else
          render json: { activity: nil, work_session: nil }
        end
      end
      
      # POST /api/v1/daily_activities/start_work_activity
      def start_work_activity
        # Check if user has a running daily log
        unless has_running_daily_log?
          render json: { error: I18n.t('controllers.daily_activities.errors.no_running_daily_log', default: 'Nejprve musíte zahájit pracovní den.') }, status: :forbidden
          return
        end

        work_assignment = @work.work_assignments.find_by(contract_id: @contract.id)
        unless work_assignment
          render json: { error: I18n.t('controllers.daily_activities.errors.not_assigned_to_work') }, status: :forbidden
          return
        end

        begin
          ActiveRecord::Base.transaction do
            # End any existing active work activity
            active_activity = DailyActivity.where(
              user: current_user, company: @company, end_time: nil,
              activity_type: ['travel_to_work', 'work_at_location', 'work_remote']
            ).first

            if active_activity
              active_activity.update!(end_time: Time.current)
              if active_activity.work_session&.in_progress?
                active_activity.work_session.update!(end_time: Time.current, status: :completed)
              end
            end

            # Create new work activity
            @daily_activity = DailyActivity.create!(
              user: current_user, company: @company, contract: @contract, work: @work,
              work_assignment: work_assignment, activity_type: params[:activity_type] || 'work_at_location',
              description: params[:description] || "#{@work.title}", start_time: Time.current,
              daily_log_id: params[:daily_log_id] || current_running_daily_log&.id
            )

            work_session = nil
            if ['work_at_location', 'work_remote'].include?(@daily_activity.activity_type)
              work_session = @work.work_sessions.create!(
                user: current_user, company: @company, work_assignment: work_assignment,
                daily_activity: @daily_activity, start_time: @daily_activity.start_time,
                status: :in_progress
              )
            end

            # Broadcast team status update to managers
            broadcast_team_status_update(
              employee: current_user,
              working: true,
              work: @work,
              activity: @daily_activity,
              event_type: 'work_activity_started'
            )

            render json: {
              activity: @daily_activity.as_json(include: [:work, :work_assignment]),
              work_session: work_session,
              message: I18n.t('controllers.daily_activities.messages.work_activity_started')
            }, status: :created
          end
        rescue ActiveRecord::RecordInvalid => e
          render json: { errors: e.record.errors.full_messages }, status: :unprocessable_entity
        end
      end

      # PATCH /api/v1/daily_activities/:id/end_work_activity
      def end_work_activity
        work_session = @daily_activity.work_session

        begin
          ActiveRecord::Base.transaction do
            @daily_activity.update!(end_time: Time.current)
            if work_session&.in_progress?
              work_session.update!(
                end_time: @daily_activity.end_time,
                status: :completed,
                check_in_notes: params[:notes]
              )
            end
          end

          # Broadcast team status update to managers
          broadcast_team_status_update(
            employee: current_user,
            working: false,
            work: @daily_activity.work,
            activity: @daily_activity,
            event_type: 'work_activity_ended'
          )

          render json: {
            activity: @daily_activity.as_json(include: [:work, :work_assignment]),
            work_session: work_session&.reload,
            message: I18n.t('controllers.daily_activities.messages.work_activity_ended')
          }
        rescue ActiveRecord::RecordInvalid => e
          render json: { errors: e.record.errors.full_messages }, status: :unprocessable_entity
        end
      end
      
      # POST /api/v1/daily_activities
      # Create regular daily activity (not work-related)
      def create
        # Check if user has a running daily log
        unless has_running_daily_log?
          render json: { error: I18n.t('controllers.daily_activities.errors.no_running_daily_log', default: 'Nejprve musíte zahájit pracovní den.') }, status: :forbidden
          return
        end

        @daily_activity = DailyActivity.new(daily_activity_params)
        @daily_activity.user = current_user
        @daily_activity.company = @company
        @daily_activity.contract = @contract

        # Set daily_log_id to current running daily log if not provided
        if @daily_activity.daily_log_id.blank?
          @daily_activity.daily_log_id = current_running_daily_log&.id
        end

        if @daily_activity.save
          render json: @daily_activity, status: :created
        else
          render json: { errors: @daily_activity.errors.full_messages },
                 status: :unprocessable_entity
        end
      end
      
      # PUT/PATCH /api/v1/daily_activities/:id
      # Update daily activity  
      def update
        if @daily_activity.update(daily_activity_params)
          render json: @daily_activity
        else
          render json: { errors: @daily_activity.errors.full_messages }, 
                 status: :unprocessable_entity
        end
      end
      
      private

      def daily_activity_params
        params.require(:daily_activity).permit(:description, :start_time, :end_time, :daily_log_id, :activity_type)
      end

      def set_contract
        @contract = current_user.contracts.find_by(company: @company)
        unless @contract
          render json: { error: I18n.t('controllers.works.errors.no_workspace_connection') }, status: :forbidden
          return false
        end
      end

      def has_running_daily_log?
        current_running_daily_log.present?
      end

      def current_running_daily_log
        @current_running_daily_log ||= @contract.daily_logs.find_by(
          start_time: DateTimeHelper.day_range(Date.current),
          end_time: nil
        )
      end
      
      def set_daily_activity
        @daily_activity = DailyActivity.find_by(
          id: params[:id],
          user: current_user,
          company: @company
        )
        
        unless @daily_activity
          render json: { error: I18n.t('controllers.daily_activities.errors.activity_not_found') }, status: :not_found
          return false
        end
      end
      
      def set_work
        @work = @company.works.find_by(id: params[:work_id])
        unless @work
          render json: { error: I18n.t('controllers.daily_activities.errors.work_not_found') }, status: :not_found
          return false
        end
      end

      def broadcast_team_status_update(employee:, working:, work:, activity:, event_type:)
        # Get employee name from contract
        employee_name = if @contract
          "#{@contract.first_name} #{@contract.last_name}"
        else
          employee.email
        end

        # Build the broadcast data matching the specification
        broadcast_data = {
          type: 'team_status_update',
          event_type: event_type,
          employee_id: @contract.id, # Use contract_id to match frontend expectations
          employee_name: employee_name,
          working: working,
          activity_started_at: activity.start_time&.iso8601,
          activity_ended_at: activity.end_time&.iso8601,
          activity_type: activity.activity_type
        }

        # Include work details if present
        if work
          broadcast_data[:current_work] = {
            id: work.id,
            title: work.title,
            location: work.location.presence || work.address
          }
        end

        # Broadcast to the company channel (received by all managers)
        TeamStatusChannel.broadcast_to(
          @company,
          broadcast_data
        )
        
        Rails.logger.info "[TeamStatusChannel] Broadcasting #{event_type} for employee #{employee.email} in company #{@company.name}"
      rescue => e
        Rails.logger.error "[TeamStatusChannel] Failed to broadcast: #{e.message}"
        # Don't let broadcast failures affect the main action
      end
    end
  end
end