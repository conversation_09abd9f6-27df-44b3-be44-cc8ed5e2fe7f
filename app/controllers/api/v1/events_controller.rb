module Api
  module V1
    class EventsController < ApiController
      include <PERSON><PERSON><PERSON><PERSON>
      
      # Authentication is now handled by <PERSON>piControll<PERSON> (dual auth)
      # before_action :authenticate_user! is inherited from ApiController
      # Tenant is set by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from JWT or session
      # @company is set by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'s set_tenant_company
      before_action :set_contract, except: [:confirm]
      
      # TODO: Consider adding explicit authorize! calls for specific resource instances where appropriate.
      # This aligns with the broader authorization strategy (Chunk 29).
      # See: docs/jwt_implementation_notes_chunk_19_final.md - "Potential Future Enhancements #4"
      
      before_action :set_event, only: [:reschedule]
      
      # FIXME: This is a security vulnerability. We should use action_policy gem to authorize the user
      # FIXME: only upcomming events should be shown
      def index
        Rails.logger.info "[🔍 DEBUG] EventsController#index called"
        Rails.logger.info "[🔍 DEBUG] Current user: #{current_user&.email}"
        Rails.logger.info "[🔍 DEBUG] Current company: #{@company&.id} - #{@company&.name}"
        Rails.logger.info "[🔍 DEBUG] Contract: #{@contract&.id}"
        
        status = params[:status] || 'pending'
        events = @company.events.includes([:contract])
        
        Rails.logger.info "[🔍 DEBUG] Found #{events.count} total events for company"
        
        events = events.public_send(status).map do |event|
          event.as_json.merge(
            name: "#{event.contract.first_name} #{event.contract.last_name}",
          )
        end
        
        Rails.logger.info "[🔍 DEBUG] Returning #{events.length} events with status '#{status}'"
        Rails.logger.info "[🔍 DEBUG] Sample event: #{events.first&.slice('id', 'title', 'start_time', 'end_time')}"
        
        render json: events
      end

      # GET /api/v1/events/fetch
      # Fetches events, works, meetings and holidays for a given month
      def fetch
        begin
          date_param = Date.parse(params[:date])
          year = date_param.year
          month = date_param.month
          start_date = Date.new(year, month, 1)
          end_date = start_date.end_of_month + 1

          # Fetch holidays for the month
          holidays = fetch_holidays(year, month)

          # Fetch events based on company policy authorization
          if allowed_to?(:view_all_company_events?, @company)
            # Managers can see ALL contract events within their company
            events = @company.events.includes(:contract)
                              .where(start_time: start_date..end_date)
                              .as_json(include: { 
                                contract: { only: [:id, :first_name, :last_name] } 
                              })
          else
            # Regular users see only their own contract events
            events = @contract.events.includes(:contract)
                              .where(start_time: start_date..end_date)
                              .as_json(include: {
                                contract: { only: [:id, :first_name, :last_name] }
                              })
          end

          # Fetch works for the company
          works = @company.works
                          .where('scheduled_start_date BETWEEN ? AND ? OR scheduled_end_date BETWEEN ? AND ?', 
                                start_date, end_date, start_date, end_date)
                          .includes(:booking, work_assignments: :contract)
                          .as_json(include: { 
                            work_assignments: { 
                              include: { contract: { only: [:id, :first_name, :last_name] } } 
                            },
                            booking: { only: [:id, :status, :client_name, :preferred_date, :preferred_period, :specific_time] }
                          })

          # Load meetings based on user role
          meetings = if current_user.company_user_roles.where(company: @company).joins(:role)
                        .where(roles: { name: ['owner', 'admin', 'supervisor'] }).exists?
            # Managers can see all meetings
            @company.meetings.where('confirmed_date BETWEEN ? AND ?', start_date, end_date)
                            .order(:confirmed_date)
          else
            # Regular users can only see meetings they're part of
            @company.meetings.joins(:meeting_users)
                            .where(meeting_users: { user_id: current_user.id })
                            .where('confirmed_date BETWEEN ? AND ?', start_date, end_date)
                            .order(:confirmed_date)
          end

          meetings_json = meetings.as_json(include: { 
            meeting_users: { only: [:id, :email, :selected_dates] },
            created_by: { only: [:id, :email] }
          })
                             
          render json: { 
            events: events, 
            works: works, 
            meetings: meetings_json, 
            holidays: holidays,
            current_user_contract_id: @contract&.id
          }
        rescue Date::Error
          Rails.logger.error "[🔍 DEBUG] Invalid date format: #{params[:date]}"
          render json: { error: t('controllers.events.errors.invalid_date_format') }, status: :unprocessable_entity
        end
      end

      # POST /api/v1/events
      def create
        event = @contract.events.new(event_params)
        event.user_id = current_user.id
        
        if event.save
          # Include contract information in response so frontend shows names immediately
          event_with_contract = event.as_json(include: { 
            contract: { only: [:id, :first_name, :last_name] } 
          })
          render json: { success: true, event: event_with_contract, message: t('controllers.events.messages.created') }, status: :created
        else
          render json: { message: event.errors.full_messages.join(', '), messageType: 'error' }, status: :unprocessable_entity
        end
      end

      # DELETE /api/v1/events/:id
      def destroy
        @event = @contract.events.find(params[:id])
        @event.destroy
        render json: { success: true, message: t('controllers.events.messages.deleted') }
      rescue ActiveRecord::RecordNotFound
        render json: { error: t('controllers.events.errors.not_found') }, status: :not_found
      end

      def confirm
        @event = @company.events.find(params[:id])
        if @event.update(status: 'approved')
          render json: { success: true, event: @event }, status: :ok
        else
          render json: { success: false, errors: @event.errors.full_messages }, status: :unprocessable_entity
        end
      end

      def reject
        @event = @company.events.find(params[:id])
        if @event.update(status: 'rejected')
          render json: { success: true, event: @event }, status: :ok
        else
          render json: { success: false, errors: @event.errors.full_messages }, status: :unprocessable_entity
        end
      end

      # PATCH /api/v1/events/:id/reschedule
      def reschedule
        # Check authorization - user can only reschedule their own events unless they're an owner
        unless can_reschedule_event?(@event)
          render json: { error: t('controllers.events.errors.unauthorized') }, status: :forbidden
          return
        end
        
        # Update the event dates
        if @event.update(reschedule_params)
          render json: { success: true, event: @event, message: t('controllers.events.messages.rescheduled') }
        else
          render json: { success: false, errors: @event.errors.full_messages }, status: :unprocessable_entity
        end
      end

      private 

      
      def set_contract
        @contract = current_user.contracts.find_by(company: @company)
        unless @contract
          render json: { error: t('controllers.events.errors.no_workspace_connection') }, status: :forbidden
        end
      end

      def event_params
        params.require(:event).permit(:event_type, :title, :start_time, :end_time, :description, :place)
      end
      
      def reschedule_params
        params.permit(:start_time, :end_time)
      end
      
      def set_event
        @event = @contract.events.find(params[:id])
      rescue ActiveRecord::RecordNotFound
        render json: { error: t('controllers.events.errors.not_found') }, status: :not_found
      end
      
      def can_reschedule_event?(event)
        # Managers can reschedule events from their own contract only (set_event ensures this)
        return true if current_user.has_role?('owner', @company)
        return true if current_user.has_role?('admin', @company)
        return true if current_user.has_role?('supervisor', @company)
        
        # Regular users can reschedule their own events
        event.user_id == current_user.id
      end

    end
  end
end