module Api
  module V1
    class FeedbacksController < ApiController
      # Feedback is available to authenticated users only

      # POST /api/v1/feedbacks
      def create
        # Authentication and tenant are enforced by ApiController
        feedback = Feedback.new(feedback_params)
        feedback.company = @company if defined?(@company) && @company.present?
        feedback.user = current_user if current_user.present?
        feedback.user_plan = @company&.current_plan&.name
        feedback.user_company_name = @company&.name
        # Always use authenticated user's email; do not accept client-provided email
        feedback.user_email = current_user&.email

        # Fallbacks
        feedback.page_url ||= request.headers['Referer']

        if feedback.save
          # Send email to central inbox with context
          FeedbackMailer.submitted(feedback.id).deliver_later
          render json: {
            success: true,
            message: I18n.t('controllers.feedbacks.messages.submitted', default: 'Děkujeme za zpětnou vazbu!')
          }, status: :created
        else
          # Debugging aid in tests: print validation errors to stdout
          begin
            puts "[FeedbacksController] Validation errors: #{feedback.errors.full_messages.join(', ')}"
          rescue => _e
          end
          render json: { success: false, errors: feedback.errors.full_messages }, status: :unprocessable_entity
        end
      end

      private

      def feedback_params
        params.require(:feedback).permit(:page_url, :category, :message)
      end
    end
  end
end

