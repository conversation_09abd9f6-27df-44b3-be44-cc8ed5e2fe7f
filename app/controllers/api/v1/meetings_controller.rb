# ABOUTME: API v1 controller for meetings following modern SPA architecture patterns
# ABOUTME: Extends ApiController for JWT authentication and provides RESTful + custom actions
module Api
  module V1
    class MeetingsController < ApiController
      include HolidayFetcher
      
      before_action :set_meeting, only: [:show, :update, :destroy, :resend_invitation, :extend_meeting_dates, :get_best_options, :add_user_to_confirmed_date, :confirm]
      before_action :authorize_meeting_actions, only: [:resend_invitation, :extend_meeting_dates, :get_best_options, :add_user_to_confirmed_date, :confirm, :destroy]
      before_action :authorize_create, only: [:create]

      def index
        @meetings = @company.meetings.order(created_at: :desc)
        render json: @meetings.as_json(include: { meeting_users: { only: [:id, :email, :selected_dates, :status] } })
      end

      def show
        render json: @meeting.as_json(include: { meeting_users: { only: [:id, :email, :selected_dates, :status] } })
      end

      def create
        @meeting = @company.meetings.new(meeting_params)
        @meeting.created_by = current_user

        meeting_data = params.require(:meeting)
        contract_ids = meeting_data[:contract_ids]
        additional_emails = meeting_data[:additional_emails]
        
        ActiveRecord::Base.transaction do
          if @meeting.save
            # Add meeting users from contracts
            if contract_ids.present?
              contract_ids.each do |contract_id|
                contract = @company.contracts.find(contract_id)
                next if contract.user_id == current_user.id
                @meeting.meeting_users.create!(
                  contract: contract,
                  user: contract.user
                )
              end
            end
            
            # Add additional email addresses
            if additional_emails.present?
              additional_emails.each do |email|
                next if email.blank?
                next if email.downcase == current_user.email.downcase
                @meeting.meeting_users.create!(email: email)
              end
            end
            
            # Send invitation emails
            @meeting.meeting_users.each do |meeting_user|
              MeetingMailer.invitation_email(@meeting, meeting_user).deliver_now
            end
            
            render json: { 
              success: true, 
              meeting: @meeting, 
              message: meetings_t('messages.created') 
            }, status: :created
          else
            render json: { 
              success: false, 
              errors: @meeting.errors.full_messages 
            }, status: :unprocessable_entity
          end
        end
      end

      def update
        if @meeting.update(meeting_params)
          render json: { 
            success: true, 
            meeting: @meeting, 
            message: meetings_t('messages.updated') 
          }
        else
          render json: { 
            success: false, 
            errors: @meeting.errors.full_messages 
          }, status: :unprocessable_entity
        end
      end

      def destroy
        if @meeting.destroy
          render json: { 
            success: true, 
            message: meetings_t('messages.deleted') 
          }
        else
          render json: { 
            success: false, 
            errors: @meeting.errors.full_messages 
          }, status: :unprocessable_entity
        end
      end

      def conflicts
        # Parse contract_ids from string format (e.g., "1,2,3") to array of integers
        contract_ids = if params[:contract_ids].present?
          if params[:contract_ids].is_a?(Array)
            params[:contract_ids].map(&:to_i)
          else
            params[:contract_ids].split(',').map(&:to_i)
          end
        else
          []
        end
        
        Rails.logger.info "🔍 DEBUG Meetings#conflicts - Parsed contract_ids: #{contract_ids.inspect}"
        
        # Add current user's contract ID if they have one
        current_user_contract = @company.contracts.find_by(user_id: current_user.id)
        if current_user_contract
          Rails.logger.info "🔍 DEBUG Meetings#conflicts - Adding current user contract: #{current_user_contract.id}"
          contract_ids << current_user_contract.id
          contract_ids.uniq!
        end
        
        start_date = Date.parse(params[:start_date])
        end_date = Date.parse(params[:end_date])
        
        events = []
        works = []
        meetings = []
        holidays = []
        
        # Fetch holidays
        (start_date.month..end_date.month).each do |month|
          year = start_date.year
          month_holidays = fetch_holidays(year, month)
          holidays.concat(month_holidays)
        end
        
        if contract_ids.present?
          contracts = @company.contracts.where(id: contract_ids)
          user_ids = contracts.where.not(user_id: nil).pluck(:user_id)
          
          # Get events (only show approved events as conflicts)
          Rails.logger.info "🔍 DEBUG Meetings#conflicts - Searching for events with contract_ids: #{contract_ids}"
          Rails.logger.info "🔍 DEBUG Meetings#conflicts - Date range: #{start_date.beginning_of_day} to #{end_date.end_of_day}"
          
          events = Event.joins(:contract)
            .where(contract_id: contract_ids)
            .where('start_time <= ? AND end_time >= ?', end_date.end_of_day, start_date.beginning_of_day)
            .where(status: 'approved')
            .select('events.id, events.title, events.event_type, events.start_time, events.end_time, events.status, contracts.first_name || \' \' || contracts.last_name as contract_name')
          
          Rails.logger.info "🔍 DEBUG Meetings#conflicts - Found #{events.count} events"
          events.each do |event|
            Rails.logger.info "🔍 DEBUG Event: #{event.title} (#{event.event_type}) - Status: #{event.status} - #{event.start_time} to #{event.end_time}"
          end

          # Get works
          works = Work.joins(:work_assignments)
            .where(work_assignments: { contract_id: contract_ids })
            .where('scheduled_start_date <= ? AND scheduled_end_date >= ?', end_date, start_date)
            .select('works.id, works.title, works.scheduled_start_date, works.scheduled_end_date')
            .distinct

          # Get meetings
          if user_ids.present?
            meetings = Meeting.joins(:meeting_users)
              .where(meeting_users: { user_id: user_ids })
              .where.not(confirmed_date: nil)
              .where('confirmed_date >= ? AND confirmed_date <= ?', start_date.beginning_of_day, end_date.end_of_day)
              .select('meetings.id, meetings.title, meetings.confirmed_date')
              .distinct
          end
        end
        
        render json: {
          events: events,
          works: works,
          meetings: meetings,
          holidays: holidays
        }
      end

      def resend_invitation
        meeting_user = @meeting.meeting_users.find(params[:meeting_user_id])
        if meeting_user
          MeetingMailer.invitation_email(@meeting, meeting_user).deliver_now
          render json: { 
            success: true, 
            message: meetings_t('messages.invitation_resent') 
          }
        else
          render json: { 
            success: false, 
            errors: ['Meeting user not found'] 
          }, status: :not_found
        end
      end

      def extend_meeting_dates
        days_forward = params[:days_forward]&.to_i || 7
        days_backward = params[:days_backward]&.to_i || 0
        
        new_day_options = @meeting.generate_extended_date_options(days_forward, days_backward)
        
        if @meeting.update(day_options: new_day_options)
          # Change user status to pending instead of resetting selections
          @meeting.meeting_users.update_all(status: 'pending')
          
          @meeting.meeting_users.each do |meeting_user|
            MeetingMailer.date_extension_email(meeting_user).deliver_now
          end
          
          render json: { 
            success: true, 
            meeting: @meeting, 
            message: meetings_t('messages.dates_extended') 
          }
        else
          render json: { 
            success: false, 
            errors: @meeting.errors.full_messages 
          }, status: :unprocessable_entity
        end
      end

      def get_best_options
        best_options = @meeting.find_best_options_with_attendance
        
        render json: { best_options: best_options }
      end

      def add_user_to_confirmed_date
        meeting_user = @meeting.meeting_users.find(params[:meeting_user_id])
        date = params[:date]

        current_selections = meeting_user.selected_dates || {}
        current_selections[date] = true
        
        if meeting_user.update(selected_dates: current_selections)
          render json: { 
            success: true, 
            message: meetings_t('messages.user_added_to_date') 
          }
        else
          render json: { 
            success: false, 
            errors: meeting_user.errors.full_messages 
          }, status: :unprocessable_entity
        end
      end

      def confirm
        confirmed_date = params[:confirmed_date]
        
        if @meeting.update(confirmed_date: confirmed_date)
          creator = @meeting.created_by
          if creator && !@meeting.meeting_users.exists?(user_id: creator.id) && !@meeting.meeting_users.exists?(email: creator.email)
            creator_contract = @company.contracts.find_by(user_id: creator.id)

            if creator_contract
              @meeting.meeting_users.create!(
                contract: creator_contract,
                user: creator,
                email: creator.email
              )
            else
              @meeting.meeting_users.create!(email: creator.email)
            end

            @meeting.meeting_users.reload
          end

          # Send confirmation emails
          @meeting.meeting_users.each do |meeting_user|
            MeetingMailer.confirmation_email(meeting_user).deliver_now
          end
          
          render json: { 
            success: true, 
            message: meetings_t('messages.confirmed') 
          }
        else
          render json: { 
            success: false, 
            errors: @meeting.errors.full_messages 
          }, status: :unprocessable_entity
        end
      end

      private

      def authorize_meeting_actions
        if @meeting.created_by_id == current_user.id
          authorize! @company, to: :plus_plan?
        else
          authorize! @company, to: :manage_meeting_actions? 
        end
      end

      def authorize_create
        authorize! @company, to: :create_meeting?
      end

      def set_meeting
        @meeting = @company.meetings.find(params[:id])
      rescue ActiveRecord::RecordNotFound
        render json: { success: false, errors: ['Meeting not found'] }, status: :not_found
      end

      def meeting_params
        params.require(:meeting).permit(:title, :description, :place, day_options: {})
      end

      def meetings_t(key, **options)
        I18n.t("controllers.meetings.#{key}", **options)
      end
    end
  end
end