module Api
  module V1
    class NotificationsController < ApplicationController
      before_action :authenticate_user!
      before_action :set_notification, only: [:read, :process_notification, :destroy]
      
      # GET /api/v1/notifications
      def index
        @notifications = current_user.notifications
                                    .includes(:notifiable)
                                    .recent
                                    .page(params[:page])
                                    .per(20)
        
        render json: {
          notifications: @notifications,
          meta: {
            current_page: @notifications.current_page,
            total_pages: @notifications.total_pages,
            total_count: @notifications.total_count
          }
        }
      end
      
      # GET /api/v1/notifications/unread_count
      def unread_count
        count = current_user.notifications.unread.count
        render json: { unread_count: count }
      end
      
      # GET /api/v1/notifications/for_mainbox
      def for_mainbox
        @notifications = current_user.notifications
                                    .for_mainbox
                                    .includes(:notifiable)
                                    .limit(10)
        
        render json: @notifications
      end
      
      # PUT /api/v1/notifications/:id/read
      def read
        @notification.mark_as_read!
        render json: { success: true, notification: @notification }
      end
      
      # PUT /api/v1/notifications/:id/process
      def process_notification
        @notification.mark_as_processed!
        render json: { success: true, notification: @notification }
      end
      
      # PUT /api/v1/notifications/mark_all_read
      def mark_all_read
        NotificationService.mark_all_as_read(current_user)
        render json: { success: true, message: notifications_t('messages.all_marked_read') }
      end
      
      # DELETE /api/v1/notifications/:id
      def destroy
        @notification.destroy
        render json: { success: true, message: notifications_t('messages.deleted') }
      end
      
      private
      
      def set_notification
        @notification = current_user.notifications.find(params[:id])
      rescue ActiveRecord::RecordNotFound
        render json: { error: notifications_t('errors.not_found') }, status: :not_found
      end
      
      def notifications_t(key, **options)
        t("controllers.notifications.#{key}", **options)
      end
    end
  end
end