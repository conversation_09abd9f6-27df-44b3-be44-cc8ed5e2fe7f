# ABOUTME: API controller for ServiceContract management - handles CRUD operations for service contracts
# ABOUTME: Follows SPA API pattern with JSON responses and proper authorization
module Api
  module V1
    class ServiceContractsController < ApiController
      before_action :set_service_contract, only: [:show, :update, :destroy]
      
      def index
        authorize! @company, to: :manage_works?

        @service_contracts = @company.service_contracts
          .includes(:works)
          .order(created_at: :desc)

        render json: @service_contracts.as_json(
          include: {
            works: { only: [:id, :title, :status, :scheduled_start_date, :scheduled_end_date] }
          },
          methods: [:total_time_spent]
        )
      end
      
      def show
        authorize! @company, to: :manage_works?

        render json: @service_contract.as_json(
          include: {
            works: {
              include: {
                work_assignments: {
                  include: { contract: { only: [:id, :first_name, :last_name] } }
                }
              }
            }
          },
          methods: [:total_time_spent]
        )
      end
      
      def create
        authorize! @company, to: :manage_works?
        
        @service_contract = @company.service_contracts.build(service_contract_params)
        
        if @service_contract.save
          render json: @service_contract, status: :created
        else
          render json: { success: false, errors: @service_contract.errors.full_messages }, status: :unprocessable_entity
        end
      end
      
      def update
        authorize! @company, to: :manage_works?
        
        if @service_contract.update(service_contract_params)
          render json: @service_contract
        else
          render json: { success: false, errors: @service_contract.errors.full_messages }, status: :unprocessable_entity
        end
      end
      
      def destroy
        authorize! @company, to: :manage_works?
        
        if @service_contract.works.any?
          render json: { success: false, error: I18n.t('controllers.service_contracts.errors.has_works') }, status: :unprocessable_entity
        else
          @service_contract.destroy
          render json: { success: true }
        end
      end
      
      private
      
      def set_service_contract
        @service_contract = @company.service_contracts.find(params[:id])
      rescue ActiveRecord::RecordNotFound
        render json: { error: I18n.t('controllers.service_contracts.errors.not_found') }, status: :not_found
      end
      
      def service_contract_params
        params.require(:service_contract).permit(:title, :description, :status, :client_id)
      end
    end
  end
end