module Api
  module V1
    class TranslationsController < ApplicationController
      skip_before_action :require_login, only: [:index, :show] 
      skip_before_action :set_current_tenant, only: [:index, :show]


      # Currently working solution
      def index
        locale = params[:locale] || I18n.default_locale.to_s
        # Ensure the locale is one of the available locales to prevent path traversal
        unless I18n.available_locales.map(&:to_s).include?(locale)
          render json: { error: "Locale not available" }, status: :not_found
          return
        end

        # TODO: Be more specific about which translation keys are exposed to the frontend.
        # Exposing all translations might be a security risk or include unnecessary data.
        # For EXAMPLE, you might want to load only a specific namespace like 'frontend'.
        # translations = I18n.t(".", locale: locale, scope: :frontend)
        # 
        translations = I18n.t("front", locale: locale)
        render json: translations
      end

      # TODO: Compare show method with the one above focusing on the scope and payload sending 
      def show
        requested_locale = params[:locale].to_sym
        if I18n.available_locales.include?(requested_locale)
          # Serve only the 'front' scope
          render json: I18n.t('front', locale: requested_locale, default: {})
        else
          render json: { error: "Locale not found" }, status: :not_found
        end
      end

    end
  end
end 