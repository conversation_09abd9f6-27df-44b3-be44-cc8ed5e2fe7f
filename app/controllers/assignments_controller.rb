class AssignmentsController < ApplicationController
  # Authentication handled by ApplicationController's require_login 

  #TODO: Add more security layers
  #Authorization: Add checks to restrict switching to unauthorized tenants.
  #before_action :check_current_company
  #before_action :permitted_user?
  #before_action :set_current_tenant
  #TODO: Consider securing against Throttling
  #TODO: For high-privilege tenants, require additional confirmation (e.g., re-entering credentials or an OTP).
  #FIXME: not needing allow params?
  #FIXME: Gracefully handle scenarios where: A tenant association is removed mid-session.
  #FIXME: A user attempts to switch to a tenant that no longer exists.
  
  def index
    # Default scope will ensure only active roles are shown
    @company_user_roles = current_user.company_user_roles.includes(:company, :role)
  end

  # CHUNK 47: Company switching in JWT-only mode
  # This method now serves as a Rails controller endpoint that delegates
  # to the JWT-based company switching API for consistency
  def switch
    tenant_id = params[:company_id]
    
    # Use unscoped to bypass default_scope and then check active status explicitly
    company_user_role = current_user.company_user_roles.find_by(company_id: tenant_id)
    
    # Security checks including active status
    if company_user_role && company_user_role.active && valid_role?(company_user_role)
      # Update user's primary company in database
      current_user.set_primary_company(company_user_role.company)
      
      # In JWT-only mode, we don't use session[:tenant_id]
      # The tenant context is managed through JWT tokens
      # For Rails controllers, we need to ensure the user gets a new JWT
      # with the updated company_id claim
      
      Rails.logger.info "[JWT-Only] User #{current_user.id} switched to company #{company_user_role.company_id} at #{Time.current}"
      
      respond_to do |format|
        format.html { 
          # For HTML requests, redirect to trigger a new JWT with updated company
          # The frontend will handle getting a new JWT token
          redirect_to root_path, notice: "Přepnuto na #{company_user_role.company.name}" 
        }
        format.json { 
          # For JSON requests, inform frontend to update JWT
          render json: { 
            success: true, 
            company_name: company_user_role.company.name,
            company_id: company_user_role.company_id,
            message: 'Company switched. Please refresh your authentication token.'
          }, status: :ok 
        }
      end
    else
      # More specific message based on the issue
      message = if company_user_role.nil?
                  "Nemáte oprávnění přepnout na vybranou firmu."
                elsif !company_user_role.active
                  "Váš přístup k této firmě byl pozastaven nebo ukončen."
                else
                  "Nemáte oprávnění přepnout na vybranou firmu."
                end
      respond_to do |format|
        format.html { redirect_to root_path, alert: message }
        format.json { render json: { success: false, error: message }, status: :forbidden }
      end
    end
  end


  private 

  def valid_role?(company_user_role)
    %w[owner employee].include?(company_user_role.role.name)
  end

  # Not implemented yet, not neccessary
  def check_authorization(company_user_role)
    if company_user_role.role.name != "owner" && company_user_role.role.name != "employee"
      redirect_to root_path, alert: "Your role does not allow switching to this tenant."
    end
  end

  # Not implemented yet, not neccessary
  def check_current_company
    if company_user_role.company.archived?
      redirect_to root_path, alert: "The selected tenant is no longer active."
    end
  end

end
