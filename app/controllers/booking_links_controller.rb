class BookingLinksController < ApplicationController
  # Authentication handled by ApplicationController's require_login
  before_action :set_tenant_company
  before_action :set_booking_link, only: [:show, :edit, :update, :destroy]
  before_action :check_subscription


  def index
    @booking_links = @company.booking_links.where(active: true).order(created_at: :desc)
    
    render json: { company_slug: @company.slug, booking_links: @booking_links }, status: :ok 
  end

  def show
    @bookings = @booking_link.bookings.order(created_at: :desc)
  end

  def new
    @booking_link = @company.booking_links.new
  end

  def create
    @booking_link = @company.booking_links.new(booking_link_params)
  
    if @booking_link.save
      render json: {
        booking_link: @booking_link,
        message: booking_link_t('messages.created')
      }, status: :created
    else
      render json: { errors: @booking_link.errors.full_messages }, status: :unprocessable_entity
    end
  end
  
  def edit
  end

  def update
    if @booking_link.update(booking_link_params)
      render json: {
        booking_link: @booking_link,
        message: booking_link_t('messages.updated')
      }
    else
      render :edit
    end
  end

  def destroy
    @booking_link.destroy
    render json: {
      message: booking_link_t('messages.deleted')
    }, status: :ok
  end

  private

  # Helper method for controller-specific translations
  # Makes translation calls cleaner and more maintainable
  def booking_link_t(key, **options)
    t("controllers.booking_links.#{key}", **options)
  end
  
  def check_subscription
    authorize! @company, to: :manage_bookings?
  end

  # def check_subscription
  #   # Instead of direct authorization which blocks everything
  #   if !allowed_to?(:manage_bookings?, @company)
  #     # Return a special response for subscription issues
  #     render json: { 
  #       subscription_required: true,
  #       feature: "booking",
  #       required_plan: "plus"
  #     }, status: :ok
  #   end
  # end

  def set_tenant_company
    @company = ActsAsTenant.current_tenant
  end

  def set_booking_link
    @booking_link = @company.booking_links.find(params[:id])
  end

  def booking_link_params
    params.require(:booking_link).permit(
      :name,
      :description,
      :active,
      :duration,
      :color,
      :location,
      :location_required,
      :is_remote,
      :morning_limit,
      :afternoon_limit,
      :daily_limit,
      :include_works_in_count,
      :book_holidays,
      preferred_days: [],
    )
  end
end