class CompaniesController < ApplicationController
  
  include ActionPolicy::Controller
  
  # Authentication handled by ApplicationController's require_login
  before_action :set_company, only: [:edit, :update, :destroy, :show, :upload_logo]
  before_action :set_tenant_company
  
  skip_before_action :set_current_tenant, only: [:switch]

  # The authorize :user, through: :current_user line ensures that the user context (the current authenticated user) is passed to the policy.
  authorize :user, through: :current_user

  # TODO: add more parameters which relevant to company to be fully working
  # TODO: handle messages and redirects in case of malicious users trying different companies

  def index
    #To avoid eager loading issues, you can use joins instead of includes if you don't need to preload the associations.
    respond_to do |format|
      format.html # renders the index.html.erb view
      format.json do
        ActsAsTenant.without_tenant do
          @company_user_roles = current_user.company_user_roles.includes(:company, :role).order(:created_at)
        end
        @current_tenant = current_tenant.id
        
        render json: {
          company_user_roles: @company_user_roles.as_json(include: { 
            company: { 
              only: [:id, :name],
              methods: [:logo_url]
            }, 
            role: { methods: [:translated_name] } 
          }),
          current_tenant: @current_tenant
        }
      end
    end

  end

  def show
    authorize! @company
    @company_user_roles = current_user.company_user_roles.includes(:company, :role).order(:created_at)
  end
  
  def new
    owner_companies = current_user.company_user_roles.joins(:role).where(roles: { name: 'owner' }).exists?

    # Limited to 1 company per user
    if !owner_companies
      @company = Company.new
    # If a company has the plus plan, it can create more companies
    else
      authorize! @current_tenant, to: :new?
      @company = Company.new
    end
  end

  def create
    @company = Company.new(company_params)
    authorize! @current_tenant if current_user.company_user_roles.joins(:role).where(roles: { name: 'owner' }).exists?

    respond_to do |format|
      if @company.save
        owner_role = Role.find_or_create_by(name: "owner")
        Role.find_or_create_by(name: "employee")
        @company.company_user_roles.create(user: current_user, role: owner_role)
        
        puts "\n ---------- created company: #{@company.inspect} ---------- \n\n"
        
        # Find or initialize contract to avoid potential race conditions or validation issues
        contract = @company.contracts.find_or_initialize_by(email: current_user.email)
        contract.assign_attributes(
          user: current_user,
          first_name: current_user.user_profile&.first_name || current_user.email,
          last_name: current_user.user_profile&.last_name || ''
          # Ensure status is set if it's a new record, default is :active (0)
          # status: :active # Explicitly setting if needed, but model default should handle it
        )
        puts "\n ---------- created contract: #{contract.inspect} ---------- \n\n"
        
        # Save the contract outside the current tenant scope for validation purposes
        # This prevents the uniqueness validation from using the global current_tenant
        # and ensures it validates against the contract's own company_id.
        ActsAsTenant.without_tenant do
          contract.save! 
        end

        # CHUNK 47: JWT-only mode - no session tenant management
        # The frontend will need to refresh JWT token to include new company_id
        # after company creation. The user's primary company is already set
        # when creating the owner role, so JWT will have correct company_id
        # on next authentication.
        
        # format.html { redirect_to companies_path, notice: 'Pracovní prostor byl úspěšně vytvořen.' }
        # format.json { render :index, status: :created, location: @company }
        format.json { render json: { 
          success: true, 
          message: companies_t('messages.created'),
          company_id: @company.id,
          requires_token_refresh: true
        }, status: :created }
      else
        # format.html { render :new }
        # format.json { render json: @company.errors, status: :unprocessable_entity }   
        format.json { render json: { success: false, message: companies_t('errors.create_failed', errors: @company.errors.full_messages.to_sentence) }, status: :unprocessable_entity }
      end
    end
  end

  def edit
    authorize! @company
    render json: { company: @company.as_json(methods: [:logo_url, :original_logo_url]) }
  end

  def update
    authorize! @company
    respond_to do |format|
      if @company.update(company_params)
        format.html { redirect_to companies_path, notice: companies_t('messages.updated') }
        format.json { render json: { success: true, message: companies_t('messages.updated') }, status: :ok }
      else
        format.html do
          flash[:alert] = "#{@company.errors.full_messages.to_sentence}"
          render :edit
        end
        format.json { render json: { success: false, errors: @company.errors.full_messages }, status: :unprocessable_entity }
      end
    end
  end

  # def destroy
  #   authorize! @company
  #   @company.destroy
  #   redirect_to companies_url, notice: "Company deleted successfully."
  # end

  def switch
    company = current_user.companies.find(params[:company_id])
    if company
      ActsAsTenant.current_tenant = company
      redirect_to root_path, notice: companies_t('messages.switched', name: company.name)
      #render json: { success: true, tenant_id: tenant.id }, status: :ok
    else
      redirect_to root_path, alert: companies_t('errors.not_found')
    end
  end

  def leave
    @company = current_user.companies.find(params[:id])
    
    if @company
      # Don't allow leaving if user is the only owner
      owner_role = Role.find_by(name: "owner")
      owners_count = @company.company_user_roles
                            .where(role_id: owner_role.id)
                            .count
                            
      if owners_count <= 1 && current_user.has_role?("owner", @company)
        render json: { 
          success: false, 
          message: companies_t('errors.cannot_leave_as_only_owner'),
          messageType: "error" 
        }, status: :unprocessable_entity
        return
      end
      
      # Proceed with leaving
      if current_user.leave_company(@company)
        # CHUNK 47: JWT-only mode - no session tenant management
        # If user left their current company, frontend needs to refresh JWT
        # to get a new token with updated company context
        requires_token_refresh = (ActsAsTenant.current_tenant&.id == @company.id)
        
        render json: { 
          success: true, 
          message: companies_t('messages.left_successfully'),
          requires_token_refresh: requires_token_refresh
        }, status: :ok
      else
        render json: { 
          success: false, 
          message: companies_t('errors.leave_failed'),
          messageType: "error" 
        }, status: :unprocessable_entity
      end
    else
      render json: { 
        success: false, 
        message: companies_t('errors.not_found'),
        messageType: "error" 
      }, status: :not_found
    end
  end

  def upload_logo
    authorize! @company, to: :manage_logo?
  
    if params[:company_logo].present?
      @company.logo.attach(params[:company_logo])
      
      if @company.logo.attached?
        render json: {
          success: true,
          logo_url: @company.logo_url,
          original_logo_url: @company.original_logo_url,
          message: companies_t('messages.logo_uploaded')
        }
      else
        render json: { errors: [companies_t('errors.logo_upload_failed')] }, status: :unprocessable_entity
      end
    else
      render json: { errors: [companies_t('errors.no_logo_file')] }, status: :unprocessable_entity
    end
  rescue => e
    Rails.logger.error("Logo upload failed: #{e.message}")
    render json: { errors: [companies_t('errors.logo_upload_error', error: e.message)] }, status: :internal_server_error
  end

  
  private
  
  def set_company
    @company = current_user.companies.find_by(id: params[:id])
    unless @company
      flash[:alert] = companies_t('errors.not_found')
      redirect_to companies_path
    end
  end
  
  def company_params
    params.require(:company).permit(
      :name,
      :subdomain,
      :slug,
      :phone,
      :web,
      :address,
      :description,
      )
  end

  def set_tenant_company
    puts "\n ---------- current_tenant: #{ActsAsTenant.current_tenant.inspect} ---------- \n\n"
    @current_tenant = ActsAsTenant.current_tenant
  end

  # Strong parameters for logo upload
  def logo_params
    params.permit(:company_logo)
  end

  def companies_t(key, **options)
    t("controllers.companies.#{key}", **options)
  end

end
