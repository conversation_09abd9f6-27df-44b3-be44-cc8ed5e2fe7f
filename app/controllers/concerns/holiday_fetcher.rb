module HolidayFetcher
  extend ActiveSupport::Concern

  def fetch_holidays(year, month, country = nil)
    month_year = "#{month}-#{year}"
    
    # Determine country code
    country_code = country || 
                  (current_user&.user_setting&.country_code if respond_to?(:current_user) && current_user) || 
                  'CZ'
    
    # Cache key includes country for proper isolation
    cache_key = "holidays/#{country_code}/#{month_year}"
    
    Rails.cache.fetch(cache_key, expires_in: 1.day) do
      Holiday.for_country(country_code)
             .for_month_year(month_year)
             .pluck(:date)
             .map(&:to_s)
    end
  rescue StandardError => e
    Rails.logger.error("Failed to fetch holidays: #{e.message}")
    []
  end

end