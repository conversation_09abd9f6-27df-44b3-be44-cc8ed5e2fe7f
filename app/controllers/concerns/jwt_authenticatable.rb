# app/controllers/concerns/jwt_authenticatable.rb
module JwtAuthenticatable
  extend ActiveSupport::Concern

  included do
    # Instance variable to store the JWT-authenticated user
    attr_reader :current_jwt_user
    # Instance variable to store JWT-derived company_id for policy access
    attr_reader :jwt_company_id
  end

  # Authenticate a user from JWT token
  # Sets @current_jwt_user if authentication successful
  # Renders unauthorized error and halts if authentication fails
  def authenticate_user_from_jwt!
    # DEBUG: Log JWT authentication attempt
    Rails.logger.info "[DEBUG] JwtAuthenticatable#authenticate_user_from_jwt! called"
    Rails.logger.info "[DEBUG] - Controller: #{self.class.name}"
    Rails.logger.info "[DEBUG] - Action: #{action_name}"
    
    token = extract_jwt_from_header
    
    unless token
      Rails.logger.info "JWT authentication failed: No token provided"
      Rails.logger.info "[DEBUG] - Full Authorization header: #{request.headers['Authorization']}"
      render_unauthorized("No authentication token provided")
      return
    end

    payload = decode_and_validate_token(token)
    
    unless payload
      Rails.logger.info "JWT authentication failed: Invalid or expired token"
      render_unauthorized("Invalid or expired token")
      return
    end

    # Find the user from the payload first (before revocation check)
    user = find_user_from_payload(payload)
    
    unless user
      Rails.logger.info "JWT authentication failed: User not found (user_id: #{payload['user_id']})"
      render_unauthorized("User not found")
      return
    end

    # Check if token has been revoked (now we have the user)
    if token_revoked?(payload, user)
      Rails.logger.info "JWT authentication failed: Token has been revoked (jti: #{payload['jti']}, user_id: #{user.id})"
      render_unauthorized("Token has been revoked")
      return
    end

    # Set the current JWT user
    @current_jwt_user = user
    
    # Extract and set tenant (company) context from JWT
    unless set_tenant_from_jwt(payload, user)
      Rails.logger.info "JWT authentication failed: Invalid tenant context"
      render_unauthorized("Invalid tenant context in token")
      return
    end
    
    # Log successful JWT authentication
    Rails.logger.info "JWT authentication successful for user: #{user.id}"
    AuthHealthCheck.log_auth_event('jwt_authentication', success: true)
  end

  # Authenticate user from session cookie (for page refreshes)
  # Retrieves JWT from Redis using session_id from cookie
  def authenticate_from_session_cookie
    session_cookie = cookies.signed[:jwt_session_id]
    return false unless session_cookie

    # Extract user_id and session_id using helper
    user_id, session_id = parse_jwt_session_cookie(session_cookie)
    return false unless user_id && session_id

    # Retrieve session from Redis with context validation
    validation_context = {
      ip_address: request.remote_ip,
      user_agent: request.user_agent
    }
    session_data = JwtSessionService.find_session(user_id, session_id, validate_context: validation_context)
    return false unless session_data

    # Use the stored access token for authentication
    token = session_data[:access_token]
    return false unless token

    # Decode and validate the stored token
    payload = decode_and_validate_token(token)
    return false unless payload

    # Continue with normal JWT authentication flow
    user = find_user_from_payload(payload)
    return false unless user

    @current_jwt_user = user
    
    unless set_tenant_from_jwt(payload, user)
      Rails.logger.info "JWT session authentication failed: Invalid tenant context"
      return false
    end

    Rails.logger.info "JWT session authentication successful for user: #{user.id}"
    true
  rescue => e
    Rails.logger.error "JWT session authentication error: #{e.message}"
    false
  end

  private

  # Extract JWT token from Authorization header
  # Expected format: "Bearer <token>"
  def extract_jwt_from_header
    auth_header = request.headers['Authorization']
    return nil unless auth_header.present?
    
    # Extract token from "Bearer <token>" format
    match = auth_header.match(/^Bearer\s+(.+)$/i)
    match ? match[1] : nil
  end

  # Extract JWT token from Redis session via cookie
  # Used for page refreshes when no Authorization header present
  def extract_jwt_from_session_cookie
    session_cookie = cookies.signed[:jwt_session_id]
    return nil unless session_cookie

    # Extract user_id and session_id using helper
    user_id, session_id = parse_jwt_session_cookie(session_cookie)
    return nil unless user_id && session_id

    # Add context validation for session cookie authentication too
    validation_context = {
      ip_address: request.remote_ip,
      user_agent: request.user_agent
    }
    session_data = JwtSessionService.find_session(user_id, session_id, validate_context: validation_context)
    session_data&.[](:access_token)
  rescue => e
    Rails.logger.error "Failed to extract JWT from session cookie: #{e.message}"
    nil
  end

  # Decode and validate JWT token
  # Returns payload if valid, nil otherwise
  def decode_and_validate_token(token)
    payload = JwtService.decode(token)
    # JwtService.decode returns nil for invalid tokens, not raising exceptions
    # So we need to check if decoding failed
    unless payload
      Rails.logger.error "JWT decode error: Invalid or expired token"
      AuthHealthCheck.log_auth_event('jwt_decode_error', success: false, error: "Invalid or expired token")
    end
    payload
  end

  # Check if token has been revoked using the revocation strategy
  def token_revoked?(payload, user)
    revocation_strategy = JwtRevocationStrategy.new
    revocation_strategy.jwt_revoked?(payload, user)
  end

  # Find user from JWT payload
  def find_user_from_payload(payload)
    user_id = payload['user_id']
    return nil unless user_id
    
    User.find_by(id: user_id)
  end

  # Render unauthorized response
  def render_unauthorized(message = "Unauthorized")
    render json: { error: message }, status: :unauthorized
  end
  
  # Extract and set tenant (company) context from JWT payload
  # ENHANCED in Chunk 29: More robust tenant validation with security logging
  def set_tenant_from_jwt(payload, user)
    company_id = payload['company_id']
    
    # Store JWT-derived company_id for policy access
    @jwt_company_id = company_id
    
    if company_id.present?
      company = Company.find_by(id: company_id)
      
      # SECURITY: Enhanced tenant validation checks
      if company.nil?
        Rails.logger.error "JWT tenant validation failed: company_id=#{company_id} does not exist for user=#{user.id}"
        AuthHealthCheck.log_security_event('invalid_company_in_jwt', {
          severity: 'high',
          user_id: user.id,
          company_id: company_id,
          reason: 'company_not_found'
        })
        return false
      end
      
      # Check if user has active access to this company
      user_company_role = user.company_user_roles.active.find_by(company: company)
      unless user_company_role
        Rails.logger.error "JWT tenant validation failed: user=#{user.id} has no active role in company=#{company_id}"
        AuthHealthCheck.log_security_event('unauthorized_company_access', {
          severity: 'high',
          user_id: user.id,
          company_id: company_id,
          reason: 'no_active_role'
        })
        return false
      end
      
      # Set tenant context
      ActsAsTenant.current_tenant = company
      Rails.logger.info "JWT tenant context set: company_id=#{company_id} for user=#{user.id} with role=#{user_company_role.role.name}"
      
      # SECURITY: Log successful tenant context switch for audit trail
      AuthHealthCheck.log_security_event('tenant_context_set', {
        severity: 'low',
        user_id: user.id,
        company_id: company_id,
        role: user_company_role.role.name
      })
    else
      Rails.logger.info "JWT authentication: No company_id in token for user=#{user.id}"
      # No company_id in JWT - this might be valid for some scenarios
      # For example: during initial login before company selection,
      # or for users with access to multiple companies who haven't selected one yet.
      # The controller/policy layer should decide if tenant context is required for the specific action.
      # ActsAsTenant.current_tenant will remain nil in this case.
    end
    
    # Return true to indicate success (tenant was set or not required)
    true
  end

  # Helper to parse the jwt_session_id cookie consistently
  # Returns [user_id, session_id] or [nil, nil] if invalid
  # TODO: Remove old cookie format support once transition period is complete (see roadmap - Code Maintenance #1)
  # TODO: Add comprehensive test coverage for all validation scenarios (see roadmap #2)
  def parse_jwt_session_cookie(cookie_value)
    return [nil, nil] unless cookie_value.present?

    if cookie_value.is_a?(Hash)
      # New format: { user_id: X, session_id: Y }
      user_id = cookie_value['user_id'] || cookie_value[:user_id]
      session_id = cookie_value['session_id'] || cookie_value[:session_id]
      [user_id, session_id]
    else
      # Old format: session_id as string (backward compatibility)
      # Note: user_id derivation for old format may be unreliable
      session_id = cookie_value
      user_id = @current_jwt_user&.id || session[:user_id] || (respond_to?(:current_user) ? current_user&.id : nil)
      Rails.logger.debug "Old cookie format detected, derived user_id: #{user_id}"
      [user_id, session_id]
    end
  end
end