# app/controllers/concerns/jwt_login_helpers.rb
module JwtLoginHelpers
  extend ActiveSupport::Concern

  # Helper method to handle successful JWT login, token generation, and response
  def handle_successful_jwt_login(user, event_name:, start_time:, message: 'Login successful', status: :ok)
    # Generate JWT tokens
    payload = user.jwt_payload
    access_token = JwtService.encode_access_token(payload)
    refresh_token = JwtService.encode_refresh_token(payload)
  
    # Set refresh token in HttpOnly cookie for security
    refresh_expires_at = Time.current + JwtService::REFRESH_TOKEN_EXPIRATION_DURATION
    SecureCookieHelper.set_refresh_token_cookie(cookies, refresh_token, refresh_expires_at)
    
    # Create Redis session for page refresh persistence
    session_id = JwtSessionService.create_session(user, {
      access_token: access_token,
      refresh_token: refresh_token,
      company_id: user.primary_company&.id,
      ip_address: request.remote_ip,
      user_agent: request.user_agent
    })
  
    unless session_id
      Rails.logger.error "Failed to create JWT session during #{event_name} for user: #{user.id}"
      render json: { 
        error: 'Login successful but failed to create a session. Please try again.' 
      }, status: :internal_server_error
      return
    end
    
    # Set session ID in signed cookie with user_id for lookup
    cookies.signed[:jwt_session_id] = {
      value: { user_id: user.id, session_id: session_id },
      httponly: true,
      secure: Rails.env.production?,
      same_site: :strict,
      expires: refresh_expires_at
    }
    
    # Log successful event
    duration_ms = ((Time.current - start_time) * 1000).round
    AuthHealthCheck.log_auth_event(event_name, success: true, duration_ms: duration_ms)
    
    render json: {
      success: true,
      message: message,
      access_token: access_token,
      expires_in: JwtService::ACCESS_TOKEN_EXPIRATION_DURATION.to_i,
      user: {
        id: user.id,
        email: user.email,
        company_id: user.primary_company&.id
      }
    }, status: status
  end
end