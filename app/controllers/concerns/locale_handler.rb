module LocaleHandler
  extend ActiveSupport::Concern

  included do
    around_action :use_locale_from_cookie_for_api
  end

  private

  def use_locale_from_cookie_for_api
    # Skip for regular locale-scoped requests
    if params[:locale].present?
      yield
    else
      # For API requests without locale in URL
      locale = cookies[:locale] || I18n.default_locale
      I18n.with_locale(locale) { yield }
    end
  end
end