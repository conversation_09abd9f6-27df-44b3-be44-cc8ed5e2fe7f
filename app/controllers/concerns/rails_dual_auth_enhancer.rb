# Rails Dual Authentication Enhancer
# Ensures all Rails controllers have proper dual authentication support
# This concern can be included in controllers that need enhanced dual auth behavior

module RailsDualAuthEnhancer
  extend ActiveSupport::Concern

  included do
    # Ensure proper error handling for JSON requests in Rails controllers
    rescue_from ActionController::InvalidAuthenticityToken, with: :handle_invalid_authenticity_token
    
    # Add comprehensive logging for authentication attempts
    before_action :log_authentication_context, if: -> { Rails.logger.debug? }
  end

  private

  # Enhanced error handling for CSRF token issues in dual auth scenarios
  def handle_invalid_authenticity_token(exception)
    Rails.logger.warn "Invalid authenticity token in #{self.class.name}: #{exception.message}"
    
    respond_to do |format|
      format.html { 
        flash[:error] = t("flash.session_expired", default: "Session expired. Please log in again.")
        redirect_to spa_login_override_path(locale: I18n.locale) 
      }
      format.json { 
        render json: { 
          error: 'Invalid authenticity token', 
          authentication_required: true,
          controller: self.class.name.underscore
        }, status: :unauthorized 
      }
    end
  end

  # Log authentication context for debugging dual auth issues
  def log_authentication_context
    auth_method = if @current_jwt_user
      "JWT (user_id: #{@current_jwt_user.id})"
    elsif user_signed_in?
      "Session (user_id: #{current_user.id})"
    else
      "None"
    end

    Rails.logger.debug "#{self.class.name}##{action_name}: Auth method: #{auth_method}, Tenant: #{ActsAsTenant.current_tenant&.id}"
  end

  # Enhanced require_login that works with dual authentication
  def require_login_with_dual_auth
    # TODO: Clarify the specific use case for this method. 
    # ApplicationController#require_login already handles dual auth (JWT/Session)
    # and provides format-aware responses. This method might be redundant or
    # its distinct purpose (e.g., for controllers not inheriting from ApplicationController
    # or needing a very specific pre-ApplicationController auth step) should be documented.
    # Consider if its functionality can be merged or if it's truly needed as a separate option.
    
    if devise_password_reset_flow?
      return
    end

    # The dual authentication logic is already in ApplicationController
    # This method provides additional error handling and logging
    unless current_user
      Rails.logger.info "#{self.class.name}: Authentication failed - no current_user"
      
      respond_to do |format|
        format.html { 
          flash[:error] = t("flash.registration_needed", default: "Registration needed.")
          redirect_to spa_login_override_path(locale: I18n.locale) 
        }
        format.json { 
          render json: { 
            error: 'Authentication required', 
            controller: self.class.name.underscore,
            action: action_name
          }, status: :unauthorized 
        }
      end
    end
  end

  # Check if user has access to current tenant (company)
  def verify_tenant_access
    return unless current_user && ActsAsTenant.current_tenant

    unless current_user.companies.include?(ActsAsTenant.current_tenant)
      Rails.logger.warn "#{self.class.name}: User #{current_user.id} attempted access to unauthorized tenant #{ActsAsTenant.current_tenant.id}"
      
      respond_to do |format|
        format.html { 
          flash[:error] = t("flash.unauthorized_company", default: "You don't have access to this company.")
          redirect_to spa_dashboard_path(locale: I18n.locale) 
        }
        format.json { 
          render json: { 
            error: 'Unauthorized company access', 
            controller: self.class.name.underscore
          }, status: :forbidden 
        }
      end
    end
  end

  # Comprehensive authentication status for debugging
  def authentication_status
    {
      controller: self.class.name,
      action: action_name,
      current_user_id: current_user&.id,
      jwt_user_id: @current_jwt_user&.id,
      # Correctly fetch session user's ID, ensuring it's from a session-based sign-in
      # and not just reflecting @current_jwt_user if a session also happens to exist.
      session_user_id: (user_signed_in? && @current_jwt_user.nil? ? current_user.id : (user_signed_in? ? warden.user(scope: :user)&.id : nil)),
      tenant_id: ActsAsTenant.current_tenant&.id,
      request_format: request.format.symbol,
      timestamp: Time.current.iso8601
    }
  end
end