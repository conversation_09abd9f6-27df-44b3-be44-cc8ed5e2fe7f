class CronController < ApplicationController
  skip_before_action :require_login
  skip_before_action :set_current_tenant
  # skip_before_action :verify_authenticity_token
  # CSRF protection is disabled globally in ApplicationController via `skip_forgery_protection`.
  # The redundant `skip_before_action :verify_authenticity_token` was removed from here
  # as it caused errors during Rails' eager loading process.
  
  # Validate Render webhook token
  before_action :authenticate_webhook
  
  def daily_team_status
    TeamStatusReportService.send_daily_reports
    render json: { status: "success", message: "Daily team status reports sent" }
  end

  def close_orphaned_activities
    puts "🔍 DEBUG CronController#close_orphaned_activities - Starting cleanup process"
    closed_activities_count = 0
    failed_companies = []
    
    companies = Company.all
    puts "🔍 DEBUG CronController - Found #{companies.count} companies to process"
    
    Company.find_each do |company|
      puts "🔍 DEBUG CronController - Processing company: #{company.name} (ID: #{company.id})"
      begin
        ActsAsTenant.with_tenant(company) do
          closed_activities = DailyActivityManager::OrphanedActivityCloser.call
          closed_activities_count += closed_activities.size
          puts "🔍 DEBUG CronController - Company #{company.name}: closed #{closed_activities.size} activities"
        end
      rescue StandardError => e
        puts "🔍 DEBUG CronController - ERROR for company #{company.name}: #{e.message}"
        Rails.logger.error "Failed to close orphaned activities for company #{company.id}: #{e.message}"
        failed_companies << company.id
      end
    end
    
    puts "🔍 DEBUG CronController - Total closed activities: #{closed_activities_count}"
    puts "🔍 DEBUG CronController - Failed companies: #{failed_companies}"
    
    render json: { 
      status: "success", 
      message: "Orphaned activities cleanup completed",
      closed_activities: closed_activities_count,
      failed_companies: failed_companies
    }
  end

  # Create a new "Cron Job"
  # Set the schedule to 30 7 * * * (7:30 AM UTC, which is 8:30 AM CET in standard time)
  # For the command, use: curl -X POST https://your-app.onrender.com/cron/daily_team_status -H "webhook-signature: ${RENDER_WEBHOOK_SECRET}"
  
  private
  
  def authenticate_webhook
    payload = request.raw_post
    # signature = request.headers['Render-Webhook-Signature']
    signature = request.headers['webhook-signature']
    # Header Name Discrepancy: The official Render documentation states the header name is webhook-signature.
    # Your code uses Render-Webhook-Signature. You should update your code to use request.headers['webhook-signature'].
    # https://render.com/docs/webhooks
    
    # Get the webhook secret from environment
    webhook_secret = Rails.application.credentials.dig(:render, :webhook_secret)
    # webhook_secret = Rails.application.credentials.render[:webhook_secret]
    
    # Calculate expected signature
    expected_signature = OpenSSL::HMAC.hexdigest('sha256', webhook_secret, payload)
    puts "Expected Signature: #{expected_signature}"
    puts "Received Signature: #{signature}"
    
    
    unless ActiveSupport::SecurityUtils.secure_compare(expected_signature, signature)
      render json: { error: "Unauthorized" }, status: :unauthorized
    end
  end
end