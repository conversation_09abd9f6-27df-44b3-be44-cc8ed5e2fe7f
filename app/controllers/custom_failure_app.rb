class CustomFailureApp < Devise::FailureApp
  def redirect_url
    if request.xhr? || request.format.json?
      # For AJAX/JSON requests, don't redirect
      nil
    else
      # For HTML requests, redirect to SPA login with locale
      spa_login_path(locale: I18n.locale)
    end
  end

  def respond
    if request.xhr? || request.format.json?
      # Return 401 for AJAX/JSON requests
      self.status = 401
      self.content_type = 'application/json'
      self.response_body = { error: 'Authentication required' }.to_json
    else
      # Redirect to SPA login for HTML requests
      redirect
    end
  end
end