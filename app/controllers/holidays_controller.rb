class HolidaysController < ApplicationController
  # Authentication handled by ApplicationController's require_login

  LOCALE_TO_COUNTRY = {
    'cs' => 'CZ',
    'sk' => 'SK',
    'pl' => 'PL',
  }.freeze

  COUNTRY_TO_LANGUAGE = {
    'CZ' => 'CS',
    'SK' => 'SK',
    'PL' => 'PL',
  }.freeze


  def index
    year = params[:year].present? ? params[:year].to_i : Date.current.year
    start_date = Date.new(year, 1, 1)
    end_date = Date.new(year, 12, 31)
    
    country = current_user.user_setting&.country_code || to_country(I18n.locale) || 'CZ'

    @holidays = Holiday.for_country(country)
                       .for_date_range(start_date, end_date)
                       .order(:date)

    respond_to do |format|
      format.html
      format.json { render json: @holidays }
    end
  end

  def sync
    # Extract year from request body, default to current year if not present
    year = params[:year].present? ? params[:year].to_i : Date.current.year
    
    # COuntry is the basic paramater - user has a control which one to load
    country = current_user.user_setting&.country_code || to_country(I18n.locale) || 'CZ'

    # But language has to be loaded according to the country, NOT locale
    # Because it will sync into locale language for ALL users
    # Remember, this is override to save API calls, but we do not intend to backup the whole CEE 
    # holiday API with all the languages and countries
    language = to_language(country) || 'CS'
    
    HolidaySyncService.new(country: country, language: language, year: year).sync

    respond_to do |format|
      format.html { redirect_to holidays_path(year: year, country: country), notice: holidays_t('messages.sync_success', country: country, year: year) }
      format.json { render json: { status: 'success', year: year, country: country } }
    end
  rescue StandardError => e
    
    Rails.logger.error "Holiday sync failed for country #{country}, year #{year}: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    
    respond_to do |format|
      format.html { redirect_to holidays_path(year: year, country: country), alert: holidays_t('errors.sync_failed', country: country, year: year) }
      format.json { render json: { status: 'error', message: e.message }, status: :internal_server_error }
    end
  end

  private

  # Helper method for controller-specific translations
  # Makes translation calls cleaner and more maintainable
  def holidays_t(key, **options)
    t("controllers.holidays.#{key}", **options)
  end

  def user_set_locale
    I18n.locale = current_user.locale if current_user.present?
  end

  def to_country(locale)
    LOCALE_TO_COUNTRY[locale.downcase]
  end

  def to_language(country)
    COUNTRY_TO_LANGUAGE[country]
  end

end 