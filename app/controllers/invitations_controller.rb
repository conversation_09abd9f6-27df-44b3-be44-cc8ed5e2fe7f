class InvitationsController < ApplicationController
  # Authentication handled by ApplicationController's require_login
  
  def index
    # Uses the built-in Devise Invitable method to check for pending invitations
    @pending_invitation = current_user.invitation_sent_at.present? && 
                          current_user.invitation_accepted_at.nil?
  end
  
  def create
    handler = InvitationHandler.new(
      email: invitation_params[:email],
      company: current_company,
      sender: current_user,
      params: invitation_params
    )
    
    result = handler.process
    
    if result[:success]
      redirect_to invitations_path, notice: result[:message]
    else
      redirect_back fallback_location: root_path, 
                   alert: result[:errors].join(', ')
    end
  end

  private

  def invitation_params
    params.require(:invitation).permit(:email, :first_name, :last_name)
  end

  def current_company
    ActsAsTenant.current_tenant
  end

end