class MainboxController < ApplicationController
  include ActionPolicy::Controller
  include RailsDualAuthEnhancer

  before_action :check_current_company
  before_action :permitted_user?
  before_action :verify_tenant_access
  # Authentication handled by ApplicationController's require_login
  
  # TODO: security, check and add security layers if needed
  # skip_before_action :set_current_tenant, only: [:show, :switch_company]

  # TODO: dailylog
  # TODO: calendar upcomming events
  # TODO: notifications
 
  # Every logged user is allowed to view the mainbox -- authenticate_user
  # User has to be logged in or to view the company -- permitted_user? -- REDUNDAND?
  # Company has to be set and if empty, user sees only basic data -- check_current_company
  # User can see limited data based on his role in the company --allowed to conditions
  # NOT setting tenant with ActsAsTenant, as this has to be controlled logic for each specific instance

  # TODO: Notifications into the landing page for the user
  # TODO: Urgent: redo mainbox to components and proper role user rights
  def show
    @company= ActsAsTenant.current_tenant
    if @company
      #authorize! @company

      # Set permissions for different sections
      # Booleans set based on roles.
      # These variables can now be used in the view to determine what to display.
      @permissions = {
        view_owner: allowed_to?(:view_owner_section?, @company),
        view_employee: allowed_to?(:view_employee_section?, @company)
      } 
      # @can_view_owner_section = allowed_to?(:view_owner_section?, @company)
      # @can_view_employee_section = allowed_to?(:view_employee_section?, @company)

      #Any authorized user can see this
      @companies = current_user.company_user_roles.includes(:company, :role).references(:company, :role).joins(:company, :role)
      
    #   #Used to switch between companies.
    #   @user_companies = current_user.companies 
      
      #Only owners or supervisors can see the owner section.
      @contracts = @permissions[:view_owner] ? @company.contracts : []

    #   @daily_log = @company.daily_logs.where('end_time IS NULL').first
    else
      @invitations_pending = Invitation.where(email: current_user.email, status: 'pending')
      @pending_contracts = Contract.where(email: current_user.email, user_id: nil)
    end

  end

  # # NOTE: Temp action for free users. Not available in freemium. 
  # # TODO: Test if able to switch to not authorized company
  # def switch_company
  #   #ActsAsTenant.without_tenant do
  #     company = current_user.companies.find_by(id: params[:company_id])
  #     if company
  #       #session[:tenant_id] = company.id
  #       #ActsAsTenant.current_tenant = company
  #       redirect_to root_path, notice: "Switched to company: #{company.name}"
  #     else
  #       redirect_to root_path, alert: "Company not found or not authorized."
  #     end
  #   #end
  # end

  private
  
  def check_current_company
    # TODO: This method appears to be a no-op as its logic is commented out.
    # ActsAsTenant.current_tenant (set by ApplicationController and verified by
    # RailsDualAuthEnhancer#verify_tenant_access) should be the source of truth for company context.
    # Consider removing this method if it's no longer needed.
    if current_user.present?
      if current_user.companies.present?
        #@company = current_user.companies.first
      end
    end
  end

  def permitted_user?
    # TODO: Review the necessity of this method. ApplicationController#require_login
    # (which runs by default) should ensure current_user is present.
    # If require_login is skipped for certain actions in this controller, this check might be valid.
    # Otherwise, it might be redundant.
    if !current_user.present?
      flash.clear
      #redirect_to root_path, alert: "Wrong address or user."
    end
  end
  
  # def switch_company_params
  #   params.permit(:company_id)
  # end

end