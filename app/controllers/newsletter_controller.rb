require "resend"

class NewsletterController < ApplicationController
  skip_before_action :require_login
  # skip_before_action :verify_authenticity_token, only: [:subscribe]
  # CSRF protection is disabled globally in ApplicationController via `skip_forgery_protection`.
  # The redundant `skip_before_action :verify_authenticity_token` was removed from here
  # as it caused errors during Rails' eager loading process.
  def subscribe
    # Handle both direct email param and nested param structure
    email_param = params[:newsletter] && params[:newsletter][:email] || params[:email]

    unless email_param =~ URI::MailTo::EMAIL_REGEXP
      render json: { success: false, error: 'Neplatný email' }, status: :unprocessable_entity
      return
    end

    begin
      # Get credentials
      api_key = Rails.application.credentials.dig(:resend, :audience_api_key)
      audience_id = Rails.application.credentials.dig(:resend, :audience_id)
      
      # Set the API key globally as in the docs
      Resend.api_key = api_key
      
      # Prepare params hash exactly as shown in docs
      contact_params = {
        email: email_param,
        audience_id: audience_id,
        unsubscribed: false
      }
      
      # Call create directly on Resend::Contacts
      response = Resend::Contacts.create(contact_params)
      
      # if response && response.id
      if response.response.code == "201" 
        render json: { success: true }
      else
        error_message = response && response.message || 'Chyba při přihlášení'
        render json: { success: false, error: error_message }, 
               status: :unprocessable_entity
      end
    rescue => e
      Rails.logger.error("Resend API error: #{e.class.name}: #{e.message}")
      Rails.logger.error(e.backtrace.join("\n")) if e.backtrace
      render json: { success: false, error: 'Služba není dostupná' }, 
             status: :internal_server_error
    end
  end
end