class PublicBookingsController < ApplicationController
  skip_before_action :require_login
  before_action :find_booking_link, only: [:show, :create, :calendar]
  before_action :check_subscription

  def show
    if @booking_link.active?
      render :show, layout: 'tymlink'
    else
      render json: { error: public_bookings_t('errors.booking_link_inactive') }, status: :forbidden
    end
  end

  def calendar
    start_date = params[:month] ? Date.parse(params[:month]) : Date.current.beginning_of_month
    end_date = start_date.end_of_month

    # Fetch holidays upfront with country context
    # Get country code with fallbacks:
    # 1. User's setting
    # 2. Map from I18n.locale
    # 3. CZ as default
    locale_to_country = {
      cs: 'CZ',
      sk: 'SK',
    }
    
    country_code = current_user&.user_setting&.country_code || 
                  locale_to_country[I18n.locale] || 
                  'CZ'
                  
    holidays_in_range = Holiday.for_country(country_code)
                              .where(date: start_date..end_date)
                              .pluck(:date)
                              .to_set

    # 1. Fetch Booking Counts
    booking_counts = @booking_link.bookings
                                  .where(preferred_date: start_date..end_date)
                                  .where.not(status: 'cancelled')
                                  .group(:preferred_date, :preferred_period)
                                  .count

    # 2. Fetch Work Counts (if needed)
    work_counts = {}
    if @booking_link.include_works_in_count
      work_counts = @booking_link.company.works
                                   .where(scheduled_start_date: start_date..end_date)
                                   .where.not(status: ['cancelled', 'completed'])
                                   .where(booking_id: nil)
                                   .group(:scheduled_start_date, :preferred_period)
                                   .count
    end

    # 3. Combine Counts into a daily structure
    daily_counts = Hash.new { |h, k| h[k] = { 'morning' => 0, 'afternoon' => 0 } }

    booking_counts.each do |(date, period), count|
      daily_counts[date.to_s][period] += count if period.present?
    end

    work_counts.each do |(date, period), count|
      daily_counts[date.to_s][period] += count if period.present?
    end

    # 4. Build calendar days using pre-fetched data
    calendar_days = (start_date..end_date).map do |date|
      date_key = date.to_s
      current_day_counts = daily_counts[date_key]
      morning_count = current_day_counts['morning']
      afternoon_count = current_day_counts['afternoon']
      total_daily_count = morning_count + afternoon_count

      # Use booking link limits (convert to_i for safe comparison)
      morning_limit = @booking_link.morning_limit.to_i
      afternoon_limit = @booking_link.afternoon_limit.to_i
      daily_limit = @booking_link.daily_limit.to_i

      # Check period limits
      morning_period_full = morning_limit > 0 && morning_count >= morning_limit
      afternoon_period_full = afternoon_limit > 0 && afternoon_count >= afternoon_limit

      # Check daily limit
      daily_limit_full = daily_limit > 0 && total_daily_count >= daily_limit

      # Base availability checks
      morning_available_base = !morning_period_full && !daily_limit_full
      afternoon_available_base = !afternoon_period_full && !daily_limit_full
      day_available_base = !daily_limit_full 

      # Check if date is a holiday using pre-fetched set
      is_holiday = holidays_in_range.include?(date)
      
      # Final availability considering holidays
      morning_available = morning_available_base
      afternoon_available = afternoon_available_base
      day_available = day_available_base

      # If it's a holiday and book_holidays is false, mark as unavailable
      if is_holiday && !@booking_link.book_holidays
        morning_available = false
        afternoon_available = false
        day_available = false
      end
      
      {
        date: date_key,
        day: date.day,
        is_today: date == Date.current,
        is_weekend: [0, 6].include?(date.wday),
        is_holiday: is_holiday,
        # Pass pre-calculated total count and day availability
        availability: date_availability(date, total_daily_count, day_available), 
        periods: {
          morning: {
            count: morning_count,
            # A period is available only if the period itself AND the day as a whole are available
            available: morning_available 
          },
          afternoon: {
            count: afternoon_count,
            # A period is available only if the period itself AND the day as a whole are available
            available: afternoon_available 
          }
        }
      }
    end
    
    calendar_data = {
      company_name: @booking_link.company.name,
      company_slug: @booking_link.company.slug,
      company_web: @booking_link.company.web,
      company_description: @booking_link.company.description,
      company_phone: @booking_link.company.phone,
      name: @booking_link.name,
      description: @booking_link.description,
      duration: @booking_link.duration,
      is_remote: @booking_link.is_remote,
      location_required: @booking_link.location_required,
      location: @booking_link.location,
      book_holidays: @booking_link.book_holidays,
      month: {
        name: start_date.strftime('%B %Y'),
        start_date: start_date.to_s,
        end_date: end_date.to_s
      },
      days: calendar_days
    }
    
    render json: calendar_data
  end

  def create
    booking = @booking_link.bookings.new(booking_params)
    booking.company_id = @booking_link.company_id
    booking.status = 'pending'

     # Set default location if not required
    unless @booking_link.location_required
      if @booking_link.is_remote || @booking_link.location.present?
        booking.location = @booking_link.location.presence || "online"
      end
    end

    # Check if the booking is still available -- no need to do that in the controller
    date = booking.preferred_date
    period = booking.preferred_period

    if !@booking_link.available_for_booking?(date, period)
      return render json: { 
        success: false, 
        errors: [public_bookings_t('errors.time_slot_unavailable')] 
      }, status: :unprocessable_entity
    end

    if booking.save
      BookingMailer.confirmation_email(booking).deliver_now if booking.client_email.present?
      BookingMailer.booking_notification(booking, :new, company_owners_emails).deliver_now
      render json: { success: true, message: public_bookings_t('messages.booking_created') } 
    else
      render json: { success: false, errors: booking.errors.full_messages }, status: :unprocessable_entity
    end
  end

  # New actions for booking management via token
  def show_by_token
    @booking = Booking.find_by(access_token: params[:token])
    
    if @booking && valid_token?(@booking)
      @company = @booking.company
      render json: { 
        success: true, 
        booking: @booking.as_json(include: :booking_link)
      }
    else
      render json: { error: public_bookings_t('errors.invalid_token') }, status: :forbidden
    end
  end

  def manage
    @booking = Booking.find_by(access_token: params[:token])
    puts "\n --------manage params #{params[:token]} \n\n "
    puts "\n --------manage booking #{@booking.inspect} \n\n " 
    
    if @booking && valid_token?(@booking)
      puts "\n --------VALID_TOKEN #{valid_token?(@booking)} \n\n "
      @company = @booking.company
      @booking_link = @booking.booking_link
      #FIXME: this was about to be manage for the unregistered user managing only his own reservation. Is there a reason why 
      # tenant scope?
      # ActsAsTenant.current_tenant = @company
      render :show, layout: 'tymlink'
    else
      render json: { error: public_bookings_t('errors.invalid_token') }, status: :forbidden
    end
  end

  def update_booking
    @booking = Booking.find_by(access_token: params[:token])
    
    if @booking && valid_token?(@booking)
      if @booking.status == "cancelled"
        render json: { success: false, error: public_bookings_t('errors.cancelled_booking_not_editable') }, status: :unprocessable_entity
        return
      end

      @booking.public_update = true

      if @booking.status == 'confirmed'
        @booking.status = 'rescheduled'
      end

      if @booking.update(booking_update_params)
        BookingMailer.booking_notification(@booking, :update, company_owners_emails).deliver_now
        render json: { success: true, booking: @booking, message: public_bookings_t('messages.booking_updated') }
      else
        puts @booking.errors.inspect
        render json: { success: false, errors: @booking.errors.full_messages }, status: :unprocessable_entity
      end
    else
      render json: { error: public_bookings_t('errors.invalid_token') }, status: :forbidden
    end
  end

  def cancel_booking
    @booking = Booking.find_by(access_token: params[:token])
    
    if @booking && valid_token?(@booking)
      if @booking.cancel
        BookingMailer.booking_notification(@booking, :cancel, company_owners_emails).deliver_now
        render json: { success: true, booking: @booking }
      else
        render json: { success: false, errors: @booking.errors.full_messages }, status: :unprocessable_entity
      end
    else
      render json: { error: public_bookings_t('errors.invalid_token') }, status: :forbidden
    end
  end



  
  private

  # Helper method for controller-specific translations
  # Makes translation calls cleaner and more maintainable
  def public_bookings_t(key, **options)
    t("controllers.public_bookings.#{key}", **options)
  end

  def company_owners_emails
    @company.company_user_roles.joins(:role, :user).where(roles: { name: 'owner' }).pluck('users.email')
  end

  def check_subscription
    @company = Company.find_by!(slug: params[:company_slug])
    
    # FIXME: this is a temporary solution to check if the company has a plus or premium plan
    # FIXME: this should be replaced with the proper Action Policy
    unless @company.current_plan&.name.in?(%w[plus premium])
      render json: { error: public_bookings_t('errors.subscription_required') }, status: :forbidden
    end
  end

  def find_booking_link
    puts "/n--------- #{params.inspect} /n/n "
    @company = Company.find_by!(slug: params[:company_slug])
    @booking_link = @company.booking_links.find_by!(slug: params[:slug])
    ActsAsTenant.current_tenant = @company
  end

  def date_availability(date, booking_count, day_available)
    return 'unavailable' if date < Date.current
    
    # Use I18n.l to get the properly capitalized day name that matches the database format
    day_name = I18n.l(date, format: '%A')
    
    if @booking_link.preferred_days.present? && !@booking_link.preferred_days.include?(day_name)
      return 'unavailable'
    end

    return 'unavailable' unless day_available
    
    if @booking_link.daily_limit.present?
      daily_limit = @booking_link.daily_limit
      percentage_filled = (booking_count.to_f / daily_limit) * 100

      if percentage_filled >= 90
        'busy'
      elsif percentage_filled >= 60
        'limited'
      else
        'available'
      end
    else
      if booking_count >= 3
        'busy'
      elsif booking_count >= 2
        'limited'
      else
        'available'
      end
    end

  end

  def valid_token?(booking)
    booking.token_generated_at && booking.token_generated_at > 72.hours.ago
  end
  
  def booking_params
    params.require(:booking).permit(
      :client_name,
      :client_email,
      :client_phone,
      :preferred_date,
      :preferred_period,
      :message,
      :specific_time,
      :duration,
      :location
    )
  end

  def booking_update_params
    params.require(:booking).permit(
      :preferred_date,
      :preferred_period,
      :specific_time
    )
  end
  
end