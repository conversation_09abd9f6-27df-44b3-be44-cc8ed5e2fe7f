class SubscriptionsController < ApplicationController
  
  include ActionPolicy::Controller
  
  before_action :set_tenant_company
  # Authentication handled by ApplicationController's require_login except for process_subscription_request
  skip_before_action :require_login, only: [:process_subscription_request]

  def create
    authorize! @company, to: :manage_subscription?
    @plan = Plan.find(params[:plan_id])

    @subscription = @company.subscriptions.build(
      plan: @plan,
      start_date: Date.today,
      expire_date: Date.today + @plan.duration.days,
      status: 'active'
    )

    if @subscription.save
      flash[:notice] = 'Úspěšná registrace!'
      redirect_to root_path
    else
      flash[:alert] = 'Registrace nebyla ú<PERSON>éšná!'
      redirect_to root_path
    end
  end

  def activate_trial
    authorize! @company, to: :manage_subscription?

    if Subscription.can_activate_trial?(current_user, @company)
      subscription = Subscription.activate_trial(current_user, @company)
      
      if subscription
        render json: { 
          success: true, 
          message: 'Zkušební verze Plus byla úspěšně aktivována.',
          subscription: {
            plan_name: 'plus',
            expire_date: subscription.expire_date
          }
        }
      else
        render json: { 
          success: false, 
          message: 'N<PERSON>oda<PERSON><PERSON> se aktivovat zkušební verzi.',
          messageType: 'error' 
        }, status: :unprocessable_entity
      end
    else
      render json: { 
        success: false, 
        message: 'Již jste využili zkušební verzi pro tento pracovní prostor.',
        messageType: 'error' 
      }, status: :unprocessable_entity
    end
  end

  def request_subscription
    @tier = params[:tier]
    @subscription_request = SubscriptionRequest.new(tier: @tier)
    
    respond_to do |format|
      format.js
    end
  end

  def process_subscription_request
    @subscription_request = SubscriptionRequest.new(subscription_request_params)
    
    if @subscription_request.valid?
      # Send notification email to admin
      AdminMailer.subscription_order(@subscription_request).deliver_now
      
      render json: { success: true }, status: :ok
    else
      render json: { success: false, errors: @subscription_request.errors }, status: :unprocessable_entity
    end
  end

  private

  def set_tenant_company
    @company = ActsAsTenant.current_tenant
  end

  def subscription_request_params
    params.require(:subscription_request).permit(:company_name, :company_id, :email, :additional_info, :tier, :billing_period)
  end

end