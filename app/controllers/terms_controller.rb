class TermsController < ApplicationController
  
  skip_before_action :require_login
  skip_before_action :set_current_tenant

  def show
    locale = params[:locale] || I18n.locale
    type = params[:type]
    
    # Determine template to render based on type and locale
    template_to_render = nil
    
    case type
    when 'tos'
      if locale == 'sk'
        template_to_render = 'terms/tos.sk'
      else
        # Default to Czech (no suffix)
        template_to_render = 'terms/tos'
      end
    when 'gdpr'
      if locale == 'sk'
        template_to_render = 'terms/gdpr.sk'
      else
        # Default to Czech (no suffix)
        template_to_render = 'terms/gdpr'
      end
    else
      head :not_found
      return
    end
    
    # Render the template content
    begin
      content = render_to_string(template_to_render, layout: false)
      
      respond_to do |format|
        format.html { render template_to_render, layout: false }
        format.json { render json: content }
      end
    rescue ActionView::MissingTemplate => e
      head :not_found
    end
  end
end