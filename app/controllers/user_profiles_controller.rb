class UserProfilesController < ApplicationController
  # Authentication handled by ApplicationController's require_login
  before_action :set_user_profile, only: [:show, :edit, :update]

  def show
    # Respond with the user profile data as JSON
    render json: { user_profile: @user_profile.as_json(only: [:first_name, :last_name, :title_prefix, :title_suffix, :location]) }
  end

  def edit
    # This might still be used for server-rendered views, keep it for now
    # @user_profile is already set by before_action
  end

  def update
    # @user_profile is already set by before_action

    if @user_profile.update(user_profile_params)
      render json: { success: true, user_profile: @user_profile.as_json(only: [:first_name, :last_name, :title_prefix, :title_suffix, :location]), message: user_profiles_t('messages.updated') }, status: :ok
    else
      render json: { success: false, errors: @user_profile.errors.full_messages }, status: :unprocessable_entity
    end
  end


  private

  # Helper method for controller-specific translations
  # Makes translation calls cleaner and more maintainable
  def user_profiles_t(key, **options)
    t("controllers.user_profiles.#{key}", **options)
  end

  def set_user_profile
    @user_profile = current_user.user_profile
    unless @user_profile
      render json: { error: user_profiles_t('errors.not_found') }, status: :not_found
    end
  end

  def user_profile_params
    params.require(:user_profile).permit(:first_name, :last_name, :title_prefix, :title_suffix, :location)
  end
end
  