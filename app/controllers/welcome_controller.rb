class WelcomeController < ApplicationController
  #skip_before_action :set_current_tenant, only: [:index]
  #skip_before_action :ensure_tenant_set
  skip_before_action :require_login

  def index
  end

  def features
  end

  def pricing
    render layout: 'welcome', template: 'welcome/pricing'
  end

  def mobile
  end  

  def mobilni_dochazka_bez_instalace
  end

  def online_dochazka_pro_male_firmy
  end

  def online_dochazka_zdarma  
  end

  def dochazka_bez_skoleni
  end

  def ztracite_cas_administrativou
  end

end
