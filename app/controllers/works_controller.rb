class WorksController < ApplicationController
  include ActionPolicy::Controller
  
  # TODO: FUTURE POLICY REFACTORING
  # Currently using manage_works? for all actions (fetch, assigned, today, create)
  # Should introduce view_works? policy for read-only operations (assigned, today)
  # and reconsider where manage_works? is too permissive
  # See POLICIES.md for policy definitions and requirements
  
  # Authentication handled by ApplicationController's require_login
  before_action :set_tenant_company
  before_action :set_contract
  before_action :set_work, only: [:show, :update, :destroy, :take_assignment, :leave_assignment]

  def index
  end

   def fetch
    authorize! @company, to: :manage_works?
    @works = @company.works.order(created_at: :desc).includes(:work_assignments)
    render json: @works.as_json(include: { 
      work_assignments: { 
        include: { contract: { only: [:id, :first_name, :last_name] } } 
      } 
    })
  end
  
  def assigned
    # Returns only works assigned to current user
    authorize! @company, to: :manage_works?

    if @contract
      @works = @company.works
        .joins(:work_assignments)
        .where(work_assignments: { contract_id: @contract.id })
        .order(created_at: :desc)
        .includes(:work_assignments)

      render json: @works.as_json(include: {
        work_assignments: {
          include: { contract: { only: [:id, :first_name, :last_name] } }
        }
      })
    else
      render json: []
    end
  end

  def today
    # Returns only works assigned to current user starting today or later
    authorize! @company, to: :manage_works?

    unless @contract
      return render json: []
    end

    today_date = Date.current
    works_with_assignments = @company.works.joins(:work_assignments)
    user_assigned_works = works_with_assignments.where(work_assignments: { contract_id: @contract.id })

    @works = user_assigned_works
      .where('scheduled_start_date >= ?', today_date)
      .where(status: ['scheduled', 'in_progress', 'unprocessed'])
      .order(:scheduled_start_date, :created_at)
      .includes(:work_assignments)

    render json: @works.as_json(include: {
      work_assignments: {
        include: { contract: { only: [:id, :first_name, :last_name] } }
      }
    })
  end

  def show
    authorize! @work
    render json: @work.as_json(include: { 
      work_assignments: { 
        include: { contract: { only: [:id, :first_name, :last_name] } } 
      } 
    })
  end

  # Authorization: Any employee, supervisor and owner can fully manage the Work
  def create
    authorize! @company, to: :manage_works?

    # Placeholder for the future paid features
    if params[:work][:is_recurring] && !allowed_to?(:can_create_recurring_works?, @work)
      return render json: { error: works_t('errors.recurring_premium_required') }, status: :forbidden
    end

    @work = @company.works.build(work_params)
    
    ActiveRecord::Base.transaction do
      if @work.save
        # Handle initial assignments
        if params[:assigned_contracts].present?
          valid_contract_ids = @company.contracts.where(id: params[:assigned_contracts]).pluck(:id)
          
          # Ensure at least one assignment
          if valid_contract_ids.empty?
            @work.errors.add(:base, works_t('errors.at_least_one_assignment'))
            raise ActiveRecord::Rollback
          end
          
          # Create assignments
          valid_contract_ids.each_with_index do |contract_id, index|
            assignment = @work.work_assignments.create!(
              contract_id: contract_id,
              company_id: @company.id,
              role: params[:assignment_role] || "worker",
              is_lead: index == 0 # First assignment is lead
            )
            
            # Create notification for each assignment
            # NotificationService.create_work_assignment_notification(assignment, 'added') if assignment.persisted?
          end
        else
          # If no assignments provided, assign to creator by default
          @work.work_assignments.create!(
            contract_id: @contract.id,
            company_id: @company.id,
            role: "worker",
            is_lead: true
          )
        end
        
        render json: @work.as_json(include: { 
          work_assignments: { 
            include: { contract: { only: [:id, :first_name, :last_name] } } 
          } 
        }), status: :created
      else
        render json: { errors: @work.errors.full_messages }, status: :unprocessable_entity
      end
    end
  rescue ActiveRecord::Rollback
    render json: { errors: @work.errors.full_messages }, status: :unprocessable_entity
  end

  def update
    authorize! @work
    
    # Handle assignment validation before transaction
    if params.key?(:assigned_contracts)
      requested_ids = params[:assigned_contracts] || []
      valid_contract_ids = @company.contracts.where(id: requested_ids).pluck(:id)
      
      # Prevent removing all assignments
      if valid_contract_ids.empty?
        return render json: { errors: [works_t('errors.at_least_one_assignment')] }, status: :unprocessable_entity
      end
    end
    
    # Start a transaction to handle both work update and assignment changes
    ActiveRecord::Base.transaction do
      if @work.update(work_params)
        # Handle assignment updates
        if params.key?(:assigned_contracts)
          # Re-fetch to ensure we have the latest data
          valid_contract_ids = @company.contracts.where(id: params[:assigned_contracts] || []).pluck(:id)
          
          # Get current assignment IDs
          current_assignment_ids = @work.work_assignments.pluck(:contract_id)
          
          # Determine which assignments to add and remove
          to_add = valid_contract_ids - current_assignment_ids
          to_remove = current_assignment_ids - valid_contract_ids
          
          # Remove assignments that are no longer selected
          if to_remove.any?
            @work.work_assignments.where(contract_id: to_remove).destroy_all
          end
          
          # Add new assignments
          to_add.each do |contract_id|
            @work.work_assignments.create!(
              contract_id: contract_id,
              company_id: @company.id,
              role: params[:assignment_role] || "worker",
              is_lead: false
            )
          end
          
          # Update lead assignment if needed (first one becomes lead if no lead exists)
          if @work.work_assignments.where(is_lead: true).none? && @work.work_assignments.any?
            @work.work_assignments.first.update!(is_lead: true)
          end
        end
        
        # Reload to get fresh data with all associations
        @work.reload
        
        render json: @work.as_json(include: { 
          work_assignments: { 
            include: { contract: { only: [:id, :first_name, :last_name] } } 
          } 
        })
      else
        render json: { errors: @work.errors.full_messages }, status: :unprocessable_entity
      end
    end
  rescue => e
    Rails.logger.error "Work update error: #{e.message}"
    render json: { errors: [works_t('errors.update_failed')] }, status: :unprocessable_entity
  end

  def destroy
    authorize! @work
    if @work.destroy
      head :no_content
    else
      render json: { errors: works_t('errors.delete_failed') }, status: :unprocessable_entity
    end
  end
  
  # Take assignment - add current user to work assignments
  def take_assignment
    authorize! @work
    
    # Check if already assigned
    existing_assignment = @work.work_assignments.find_by(contract: @contract)
    if existing_assignment
      return render json: { error: works_t('errors.already_assigned') }, status: :unprocessable_entity
    end
    
    assignment = @work.work_assignments.build(
      contract: @contract,
      company: @company,
      role: "worker",
      is_lead: false
    )
    
    if assignment.save
      # Create notification for assignment
      # NotificationService.create_work_assignment_notification(assignment, 'added')
      
      render json: { 
        message: works_t('messages.assignment_taken'),
        work: @work.as_json(include: { 
          work_assignments: { 
            include: { contract: { only: [:id, :first_name, :last_name] } } 
          } 
        })
      }
    else
      render json: { errors: assignment.errors.full_messages }, status: :unprocessable_entity
    end
  end
  
  # Leave assignment - remove current user from work assignments
  def leave_assignment
    authorize! @work
    
    assignment = @work.work_assignments.find_by(contract: @contract)
    unless assignment
      return render json: { error: works_t('errors.not_assigned') }, status: :not_found
    end
    
    # Prevent last user from leaving
    if @work.work_assignments.count == 1
      return render json: { error: works_t('errors.cannot_leave_last') }, status: :unprocessable_entity
    end
    
    if assignment.destroy
      # Create notification for removal
      # NotificationService.create_work_assignment_notification(assignment, 'removed')
      
      render json: { 
        message: works_t('messages.assignment_left'),
        work: @work.as_json(include: { 
          work_assignments: { 
            include: { contract: { only: [:id, :first_name, :last_name] } } 
          } 
        })
      }
    else
      # Return the specific error message from the model
      error_message = assignment.errors.full_messages.first || works_t('errors.leave_failed')
      render json: { error: error_message }, status: :unprocessable_entity
    end
  end

  private

  def set_tenant_company
    puts "=== SET_TENANT_COMPANY DEBUG START ==="
    puts "ActsAsTenant.current_tenant: #{ActsAsTenant.current_tenant&.inspect}"
    @company = ActsAsTenant.current_tenant
    puts "Set @company to: #{@company&.inspect}"
    puts "=== SET_TENANT_COMPANY DEBUG END ==="
  end

  def set_contract
    puts "=== SET_CONTRACT DEBUG START ==="
    puts "Current user: #{current_user&.inspect}"
    puts "Current user email: #{current_user&.email}"
    puts "Company: #{@company&.inspect}"
    puts "Company ID: #{@company&.id}"
    puts "ActsAsTenant.current_tenant: #{ActsAsTenant.current_tenant&.inspect}"
    puts "Current user contracts count: #{current_user&.contracts&.count}"
    puts "Current user contracts: #{current_user&.contracts&.pluck(:id, :company_id, :email, :status)}"
    
    @contract = current_user.contracts.find_by(company: @company)
    puts "Found contract: #{@contract&.inspect}"
    puts "=== SET_CONTRACT DEBUG END ==="
    
    unless @contract
      Rails.logger.warn "[🔧 CONTRACT] No contract found for user #{current_user&.email} in company #{@company&.id}"
      Rails.logger.warn "[🔧 CONTRACT] User contracts: #{current_user&.contracts&.pluck(:id, :company_id, :email, :status)}"
      respond_to do |format|
        format.html { redirect_to root_path, alert: works_t('errors.no_workspace_connection') }
        format.json { render json: { error: works_t('errors.no_workspace_connection') }, status: :forbidden }
      end
    end
  end

  def set_work
    @work = @company.works.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    render json: { error: works_t('errors.not_found') }, status: :not_found
  end

  # Helper method for controller-specific translations
  # Makes translation calls cleaner and more maintainable
  def works_t(key, **options)
    t("controllers.works.#{key}", **options)
  end

  def work_params
    params.require(:work).permit(
      :title, 
      :description, 
      :location, 
      :status, 
      :scheduled_start_date, 
      :scheduled_end_date,
      :work_type,
      :is_recurring,
      :latitude,
      :longitude,
      :client_id,
      :booking_id,
      :duration,
      :preferred_period,
      :specific_time,
      :confirmed_time
    )
  end
end