<template>
  <div v-if="!hasConsent" class="cookie-box" :class="{ expanded: showDetails }">
    <div class="cookie-main">
      <h3 class="center">{{ $t('cookie_consent.title', 'Spravujte souhlas se soubory cookie') }}</h3>
      <p class="center">{{ $t('cookie_consent.text', 'Týmbox používá cookies, aby fungoval a taky proto, že chceme vědět, jak tuhle aplikaci pou<PERSON>v<PERSON>. Pomůže nám to dělat lepší produkt.') }}</p>
      <div class="cookie-actions center">
        <button class="btn btn-small btn-outline" @click="toggleDetails">{{ showDetails ? $t('hide', 'Skrýt') : $t('more', 'Více') }}</button>
        <button class="btn btn-small btn-outline" @click="rejectAll">{{ $t('reject', 'Odmítnout') }}</button>
        <button class="btn btn-primary" @click="accept">{{ $t('accept', 'Přijmout') }}</button>
      </div>
    </div>

    <div v-if="showDetails" class="cookie-details">
      <div class="cookie-option">
        <label class="cookie-label">
          <input type="checkbox" v-model="consents.necessary" @change="updateNecessary" checked>
          <span>{{ $t('cookie_consent.technical', 'Technické cookies') }}</span>
        </label>
        <small>{{ $t('cookie_consent.function', 'Nutné pro fungování aplikace') }}</small>
      </div>
      <div class="cookie-option">
        <label class="cookie-label">
          <input type="checkbox" v-model="consents.analytics" checked>
          <span>{{ $t('cookie_consent.analytics', 'Analytické cookies') }}</span>
        </label>
        <small>{{ $t('cookie_consent.help', 'Pomáhají nám zlepšovat Týmbox') }}</small>
      </div>
      <div class="cookie-option">
        <label class="cookie-label">
          <input type="checkbox" v-model="consents.marketing">
          <span>{{ $t('cookie_consent.marketing', 'Marketingové cookies') }}</span>
        </label>
        <small>{{ $t('cookie_consent.measure', 'Měření efektivity marketingu a reklamy') }}</small>
      </div>
      <div class="cookie-option">
        <label class="cookie-label">
          <input type="checkbox" v-model="consents.thirdParty">
          <span>{{ $t('cookie_consent.third_party', 'Cookies třetích stran') }}</span>
        </label>
        <small>{{ $t('cookie_consent.for_services', 'Pro služby poskytované externími partnery') }}</small>
      </div>
      <button class="btn btn-primary" @click="savePreferences">{{ $t('save_settings', 'Uložit nastavení') }}</button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      hasConsent: false,
      showDetails: false,
      consents: {
        necessary: true,
        analytics: true
      }
    }
  },
  mounted() {
    this.checkConsent()
  },
  methods: {
    updateNecessary(value) {
      if (!value) return
      this.consents.necessary = true
    },
    checkConsent() {
      const consent = localStorage.getItem('cookieConsent')
      if (consent) {
        this.hasConsent = true
        this.consents = JSON.parse(consent)
      }
    },
    accept() {
      this.savePreferences()
    },
    acceptAll() {
      Object.keys(this.consents).forEach(key => {
        this.consents[key] = true
      })
      this.savePreferences()
    },
    rejectAll() {
      this.hasConsent = true
    },
    onlyMinimal() {
      Object.keys(this.consents).forEach(key => {
        this.consents[key] = key === 'necessary'
      })
      this.savePreferences()
    },
    toggleDetails() {
      this.showDetails = !this.showDetails
    },
    savePreferences() {
      localStorage.setItem('cookieConsent', JSON.stringify(this.consents))
      this.hasConsent = true
    }
  }
}
</script>

<style scoped>
.cookie-box {
  position: fixed;
  bottom: 20px;
  left: 20px;
  max-width: 357px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  padding: 1rem;
  z-index: 910;
  font-size: 0.9rem;
  color: gray;
}

.cookie-main {
  margin-bottom: 1rem;
}

.cookie-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  align-content: center;
}

.cookie-details {
  border-top: 1px solid #E5E7EB;
  padding-top: 1rem;
  margin-top: 1rem;
}

.cookie-option {
  margin-bottom: 1rem;
}

.cookie-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

input[type="checkbox"] {
  accent-color: gray;
}

.cookie-option small {
  display: block;
  color: gray;
  margin-left: 1.5rem;
}
</style>