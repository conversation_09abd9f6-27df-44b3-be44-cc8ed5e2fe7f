<template>
  <div>
    <select v-model="selectedCountry" @change="changeCountry" class="form-select">
      <option v-for="country in countries" :key="country.code" :value="country.code">
        {{ country.name }}
      </option>
    </select>
  </div>
</template>

<script>
import axios from 'axios';
import sendFlashMessage from '../utils/flashMessage.js';

export default {
  name: 'CountrySelector',
  props: {
    initialCountry: {
      type: String,
      default: 'CZ'
    }
  },
  data() {
    return {
      selectedCountry: this.initialCountry,
      countries: [
        { code: 'CZ', name: 'Česká republika' },
        { code: 'SK', name: 'Slovensko' },
      ]
    }
  },
  watch: {
    initialCountry(newValue) {
      if (newValue !== this.selectedCountry) {
        this.selectedCountry = newValue
      }
    }
  },
  created() {
    this.fetchCurrentCountry()
  },
  methods: {
    fetchCurrentCountry() {
      axios.get('/user_settings')
        .then(response => {
          if (response.data && response.data.country_code) {
            this.selectedCountry = response.data.country_code
            // if (this.selectedCountry !== this.initialCountry) {
            //   this.$emit('country-change', this.selectedCountry)
            // }
          }
        })
        .catch(error => {
          console.error('Error fetching user country setting:', error);
          sendFlashMessage(this.$t('user_settings.error_country_fetching', 'Chyba pri načítaní země'), 'error');
        })
    },
    changeCountry() {
      const newCountry = this.selectedCountry
      
      axios.put('/user_settings', {
        user_setting: { country_code: newCountry }
      })
      .then(() => {
        this.$emit('country-change', newCountry);
        sendFlashMessage(this.$t('user_settings.country_updated', 'Nastavení země bylo aktualizováno'), 'success');
      })
      .catch(error => {
        console.error('Error updating country setting:', error);
        sendFlashMessage(this.$t('user_settings.error_updating_country', 'Chyba pri aktualizování země'), 'error');
      })
    }
  }
}
</script>
