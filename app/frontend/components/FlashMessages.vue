<template>
  <div v-if="messages.length" class="flash-box">
    <div
      v-for="(message, index) in messages"
      :key="index"
      :class="['flash-message', getMessageClass(message.type)]"
      @click="removeMessage(index)"
    >
      {{ message.text }}
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      messages: []
    };
  },
  mounted() {
    document.addEventListener('flashMessage', this.addMessage);
    
    const container = document.getElementById('flash-messages');
    if (container && container.dataset.messages) {
      const railsMessages = JSON.parse(container.dataset.messages);
      Object.entries(railsMessages).forEach(([type, text]) => {
        if (text) {
          this.addMessage({ detail: { text, type } });
        }
      });
    }
  },
  beforeUnmount() {
    // document.removeEventListener('flashMessage', this.addMessage);
  },
  methods: {
    addMessage(event) {
      const { text, type = 'info' } = event.detail;
      this.messages = [{ text, type }];
      
      setTimeout(() => {
        const index = this.messages.findIndex(m => m.text === text);
        if (index !== -1) {
          this.messages.splice(index, 1);
        }
      }, 10000);
    },
    removeMessage(index) {
      this.messages.splice(index, 1);
    },
    getMessageClass(type) {
      switch(type) {
        case 'notice':
        case 'success': 
          return 'flash-success';
        case 'alert':
        case 'error': 
          return 'flash-error';
        case 'warning':
          return 'flash-warning';
        default:
          return 'flash-info';
      }
    }
  }
};
</script>

<style>
.flash-box {
  font-family: "Inter";
  font-weight: 500;
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10000;
  width: 90%;
  max-width: 600px;
}

.flash-message {
  padding: 1rem;
  margin-bottom: 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  text-align: center;
}

.flash-success {
  background: #007BFF;
  color: white;
}

.flash-error {
  background: #dc3545;
  color: white;
}

.flash-warning {
  background: #ffc107;
  color: #333;
}

.flash-info {
  background: #007BFF;
  color: white;
}
</style>