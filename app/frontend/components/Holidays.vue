<template>
  <div class="holidays-container p-3">
    <div class="section-header">
      <h2 class="text-xl md:text-2xl font-semibold text-gray-800 mb-4">
        {{ $t('holiday_page.title', 'Státní svátky pro rok') }} {{ selectedYear }}
      </h2>

      <div class="flex flex-row flex-wrap gap-2">
        <CountrySelector :initialCountry="selectedCountry" @country-change="onCountryChange" />
        <button @click="syncHolidays" class="btn btn-primary" :disabled="isSyncing">
          {{ isSyncing ? $t('holiday_page.syncing', 'Synchronizuji...') : $t('holiday_page.sync_year', 'Synchronizovat') + ' ' + selectedYear }}
        </button>
        <button @click="changeYear(-1)" class="btn btn-small btn-secondary">&lt; {{ $t('holiday_page.previous_year', 'Předchoz<PERSON> rok') }}</button>
        <button @click="changeYear(1)" class="btn btn-small btn-secondary">{{ $t('holiday_page.next_year', 'Dal<PERSON>í rok') }} &gt;</button>
      </div>
    </div>

    <div class="mt-4">
      <div v-if="isLoading" class="text-center p-4">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">{{ $t('loading', 'Načítání...') }}</span>
        </div>
        <p class="mt-2">{{ $t('loading', 'Načítání...') }}</p>
      </div>
      <div v-else-if="holidays.length" class="list-base">
        <div v-for="holiday in holidays" :key="holiday.id" class="list-item-base">
          <div class="w-100">
            <div class="holiday-item">
              <div class="holiday-main">
                <span class="holiday-date">{{ formatDate(holiday.date) }}</span>
                <span class="holiday-name">{{ holiday.name }}</span>
              </div>
              <p v-if="holiday.description" class="holiday-desc">{{ holiday.description }}</p>
            </div>
          </div>
        </div>
      </div>
      <p v-else class="text-center p-4">{{ $t('holiday_page.no_holidays_found', 'Žádné svátky nebyly nalezeny pro tento rok.') }}</p>
    </div>
  </div>
</template>

<script>
import CountrySelector from './CountrySelector.vue'
import axios from 'axios'

export default {
  name: 'Holidays',
  components: {
    CountrySelector
  },
  data() {
    return {
      holidays: [],
      isLoading: false,
      isSyncing: false,
      selectedYear: new Date().getFullYear(),
      selectedCountry: 'CZ'
    }
  },
  methods: {
    fetchHolidays() {
      this.isLoading = true
      
      axios.get('/holidays.json', {
        params: {
          year: this.selectedYear,
          country: this.selectedCountry
        }
      })
      .then(response => {
        this.holidays = response.data
      })
      .catch(error => {
        console.error('Error fetching holidays:', error)
      })
      .finally(() => {
        this.isLoading = false
      })
    },
    syncHolidays() {
      this.isSyncing = true
      
      // JWT-only authentication mode - authorization handled automatically by axios interceptor
      axios.post('/holidays/sync', {
        year: this.selectedYear,
        country: this.selectedCountry
      })
      .then(() => {
        this.fetchHolidays()
      })
      .catch(error => {
        console.error('Error syncing holidays:', error)
      })
      .finally(() => {
        this.isSyncing = false
      })
    },
    formatDate(dateString) {
      const date = new Date(dateString)
      return date.toLocaleDateString(this.$i18n.locale, {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    },
    changeYear(delta) {
      this.selectedYear += delta
      this.fetchHolidays()
    },
    onCountryChange(country) {
      this.selectedCountry = country
      this.fetchHolidays()
    }
  },
  mounted() {
    this.fetchHolidays()
  }
}
</script>

<style scoped>
.holiday-item {
  display: flex;
  flex-direction: column;
  padding: 0.25rem 0;
}

.holiday-main {
  display: flex;
  margin-bottom: 0.25rem;
}

.holiday-date {
  color: #27A844;
  min-width: 110px;
  margin-right: 0.75rem;
}

.holiday-desc {
  color: #707580;
  margin: 0 0 0 110px;
}

@media (max-width: 576px) {
  .holiday-main {
    flex-direction: column;
    margin-bottom: 0.5rem;
  }
  
  .holiday-date {
    margin-bottom: 0.25rem;
  }
  
  .holiday-desc {
    margin-left: 0;
  }
}
</style>