<template>
  <div class="language-selector">
    <button 
      @click="toggleDropdown" 
      class="dropdown-toggle" 
      type="button"
    >
      <span>{{ displayLocale }}</span>
      <ChevronDown :size="14" :class="{ 'rotated': isOpen }" />
    </button>
    
    <div 
      v-if="isOpen" 
      class="dropdown-menu"
    >
      <a 
        v-for="locale in availableLocales" 
        :key="locale.code"
        @click="selectLanguage(locale.code)"
        class="dropdown-item"
        :class="{ 'active': currentLocale === locale.code }"
      >
        {{ locale.name }}
      </a>
    </div>
  </div>
</template>

<script>
import { ChevronDown } from 'lucide-vue-next';
import axios from 'axios';
import sendFlashMessage from '../utils/flashMessage.js';

export default {
  name: 'LanguageSelector',
  components: {
    ChevronDown
  },
  props: {
    currentLocale: {
      type: String,
      required: true
    },
    usePathPrefix: {
      type: <PERSON>olean,
      default: true
    }
  },
  data() {
    return {
      isOpen: false,
      availableLocales: [
        { code: 'cs', name: '<PERSON><PERSON><PERSON><PERSON>' },
        { code: 'sk', name: 'Slovenčina' }
      ]
    };
  },
  computed: {
    displayLocale() {
      const locale = this.availableLocales.find(l => l.code === this.currentLocale);
      return locale ? locale.name : 'CS';
    }
  },
  mounted() {
    document.addEventListener('click', this.closeDropdown);
  },
  beforeUnmount() {
    document.removeEventListener('click', this.closeDropdown);
  },
  methods: {
    toggleDropdown(event) {
      event.stopPropagation();
      this.isOpen = !this.isOpen;
    },
    closeDropdown(event) {
      if (!this.$el.contains(event.target)) {
        this.isOpen = false;
      }
    },
    selectLanguage(locale) {
      this.isOpen = false;
      document.cookie = `locale=${locale};path=/;max-age=31536000;SameSite=Lax`;

      axios.put('/user_settings', {
        user_setting: { language_code: locale }
      })
      .then(() => {
        if (this.usePathPrefix) {
          const { origin, pathname, search, hash } = window.location;
          const pathWithoutLocale = pathname.replace(/^\/(cs|sk)/, '');
          const newPath = `/${locale}${pathWithoutLocale || '/'}`;
          window.location.href = origin + newPath + search + hash;
        } else {
          window.location.reload();
        }
        sendFlashMessage(this.$t('user_settings.language_updated', 'Nastavení jazyka bylo aktualizováno'), 'success');
      })
      .catch(error => {
        console.error('Error updating language setting:', error);
        sendFlashMessage(this.$t('user_settings.error_updating_language', 'Chyba pri aktualizování jazyka'), 'error');
      });
    }
  }
};
</script>

<style scoped>
.language-selector {
  position: relative;
  display: inline-block;
}

.dropdown-toggle {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  border-radius: 0.25rem;
  transition: background-color 0.2s ease;
}

.dropdown-toggle:hover {
  background-color: #f3f4f6;
}

.rotated {
  transform: rotate(180deg);
  transition: transform 0.2s ease;
}

.dropdown-menu {
  position: absolute;
  right: 0px;
  top: 18px;
  margin-top: 0.25rem;
  width: 7rem;
  background-color: #fff;
  border-radius: 0.375rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
              0 4px 6px -4px rgba(0, 0, 0, 0.1);
  padding: 0.25rem 0;
  z-index: 10;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.dropdown-item {
  display: block;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  color: #374151;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.dropdown-item:hover {
  background-color: #eff6ff;
}

.dropdown-item.active {
  background-color: #eff6ff;
}
</style>
