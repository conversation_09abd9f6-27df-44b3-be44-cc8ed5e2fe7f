<template>
  <router-link v-if="!useAnchor && hasRouter" :to="localizedPath" v-bind="$attrs"><slot /></router-link>
  <a v-else :href="localizedPath" @click="handleClick" v-bind="$attrs" data-localized-link-anchor><slot /></a>
</template>

<script setup>
import { computed, getCurrentInstance } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';

const props = defineProps({
  to: {
    type: String,
    required: true
  },
  useAnchor: {
    type: Boolean,
    default: false
  }
});

const i18n = useI18n();
const instance = getCurrentInstance();
const router = useRouter();

const hasRouter = computed(() => {
  return !!router && !!instance?.appContext?.app?.config?.globalProperties.$router;
});

const handleClick = (event) => {
  if (props.to && !props.to.startsWith('http') && !props.to.startsWith('#')) {
    event.preventDefault();
    
    // Use Vue Router if available, otherwise fall back to page navigation
    if (hasRouter.value && router) {
      router.push(localizedPath.value);
    } else {
      window.location.href = localizedPath.value;
    }
  }
};

const localizedPath = computed(() => {
  // Handle undefined or empty to prop
  if (!props.to) {
    return '/';
  }
  
  // Skip localization for external URLs, anchors, or already localized paths
  if (props.to.startsWith('http') || 
      props.to.startsWith('#') || 
      props.to.startsWith('/cs/') || 
      props.to.startsWith('/sk/')) {
    return props.to;
  }
  
  // Add locale prefix to the path and remove any query parameters
  let path = props.to.startsWith('/') ? props.to : `/${props.to}`;
  
  // Remove any existing query parameters
  path = path.split('?')[0];
  
  const locale = i18n.locale.value || 'cs';
  return `/${locale}${path}`;
});
</script>
