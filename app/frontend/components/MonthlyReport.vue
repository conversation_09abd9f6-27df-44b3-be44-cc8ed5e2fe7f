<template>
  
  <div v-if="showDeleteConfirm" class="delete-modal">
    <div class="delete-modal-content">
      <h3 class="delete-modal-title">{{ $t('delete_prefix', 'Smazat') }} {{ deleteType === 'log' ? $t('daily_log', 'záznam') : $t('event', 'událost') }}</h3>
      <p>{{ $t('confirm_delete_prefix', 'Opravdu chcete smazat tento') }} {{ deleteType === 'log' ? $t('daily_log', 'záznam') : $t('event', 'událost') }}?</p>
      <div class="delete-modal-actions">
        <button @click="showDeleteConfirm = false" class="delete-modal-button delete-modal-button--cancel">
          {{ $t('cancel', 'Zrušit') }}
        </button>
        <button @click="deleteItem" class="delete-modal-button delete-modal-button--delete">
          {{ $t('delete', 'Smazat') }}
        </button>
      </div>
    </div>
  </div>
  <div v-if="isLoading" class="time-tracking-loading">
    <Clock :size="20" class="time-tracking-loading-icon" />
  </div>
  <template v-else>
    <div class="report">
      <div class="report-header p-2">
        <h2 class="text-xl md:text-2xl font-semibold text-gray-800">
          {{ $t('attendance_report.title', 'Docházka') }} {{ currentMonth.charAt(0).toUpperCase() + currentMonth.slice(1) }}
        </h2>
        <div>
          <select :value="getCurrentMonthValue()" @change="onMonthSelect($event.target.value)" class="month-selector">
            <option value="" disabled selected>{{ $t('select_month', 'Vyberte měsíc') }}</option>
            <option v-for="month in availableMonths" 
                    :key="month.value" 
                    :value="month.value">
              {{ month.label }}
            </option>
          </select>
        </div>
      
      </div>

      <div class="content-panel">
        <div class="flex items-start gap-2 p-1 rounded text-sm text-gray-600">
          <div class="flex items-center justify-center w-6 h-6">
            <Info size="16" />
          </div>
          <p>
            {{ $t('attendance_report.help_text', 'Zde si opravíte výkazy pracovní doby na konci měsíce. Stačí ťuknout do buňky. A pak mimo ní.') }}
          </p>
        </div>
      </div>
    
      <div class="report-table">
        <div class="report-row report-head">
          <div class="report-cell">{{ $t('day', 'Den') }}</div>
          <div class="report-cell">{{ $t('start', 'Začátek') }}</div>
          <div class="report-cell">{{ $t('end', 'Konec') }}</div>
          <div class="report-cell">{{ $t('break', 'Pauza') }}</div>
          <div class="report-cell">{{ $t('end', 'Konec') }}</div>
          <div class="report-cell">{{ $t('hours', 'Hodin') }}</div>
        </div>

        <div 
          v-for="day in days" 
          :key="day.day" 
          class="report-row"
          :class="{
            'is-weekend': day.is_weekend,
            'is-holiday': day.is_holiday
          }"
        >
          <div class="report-cell">
            <div class="day-info">
              <div>
                <span class="day-number">{{ day.day }}. </span>
                <span class="weekday">{{ getWeekday(day.date) }}</span>
              </div>
            
              <div class="day-actions">
                <button 
                  class="action-btn"
                  @click="toggleActions(day)"
                  ref="actionBtn"
                >
                  <ChevronDown :size="20" />
                </button>
                <div 
                  v-if="activeDay === day.day" 
                  class="actions-menu"
                  @click.stop
                >
                  <button 
                    v-if="day.log || day.event"
                    class="menu-item menu-item--delete"
                    @click="openDeleteConfirm(day)"
                  >
                    {{ $t('attendance_report.delete_daily_record', 'Smazat denní záznam') }}
                  </button>
                  <button 
                    v-if="!day.log && !day.event"
                    class="menu-item menu-item--add"
                    @click="createLog(day.date)"
                  >
                    {{ $t('attendance_report.add_record', 'Přidat záznam') }}
                  </button>
                  <button 
                    v-if="!day.log && !day.event"
                    class="menu-item menu-item--add"
                    @click="addEvent(day)"
                  >
                    {{ $t('add_event', 'Přidat událost') }}
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          <template v-if="day.log">
            
            <!-- Start Time -->
            <div 
              class="report-cell editable"
              :class="{ 'is-editing': isEditing(day.log.id, 'start_time') }"
              @click="startEditing(day.log.id, 'start_time')"
            >
              <template v-if="!isEditing(day.log.id, 'start_time')">
                {{ formatTime(day.log.start_time) }}
              </template>
              <input
                v-else
                type="text" 
                v-model="editValue"
                placeholder="07:00"
                @blur="handleBlur(day.log.id, 'start_time')"
                @keydown.enter.prevent="handleEnter(day.log.id, 'start_time', $event)"
                @keydown.esc.prevent="resetEdit"
                :ref="getInputRef(day.log.id, 'start_time')"
              >
            </div>

            <!-- End Time -->
            <div 
              class="report-cell editable"
              :class="{ 'is-editing': isEditing(day.log.id, 'end_time') }"
              @click="startEditing(day.log.id, 'end_time')"
              >
              <template v-if="!isEditing(day.log.id, 'end_time')">
                {{ formatTime(day.log.end_time) }}
              </template>
              <input
                v-else
                type="text" 
                v-model="editValue"
                placeholder="15:30"
                @blur="handleBlur(day.log.id, 'end_time')"
                @keydown.enter.prevent="handleEnter(day.log.id, 'end_time', $event)"
                @keydown.esc.prevent="resetEdit"
                :ref="getInputRef(day.log.id, 'end_time')"
              >
            </div>

            <!-- Break Start -->
            <div 
              class="report-cell editable"
              :class="{ 'is-editing': isEditing(day.log.id, 'break_start') }"
              @click="day.log.break_id ? startEditing(day.log.id, 'break_start') : createBreak(day.log.id)"
              >
              <template v-if="!isEditing(day.log.id, 'break_start')">
                {{ day.log.break_start ? formatTime(day.log.break_start) : '-' }}
              </template>
              <input
                v-else
                type="text"
                v-model="editValue"
                @blur="saveEdit(day.log.id, 'break_start')"
                @keyup.enter="saveEdit(day.log.id, 'break_start')"
                :ref="getInputRef(day.log.id, 'break_start')"
              >
            </div>

            <!-- Break End -->
            <div 
              class="report-cell editable"
              :class="{ 'is-editing': isEditing(day.log.id, 'break_end') }"
              @click="(day.log.break_id || day.log.break_start) ? startEditing(day.log.id, 'break_end') : createBreak(day.log.id)"
              >
              <template v-if="!isEditing(day.log.id, 'break_end')">
                {{ day.log.break_end ? formatTime(day.log.break_end) : '-' }}
              </template>
              <input
                v-else
                type="text"
                v-model="editValue"
                @blur="saveEdit(day.log.id, 'break_end')"
                @keyup.enter="saveEdit(day.log.id, 'break_end')"
                :ref="getInputRef(day.log.id, 'break_end')"
              >
            </div>

            <div class="report-cell">{{ formatDuration(day.log.duration) }}</div>
          </template>
          
          <template v-else-if="day.event">
            <div class="report-cell merge-cell">{{ day.event.description }}</div>
          </template>
          <template v-else>
            <div class="report-cell empty-cell" @click="createLog(day.date)">-</div>
            <div class="report-cell empty-cell" @click="createLog(day.date)">-</div>
            <div class="report-cell empty-cell" @click="createLog(day.date)">-</div>
            <div class="report-cell empty-cell" @click="createLog(day.date)">-</div>
            <div class="report-cell">-</div>
          </template>
        </div>

        <div class="report-row report-total">
          <div class="report-cell">{{ $t('total', 'Spolu') }}</div>
          <div class="report-cell"></div>
          <div class="report-cell"></div>
          <div class="report-cell"></div>
          <div class="report-cell"></div>
          <div class="report-cell">{{ formatDuration(totalHours) }}</div>
        </div>
      </div>

      <br/>
      <div class="content-panel">
        <div class="grid grid-cols-2 md:grid-cols-3 gap-4 mb-4">
          <div class="text-sm text-gray-700">{{ $t('attendance_report.days_worked', 'Odpracované dny') }}: {{ daysWorked }}</div>
          <div class="text-sm text-gray-700">{{ $t('event_type.vacation', 'Dovolená') }}: {{ eventCounts.vacation }}</div>
          <div class="text-sm text-gray-700">{{ $t('event_type.illness', 'Nemocenská') }}: {{ eventCounts.illness }}</div>
          <div class="text-sm text-gray-700">{{ $t('event_type.day_care', 'Návštěva lékaře') }}: {{ eventCounts.day_care }}</div>
          <div class="text-sm text-gray-700">{{ $t('event_type.family_sick', 'OČR') }}: {{ eventCounts.family_sick }}</div>
          <div class="text-sm text-gray-700">{{ $t('event_type.other', 'Jiné absence') }}: {{ eventCounts.other }}</div>
        </div>
        <div class="form-checkbox-group mt-1">
          <input type="checkbox" class="form-checkbox" v-model="includeSummaryFields" id="includeSummaryFields" />
          <label for="includeSummaryFields" class="form-checkbox-label">{{ $t('attendance_report.include_in_report', 'Vložit do reportu.') }}</label>
        </div>
      </div>
      <br/>

      <div class="content-panel">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="form-group">
            <label for="companyName" class="form-label">{{ $t('attendance_report.organization_or_company', 'Organizace nebo firma') }}</label>
            <input id="companyName" type="text" class="form-input" v-model="companyName" :placeholder="$t('attendance_report.organization_name', 'Název organizace')"/>
          </div>
          <div class="form-group">
            <label for="employeeName" class="form-label">{{ $t('name', 'Jméno') }}</label>
            <input id="employeeName" type="text" class="form-input" v-model="employeeName" :placeholder="$t('attendance_report.your_name', 'Vaše jméno')" />
          </div>
          <div class="form-group md:col-span-2">
            <label for="reportRecipient" class="form-label">{{ $t('attendance_report.where_to_send', 'Kam zaslat docházku?') }}</label>
            <input id="reportRecipient" type="text" class="form-input" v-model="reportRecipient" :placeholder="$t('email', 'E-mailová adresa')" />
          </div>
        </div>
      </div>
      
      <div class="end">
        <div class="flex justify-end items-center gap-2 mt-4 px-4">
          <button @click="sendReport" class="btn btn-primary">
            {{ $t('attendance_report.send_to_email', 'Odeslat na e-mail') }}
          </button>
          <PDFExport
          :days="days"
          :selected-date="selectedDate"
          :total-hours="totalHours"
          :company-name="companyName"
          :employee-name="employeeName"
          :include-summary-fields="includeSummaryFields"
          :days-worked="daysWorked"
          :event-counts="eventCounts"
          />
        </div>
      </div>

      <br/>
      <br/>

    </div>
    <div v-if="showEventForm" class="event-form-modal">
      <div class="event-form-container">
        <div class="event-form-header">
          <h3>{{ $t('add_event', 'Přidat událost') }}</h3>
          <button @click="showEventForm = false" class="close-btn">&times;</button>
        </div>
        <EventForm 
          @event-added="handleEventAdded" 
          :initial-date="selectedDay ? selectedDay.date : null"
        />
      </div>
    </div>
  </template>
</template>

<script>
import axios from 'axios';
import { sendFlashMessage } from '../utils/flashMessage';
import { ChevronDown, Clock, Info } from 'lucide-vue-next'
import PDFExport from './PDFExport.vue';
import EventForm from './events/EventForm.vue';

export default {
  name: 'MonthlyReport',
  components: {
    ChevronDown, Clock, Info, PDFExport, EventForm
  },
  data() {
    return {
      isLoading: true,  
      days: [],
      currentMonth: '',
      showMeta: false,
      editingCell: null,
      editValue: '',
      editField: null,
      // totalHours: 0,
      activeDay: null,
      showConfirmDialog: false,
      pendingDeleteId: null,
      showDeleteConfirm: false,
      deleteType: null, 
      deleteId: null,
      deleteLogId: null,
      companyName: '',
      employeeName: '',
      reportRecipient: '',
      showEventForm: false,
      selectedDay: null,
      selectedDate: new Date(localStorage.getItem('lastViewedMonth') || new Date()), 
      availableMonths: this.getLastTwelveMonths(),
      daysWorked: 0,
      eventCounts: {
        vacation: 0,
        illness: 0,
        day_care: 0,
        family_sick: 0,
        other: 0
      },
      includeSummaryFields: false,
    };
  },
  created() {
    document.addEventListener('click', this.closeActionMenus);
  },
  beforeDestroy() {
    document.removeEventListener('click', this.closeActionMenus);
  },
  watch: {
    days: {
      deep: true,
      handler() {
        this.updateTotalHours();
      }
    }
  },
  methods: {
    getLastTwelveMonths() {
      const months = [];
      let date = new Date();
      
      for(let i = 0; i < 12; i++) {
        months.unshift({
          value: date.toISOString(),
          label: date.toLocaleString(this.$i18n.locale, { month: 'long', year: 'numeric' })
        });
        date = new Date(date.setMonth(date.getMonth() - 1));
      }
      return months;
    },

    onMonthSelect(value) {
      this.selectedDate = new Date(value);
      localStorage.setItem('lastViewedMonth', value);
      this.fetchLogs();
    },

    getCurrentMonthValue() {
      return this.selectedDate.toISOString();
    },

    openDeleteConfirm(day) {
      if (day.log) {
        this.deleteType = 'log';
        this.deleteId = day.log.id;
      } else if (day.event) {
        this.deleteType = 'event';
        this.deleteId = day.event.id;
      } else {
        return; 
      }
      
      this.showDeleteConfirm = true;
      this.activeDay = null;
    },

    initiateDelete(logId) {
      this.pendingDeleteId = logId;
      this.showConfirmDialog = true;
      this.activeDay = null;
    },

    async deleteItem() {
      const id = this.deleteId;
      const type = this.deleteType;
      
      if (type === 'log') {
        // Optimistic UI update for log
        const dayIndex = this.days.findIndex(day => day.log?.id === id);
        if (dayIndex !== -1) {
          const updatedDay = {
            ...this.days[dayIndex],
            log: null
          };
          this.days.splice(dayIndex, 1, updatedDay);
        }

        try {
          await axios.delete(`/daily_logs/${id}`, {
            headers: { 'Accept': 'application/json' }
          });
          sendFlashMessage(this.$t('attendance_report.record_deleted', 'Záznam byl smazán'));
        } catch (error) {
          console.error('Error deleting log:', error);
          sendFlashMessage(this.$t('attendance_report.cannot_delete_record', 'Nelze smazat záznam'), 'error');
          this.updateTotalHours(); 
          await this.fetchLogs(); 
        }
      } else if (type === 'event') {
        // Optimistic UI update for event
        const affectedDays = this.days.filter(day => day.event?.id === id);
        
        affectedDays.forEach(day => {
          const dayIndex = this.days.findIndex(d => d.day === day.day);
          if (dayIndex !== -1) {
            const updatedDay = {
              ...this.days[dayIndex],
              event: null
            };
            this.days.splice(dayIndex, 1, updatedDay);
          }
        });

        try {
          await axios.delete(`/events/${id}`, {
            headers: { 'Accept': 'application/json' }
          });
          sendFlashMessage(this.$t('attendance_report.event_deleted', 'Událost byla smazána'));
          this.updateTotalHours(); 
        } catch (error) {
          console.error('Error deleting event:', error);
          sendFlashMessage(this.$t('attendance_report.cannot_delete_event', 'Nelze smazat událost'), 'error');
          await this.fetchLogs(); 
        }
      }
      
      this.showDeleteConfirm = false;
      this.deleteType = null;
      this.deleteId = null;
    },

    getWeekday(dateString) {
      const weekdays = ['ne', 'po', 'út', 'st', 'čt', 'pá', 'so'];
      const date = new Date(dateString);
      return weekdays[date.getDay()];
    },

    toggleActions(day) {
      this.activeDay = this.activeDay === day.day ? null : day.day;
    },

    closeActionMenus(event) {
      if (!event.target.closest('.day-actions')) {
        this.activeDay = null;
      }
    },

    addEvent(day) {
      this.selectedDay = day;
      this.showEventForm = true;
      this.activeDay = null;
    },
    
    getInputRef(logId, field) {
      return `timeInput_${logId}_${field}`;
    },

    formatTime(time) {
      if (!time) return '-';
      return new Date(time).toLocaleTimeString('cs-CZ', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: false 
      });
    },

    parseTimeInput(value) {
      const cleaned = value.replace(/[^\d.,]/g, '');
      
      let hours, minutes;
      
      if (cleaned.includes('.') || cleaned.includes(',')) {
        // Handle format like 9.15 or 9,15
        const parts = cleaned.split(/[.,]/);
        hours = parseInt(parts[0]);
        minutes = parseInt(parts[1]);
      } else {
        // Handle format like 0915 or 915
        if (cleaned.length === 3) {
          hours = parseInt(cleaned[0]);
          minutes = parseInt(cleaned.slice(1));
        } else if (cleaned.length === 4) {
          hours = parseInt(cleaned.slice(0, 2));
          minutes = parseInt(cleaned.slice(2));
        } else {
          return null;
        }
      }
      
      // Validate hours and minutes
      if (isNaN(hours) || isNaN(minutes) || hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
        return null;
      }
      
      return {
        hours: hours.toString().padStart(2, '0'),
        minutes: minutes.toString().padStart(2, '0')
      };
    },

    formatDuration(seconds) {
      if (!seconds) return '-';
      const hours = seconds / 3600;
      return hours.toFixed(1);
    },

    isEditing(logId, field) {
      return this.editingCell === logId && this.editField === field;
    },

    startEditing(logId, field) {
      this.editingCell = logId;
      this.editField = field;
      
      const day = this.days.find(d => d.log?.id === logId);
      if (!day?.log) return;
      
      this.editValue = day.log[field] ? this.formatTime(day.log[field]) : '';
      
      this.$nextTick(() => {
        const inputRef = this.getInputRef(logId, field);
        const input = this.$refs[inputRef];
        if (input) {
          const inputElement = Array.isArray(input) ? input[0] : input;
          inputElement?.focus();
          inputElement?.select(); 
        }
      });
    },

    moveToNextCell(currentDayIndex, currentField) {
      const fields = ['start_time', 'end_time', 'break_start', 'break_end'];
      const currentFieldIndex = fields.indexOf(currentField);
      
      if (currentFieldIndex < fields.length - 1) {
        const nextField = fields[currentFieldIndex + 1];
        const currentDay = this.days[currentDayIndex];
        if (currentDay.log) {
          this.$nextTick(() => {
            this.startEditing(currentDay.log.id, nextField);
          });
          return;
        }
      }

      if (currentFieldIndex === fields.length - 1) {
        let nextDayIndex = currentDayIndex + 1;
        while (nextDayIndex < this.days.length) {
          if (this.days[nextDayIndex].log) {
            this.$nextTick(() => {
              this.startEditing(this.days[nextDayIndex].log.id, fields[0]);
            });
            break;
          }
          nextDayIndex++;
        }
      }
    },

    handleBlur(logId, field) {
      this.saveEdit(logId, field);
    },

    handleEnter(logId, field) {
      //e.preventDefault();
      const dayIndex = this.days.findIndex(day => day.log?.id === logId);
      
      this.saveEdit(logId, field).then(() => {
        this.moveToNextCell(dayIndex, field);
      });
    },

    async saveEdit(logId, field) {
      if (!this.editValue) {
        this.resetEdit();
        return;
      }

      try {
        const parsedTime = this.parseTimeInput(this.editValue);
        if (!parsedTime) {
          sendFlashMessage(this.$t('attendance_report.invalid_time_format', 'Neplatný formát času. Použijte 0915, 9.15 nebo 09:15'), 'error');
          return;
        }

        const { hours, minutes } = parsedTime;
        
        const day = this.days.find(d => d.log?.id === logId);
        if (!day) return;

        const dateTime = new Date(day.date);
        dateTime.setHours(parseInt(hours), parseInt(minutes), 0);

        let response;

        if (field === 'break_start' || field === 'break_end') {
          
          const breakId = day.log?.break_id; 
          if (!breakId) {
            sendFlashMessage(this.$t('attendance_report.break_not_found', 'Přestávka nebyla nalezena'), 'error');
            return;
          }

          response = await axios.put(`/breaks/${breakId}`, {
            break: {
              [field === 'break_start' ? 'start_time' : 'end_time']: dateTime.toISOString()
            }
          }, {
            headers: { 'Accept': 'application/json' }
          });
        } else {
          response = await axios.put(`/daily_logs/${logId}`, {
            daily_log: {
              [field]: dateTime.toISOString()
            }
          }, {
            headers: { 'Accept': 'application/json' }
          });
        }
    
        if (response.data) {
          const dayData = await axios.get('/daily_logs/fetch_day_data', {
            params: { date: day.date },
            headers: { 'Accept': 'application/json' }
          });
          
          const dayIndex = this.days.findIndex(d => d.date === day.date);
          
          if (dayIndex !== -1) {
            const updatedDay = {
              ...this.days[dayIndex],
              log: { 
                ...dayData.data.log,
                break_start: field === 'break_start' ? dateTime.toISOString() : day.log.break_start,
                break_end: field === 'break_end' ? dateTime.toISOString() : day.log.break_end,
                break_id: day.log.break_id,
                duration: dayData.data.log.duration 
              }
            };
            this.days.splice(dayIndex, 1, updatedDay);
          }

          this.resetEdit(); 
          return true; 
        }

      } catch (error) {
        console.error('Error saving edit:', error);
        if (error.response?.data?.errors) {
          sendFlashMessage(error.response.data.errors.join(', '), 'error');
        } else {
          sendFlashMessage(this.$t('attendance_report.cannot_save_changes', 'Nelze uložit změny'), 'alert');
        }
      } finally {
        this.resetEdit();
      }
    },

    updateTotalHours() {
      this.totalHours = this.days.reduce((total, day) => {
        return total + (day.log?.duration || 0);
      }, 0);
    },

    async createLog(date) {
      try {
        const response = await axios.post('/daily_logs/create_for_report', {
          daily_log: { date }
        }, {
          headers: { 'Accept': 'application/json' }
        });

        if (response.data.errors) {
          sendFlashMessage(response.data.errors, 'error');
          return false; 
        }

        if (response.data) {
          const dayIndex = this.days.findIndex(d => d.date === date);
          if (dayIndex !== -1) {
            const dayData = await axios.get('/daily_logs/fetch_day_data', {
              params: { date },
              headers: { 'Accept': 'application/json' }
            });
            
            const updatedDay = {
              ...this.days[dayIndex],
              log: dayData.data.log
            };
            this.days.splice(dayIndex, 1, updatedDay);
          }
        }
      } catch (error) {
        console.error('Error creating log:', error);
        sendFlashMessage(this.$t('attendance_report.cannot_create_record', 'Nelze vytvořit záznam'), 'error');
      }
    },

    async createBreak(logId) {
      const day = this.days.find(d => d.log?.id === logId);
      if (!day?.log?.start_time || !day?.log?.end_time) {
        sendFlashMessage(this.$t('attendance_report.must_specify_start_end', 'Musí být zadán začátek a konec práce'), 'error');
        return;
      }

      try {
        const response = await axios.post('/breaks/create_for_report', {
          break: { 
            date: day.date,
            daily_log_id: logId 
          }
        }, {
          headers: { 'Accept': 'application/json' }
        });

        if (response.data) {
          const dayData = await axios.get('/daily_logs/fetch_day_data', {
            params: { date: day.date },
            headers: { 'Accept': 'application/json' }
          });

          const dayIndex = this.days.findIndex(d => d.date === day.date);
          if (dayIndex !== -1) {
            const updatedDay = {
              ...this.days[dayIndex],
              log: {
                ...day.log,
                break_id: response.data.id,
                break_start: response.data.start_time,
                break_end: response.data.end_time, 
                duration: dayData.data.log.duration
              }
            };
            this.days.splice(dayIndex, 1, updatedDay);
            // this.updateTotalHours();
          }
        }
      } catch (error) {
        console.error('Error creating break:', error);
        sendFlashMessage(this.$t('attendance_report.cannot_create_break', 'Nelze vytvořit přestávku'), 'error');
      }
    },

    resetEdit() {
      this.editingCell = null;
      this.editField = null;
      this.editValue = '';
    },

    async fetchLogs() {
      this.isLoading = true;

      try {
        const date = this.selectedDate.toISOString().split('T')[0];
        const response = await axios.get('/daily_logs/fetch_report_data', {
          params: { date },
          headers: { 'Accept': 'application/json' }
        });
        if (response.data) {
          this.days = response.data.days;
          this.totalHours = response.data.total_hours;
          this.daysWorked = response.data.days_worked;
          this.eventCounts = response.data.event_counts;
          this.companyName = response.data.company_name;
          this.employeeName = response.data.user_name;
          this.reportRecipient = response.data.user_email;
          this.currentMonth = this.selectedDate.toLocaleString(this.$i18n.locale, { 
            month: 'long', 
            year: 'numeric' 
          });
        }
      } catch (error) {
        console.error('Error fetching logs:', error);
        sendFlashMessage(this.$t('attendance_report.cannot_load_data', 'Nelze načíst data'), 'error');
      } finally {
        this.isLoading = false;
      }
    },
    handleEventAdded(event) {
      this.showEventForm = false;
      
      const startDate = new Date(event.start_time).toISOString().split('T')[0];
      const endDate = new Date(event.end_time).toISOString().split('T')[0];
      
      if (startDate === endDate) {
        const dayIndex = this.days.findIndex(day => day.date === startDate);
        if (dayIndex !== -1) {
          const updatedDay = {
            ...this.days[dayIndex],
            event: {
              id: event.id,
              type: event.event_type,
              description: this.getEventTypeDescription(event.event_type)
            }
          };
          this.days.splice(dayIndex, 1, updatedDay);
          this.updateTotalHours(); 
        }
      } else {
        this.fetchLogs();
      }
    },
    getEventTypeDescription(eventType) {
      return this.$t(`event_type.${eventType}`, eventType);
    },
    sendReport() {
      if (!this.reportRecipient) {
        sendFlashMessage(this.$t('attendance_report.enter_email', 'Zadejte e-mail pro odeslání'), 'error');
        return;
      }
      
      console.log(this.selectedDate.toISOString());
      axios.post('/reports/send_pdf', {
        selected_date: this.selectedDate.toISOString(),
        report_recipient: this.reportRecipient,
        company_name: this.companyName,
        employee_name: this.employeeName,
        include_summary_fields: this.includeSummaryFields,
        days_worked: this.daysWorked,
        event_counts: this.eventCounts
      }, {
        headers: { 'Accept': 'application/json' }
      })
      .then(response => {
        sendFlashMessage(this.$t('attendance_report.sent_to_email', 'Docházka byla odeslána na e-mail'), 'notice');
      })
      .catch(error => {
        console.error('Error sending report:', error);
        sendFlashMessage(this.$t('attendance_report.send_error', 'Nelze odeslat report: ') + 
          (error.response?.data?.error || ''), 'error');
      });
    },
    handleError(error, defaultMessage) {
      const errorMessage = error.response?.data?.errors?.join('\n') || defaultMessage;
      sendFlashMessage(errorMessage, 'alert');
    },
    refreshPage() {
      window.location.reload();
    }
  },

  mounted() {
    this.fetchLogs();
  }
};
</script>

<style>
.time-tracking-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100px;
}

.time-tracking-loading-icon {
  animation: spin 2s linear infinite;
}

@keyframes spin {
  100% {
    transform: rotate(360deg);
  }
}

.day-cell {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.day-info {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.day-number {
  font-size: 0.9rem;
  font-weight: 600;
}

.weekday {
  font-size: 0.9rem;
  color: #666;
}

.day-actions {
  position: relative;
}

.action-btn {
  background: none;
  border: none;
  padding: 2px 4px;
  cursor: pointer;
  color: #666;
  font-weight: bold;
}

.action-btn:hover {
  color: #333;
}

.action-icon {
  width: 16px;
  height: 16px;
}

.actions-menu {
  position: absolute;
  top: 100%;
  right: -80;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  z-index: 800;
  min-width: 180px;
}

.menu-item {
  display: block;
  width: 100%;
  padding: 15px 15px;
  text-align: left;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
}

.menu-item:hover {
  background: #f5f5f5;
}

.menu-item--delete {
  color: #dc3545;
}

.menu-item--delete:hover {
  background: #fff5f5;
}

.menu-item--add {
  color: #22C55E;
}

.menu-item--add:hover {
  background: #f5fff5;
}

.editable input {
  width: 100%;
  height: 100%;
  padding: 0.75rem;
  border: 2px solid #007bff;
  outline: none;
  font-size: 0.9rem;
  text-align: center;
}

.report {
  max-width: 600px;
  margin: 0 auto;
  
}

.report-header {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 10px;
  margin-bottom: 15px;
}

select.month-selector {
  padding: 0.5rem;
  margin: 0;
}

.report-title {
  font-size: 1.2rem;
  font-weight: 500;
  margin: 0;
}

.report-table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb; 
  margin-bottom: 1.5rem;
}

.report-row {
  display: grid;
  grid-template-columns: 1.1fr 0.9fr 0.9fr 0.9fr 0.9fr 0.5fr;
  border-bottom: 1px solid #eee;
  padding: 0 0.25rem 0 0.25rem;
}

.report-head {
  font-weight: 500;
  background: white;
}

.report-cell {
  padding: 0.75rem;
  font-size: 0.9rem;
}

.merge-cell {
  grid-column: span 5;
}

.report-total {
  font-weight: 500;
  border-top: 2px solid #eee;
}

.editable, .empty-cell {
  cursor: pointer;
}

.editable:hover, .empty-cell:hover {
  background-color: #f8f9fa;
}

.editable.is-editing {
  padding: 0;
}

.editable input {
  width: 100%;
  height: 100%;
  padding: 0.75rem;
  border: 2px solid #007bff;
  outline: none;
  font-size: 0.9rem;
  text-align: center;
}

.is-weekend {
  background: #B7EECB;
}

.is-holiday {
  background: #ffeeaa;
}

@media (max-width: 480px) {
  .report-cell {
    padding: 1rem 0.2rem;
    font-size: 0.90rem;
  }
  
  .editable input {
    padding: 0.5rem;
    font-size: 0.90rem;
  }
}

.delete-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 900;
}

.delete-modal-content {
  background: white;
  padding: 20px;
  border-radius: 8px;
  max-width: 400px;
  width: 90%;
}

.delete-modal-title {
  margin: 0 0 16px 0;
  font-size: 1.2rem;
}

.delete-modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 20px;
}

.delete-modal-button {
  display: inline-flex;
  margin: 0 4px;
  padding: 0 0.875rem;
  align-items: center;
  text-align: center;
  justify-content: center;
  font-size: 1rem;
  height: 38px;  
  line-height: 1;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
}

.delete-modal-button--cancel {
  background: #f1f1f1;
}

.delete-modal-button--delete {
  background: #dc3545;
  color: white;
}

.event-form-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 900;
}

.event-form-container {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  min-height: 700px;
  overflow-y: auto;
}

.event-form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
}

.event-form-header h3 {
  margin: 0;
  font-size: 1.2rem;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
}

.close-btn:hover {
  color: #333;
}
</style>