<template>
  <div 
    class="flex items-center cursor-pointer rounded-md"
    :class="[
      collapsed ? 'justify-center px-3 py-2.5 mb-1' : 'justify-between px-3 py-2.5 mb-1',
      active ? 'bg-blue-50 text-blue-600' : 'text-gray-600 hover:bg-gray-50'
    ]"
    @click="$emit('click')"
  >
    <div class="flex items-center">
      <div :class="{'mr-3': !collapsed}">
        <component :is="icon" :size="20" />
      </div>
      <span v-if="!collapsed" class="text-sm font-medium">{{ text }}</span>
    </div>
    
    <div v-if="!collapsed" class="flex items-center">
      <span v-if="badge" class="bg-blue-100 text-blue-600 px-1.5 py-0.5 text-xs rounded mr-1.5">
        {{ badge }}
      </span>
      <ChevronRight v-if="hasSubmenu" :size="16" class="text-gray-400" />
    </div>
  </div>
</template>

<script>
import { ChevronRight } from 'lucide-vue-next';

export default {
  name: 'NavItem',
  components: {
    ChevronRight
  },
  props: {
    icon: {
      type: Object,
      required: true
    },
    text: {
      type: String,
      required: true
    },
    collapsed: {
      type: Boolean,
      default: false
    },
    active: {
      type: Boolean,
      default: false
    },
    badge: {
      type: String,
      default: null
    },
    hasSubmenu: {
      type: Boolean,
      default: false
    }
  }
}
</script>