<template>
  <router-link v-if="isSpa" :to="to" :class="linkClass" @click="handleClick">
    <slot />
  </router-link>
  <a v-else :href="href" :class="linkClass" @click="handleClick">
    <slot />
  </a>
</template>

<script>
export default {
  name: 'NavLink',
  props: {
    to: {
      type: String,
      required: true
    },
    href: {
      type: String,
      default: null
    },
    linkClass: {
      type: [String, Array, Object],
      default: ''
    }
  },
  computed: {
    isSpa() {
      // Properly detect SPA mode by checking for Vue Router
      return !!this.$router && !!this.$route;
    }
  },
  methods: {
    handleClick(event) {
      this.$emit('click', event);
    }
  }
}
</script>