<template>
  <div v-if="isOpen" class="modal-overlay">
    <div class="modal-container">
      <div class="modal-header">
        <h2>Newsletter</h2>
        <button @click="close" class="close-btn">&times;</button>
      </div>
      <div class="modal-body">
        <p class="consent-text">
          Získejte aktuální novinky z Týmboxu
        </p>
        <div class="form-group">
          <input 
            type="email" 
            v-model="email" 
            placeholder="Váš email"
            class="input-field"
          />
          <div v-if="error" class="error-text">{{ error }}</div>
        </div>
        <div class="checkbox-group">
          <input type="checkbox" id="consent" v-model="consentGiven" />
          <label for="consent">Souhlasím s odběrem novinek</label>
        </div>
      </div>
      <div class="modal-footer">
        <button 
          @click="subscribe" 
          :disabled="!canSubmit || isSubmitting"
          class="btn btn-primary"
        >
          {{ isSubmitting ? 'Odesílám...' : 'Odebírat' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  data() {
    return {
      isOpen: false,
      email: '',
      consentGiven: false,
      isSubmitting: false,
      error: null
    }
  },
  computed: {
    canSubmit() {
      return this.email && this.consentGiven;
    }
  },
  methods: {
    open() {
      this.isOpen = true;
      document.body.classList.add('modal-open');
    },
    close() {
      this.isOpen = false;
      document.body.classList.remove('modal-open');
      this.email = '';
      this.consentGiven = false;
      this.error = null;
    },
    async subscribe() {
      if (!this.canSubmit) return;
      
      this.isSubmitting = true;
      this.error = null;
      
      try {
        const response = await axios.post('/newsletter/subscribe', { 
          newsletter: { email: this.email }
        });
        
        if (response.data.success) {
          const event = new CustomEvent('flashMessage', {
            detail: { text: 'Díky! Byli jste přihlášeni k odběru novinek.', type: 'success' }
          });
          document.dispatchEvent(event);
          this.close();
        } else {
          this.error = response.data.error || 'Přihlášení selhalo. Zkuste to znovu.';
        }
      } catch (error) {
        this.error = error.response?.data?.error || 'Došlo k chybě. Zkuste to znovu.';
      } finally {
        this.isSubmitting = false;
      }
    }
  }
}
</script>