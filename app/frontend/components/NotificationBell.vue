<template>
  <div class="notification-bell">
    <button
      @click="toggleDropdown"
      class="relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
    >
      <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
      </svg>
      <span v-if="unreadCount > 0" class="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full">
        {{ unreadCount > 99 ? '99+' : unreadCount }}
      </span>
    </button>

    <div v-if="showDropdown" class="absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg overflow-hidden z-20">
      <div class="py-2">
        <div class="px-4 py-2 text-sm font-semibold text-gray-700 border-b">
          Notifications
        </div>
        
        <div v-if="notifications.length === 0" class="px-4 py-3 text-sm text-gray-500">
          No notifications
        </div>
        
        <div v-else class="max-h-64 overflow-y-auto">
          <div
            v-for="notification in notifications"
            :key="notification.id"
            @click="markAsRead(notification)"
            class="px-4 py-3 hover:bg-gray-50 cursor-pointer border-b"
            :class="{ 'bg-blue-50': !notification.read }"
          >
            <div class="flex items-start">
              <div class="flex-1">
                <p class="text-sm text-gray-900">{{ notification.title }}</p>
                <p class="text-xs text-gray-500 mt-1">{{ notification.message }}</p>
                <p class="text-xs text-gray-400 mt-1">{{ formatTime(notification.created_at) }}</p>
              </div>
              <div v-if="!notification.read" class="ml-2 flex-shrink-0">
                <span class="inline-block w-2 h-2 bg-blue-600 rounded-full"></span>
              </div>
            </div>
          </div>
        </div>
        
        <div v-if="notifications.length > 0" class="px-4 py-2 border-t">
          <button
            @click="markAllAsRead"
            class="text-sm text-indigo-600 hover:text-indigo-500"
          >
            Mark all as read
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import cable from '../services/cable';

export default {
  name: 'NotificationBell',

  data() {
    return {
      showDropdown: false,
      notifications: [],
      subscription: null
    };
  },

  computed: {
    ...mapGetters('userStore', ['currentUser']),
    
    unreadCount() {
      return this.notifications.filter(n => !n.read).length;
    }
  },

  mounted() {
    this.loadNotifications();
    this.subscribeToNotifications();
    
    // Close dropdown when clicking outside
    document.addEventListener('click', this.handleClickOutside);
  },

  beforeUnmount() {
    this.unsubscribeFromNotifications();
    document.removeEventListener('click', this.handleClickOutside);
  },

  methods: {
    toggleDropdown() {
      this.showDropdown = !this.showDropdown;
    },

    handleClickOutside(event) {
      if (!this.$el.contains(event.target)) {
        this.showDropdown = false;
      }
    },

    async loadNotifications() {
      try {
        const response = await this.$axios.get('/api/v1/notifications');
        this.notifications = response.data.notifications || [];
      } catch (error) {
        console.error('[NotificationBell] Failed to load notifications:', error);
      }
    },

    subscribeToNotifications() {
      if (!this.currentUser) {
        console.warn('[NotificationBell] No current user, cannot subscribe to notifications');
        return;
      }

      this.subscription = cable.subscribe('NotificationChannel', {}, {
        connected: () => {
          console.log('[NotificationBell] Connected to NotificationChannel');
        },
        
        disconnected: () => {
          console.log('[NotificationBell] Disconnected from NotificationChannel');
        },
        
        received: (data) => {
          console.log('[NotificationBell] Received notification:', data);
          
          if (data.type === 'new_notification' && data.notification) {
            // Add new notification to the beginning of the list
            this.notifications.unshift(data.notification);
            
            // Show browser notification if permitted
            this.showBrowserNotification(data.notification);
          } else if (data.type === 'notification_read' && data.notification_id) {
            // Update notification read status
            const notification = this.notifications.find(n => n.id === data.notification_id);
            if (notification) {
              notification.read = true;
            }
          }
        }
      });
    },

    unsubscribeFromNotifications() {
      if (this.subscription) {
        cable.unsubscribe('NotificationChannel');
        this.subscription = null;
      }
    },

    async markAsRead(notification) {
      if (!notification.read && this.subscription) {
        // Send mark as read action through Action Cable
        this.subscription.perform('mark_as_read', { notification_id: notification.id });
      }
      
      // Navigate to notification link if available
      if (notification.link) {
        this.$router.push(notification.link);
        this.showDropdown = false;
      }
    },

    async markAllAsRead() {
      const unreadIds = this.notifications
        .filter(n => !n.read)
        .map(n => n.id);
      
      if (unreadIds.length > 0) {
        try {
          await this.$axios.post('/api/v1/notifications/mark_all_read', {
            notification_ids: unreadIds
          });
          
          // Update local state
          this.notifications.forEach(n => {
            if (!n.read) n.read = true;
          });
        } catch (error) {
          console.error('[NotificationBell] Failed to mark all as read:', error);
        }
      }
    },

    formatTime(timestamp) {
      const date = new Date(timestamp);
      const now = new Date();
      const diff = now - date;
      const minutes = Math.floor(diff / 60000);
      const hours = Math.floor(diff / 3600000);
      const days = Math.floor(diff / 86400000);
      
      if (minutes < 1) return 'just now';
      if (minutes < 60) return `${minutes}m ago`;
      if (hours < 24) return `${hours}h ago`;
      if (days < 7) return `${days}d ago`;
      
      return date.toLocaleDateString();
    },

    async showBrowserNotification(notification) {
      if ('Notification' in window && Notification.permission === 'granted') {
        const browserNotification = new Notification(notification.title, {
          body: notification.message,
          icon: '/logo-v7a.png'
        });
        
        browserNotification.onclick = () => {
          window.focus();
          this.markAsRead(notification);
        };
      }
    }
  }
};
</script>

<style scoped>
.notification-bell {
  position: relative;
}
</style>