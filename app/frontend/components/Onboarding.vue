<template>
  <div>
    <!-- When no tenant is set -->
    <div v-if="!currentTenant" class="section">
      <div class="box">
        <div class="styled-form w100">
          <h2>{{ $t('onboarding.welcome', 'Vítejte na Týmboxu!') }}</h2>
          <p>
            {{ $t('onboarding.welcome_text', 'Týmbox je aplikace, která vám pomáhá soustředit se na vaši práci, ne na softvér.') }}
            {{ $t('onboarding.welcome_text_2', '<PERSON><PERSON><PERSON><PERSON>, žádn<PERSON> komplikace - jen n<PERSON>, k<PERSON><PERSON> skutečně používáte.') }}
            {{ $t('onboarding.welcome_text_3', 'Začněte s evidenc<PERSON> do<PERSON> a objevte, jak může být jednoduché to, co bylo dřív otravné.') }}
          </p>
        </div>

        <div v-if="pendingContracts && pendingContracts.length > 0" id="company-connections" class="w100">
          <!-- Company connections component is mounted here by Rails -->
        </div>

        <div v-else class="styled-form">
          <h2>
            {{ $t('onboarding.workspace', 'Pracovní prostor') }}
          </h2>
          <p> 
            {{ $t('onboarding.workspace_text', 'Aplikace je vytvořená pro firmy a organizace, ale můžete ji používat i ve skupině nebo osamotě.') }}
            {{ $t('onboarding.workspace_text_2', 'Jestli firmu, organizaci či skupinu nezakládáte, počkejte si na pozvání.') }}
          </p>

          <div class="form-group">
            <LocalizedLink :to="'/company_connections'" 
               :class="['btn btn-primary', {'inactive': !pendingInvitation}]" :use-anchor="true">
              {{ $t('onboarding.connect_to_company', 'Připojit se k firmě') }}
            </LocalizedLink>
            <br/><br/>
            <LocalizedLink :to="'/companies/new'" class="btn btn-outline" :use-anchor="true">
              {{ $t('onboarding.create_workspace', 'Vytvořit vlastní prostor') }}
            </LocalizedLink>
          </div>
        </div>
      </div>
    </div>

    <!-- Owner view when there are no contracts and user has owner permissions -->
    <div v-else-if="emptyContracts && permissions.view_owner" class="section">
      <div class="box">
        <div class="styled-form">
          <h2>{{ $t('onboarding.first_step', 'První krok máte za sebou!') }}</h2>
          <p>
            {{ $t('onboarding.first_step_text', 'Přidejte si do vášeho pracovního prostoru kolegy a spolupracovníky a začněte evidovat docházku. Můžete přidat i sami sebe a vést tak svoji vlastní docházku.') }}
          </p>
          <div class="form-group">
            <LocalizedLink :to="'/contracts/new'" class="btn btn-primary" :use-anchor="true">Nový kolega</LocalizedLink>
          </div>
        </div>  

        <div class="styled-form">
          <h2>{{ $t('onboarding.second_step', 'Máte vytvořený pracovní prostor') }}</h2>
          <p class="my-1">{{ $t('onboarding.second_step_text', 'Jako majitel vytvořeného prostoru nebo autorizovaný uživatel můžete:') }}</p>
          
          <div class="action-list">
            <LocalizedLink :to="'/contracts/new'" class="text-link" :use-anchor="true">{{ $t('onboarding.add_people_to_team', 'Přidávat lidi do týmu') }}</LocalizedLink>
            <LocalizedLink :to="'/contracts/new'" class="text-link" :use-anchor="true">{{ $t('onboarding.manage_own_attendance', 'Vést vlastní docházku') }}</LocalizedLink>
            <LocalizedLink :to="'/contracts'" class="text-link" :use-anchor="true">{{ $t('onboarding.manage_colleagues', 'Spravovat kolegy') }}</LocalizedLink>
            <a href="#" class="text-link inactive">{{ $t('onboarding.view_reports', 'Zobrazit reporty') }}</a>
            <a href="#" class="text-link inactive">{{ $t('onboarding.approve_leave', 'Schvalovat dovolené') }}</a>
          </div>
        </div>
      </div>
    </div>

    <!-- Regular dashboard view -->
    <div v-else id="mainbox">
      <!-- Mainbox component will be mounted here -->
    </div>
  </div>
</template>

<script>
import LocalizedLink from './LocalizedLink.vue';

export default {
  name: 'Onboarding',
  components: { 
    LocalizedLink
  },
  props: {
    currentTenant: {
      type: Boolean,
      default: false
    },
    permissions: {
      type: Object,
      default: () => ({
        view_owner: false,
        view_employee: false
      })
    },
    contracts: {
      type: Array,
      default: () => []
    },
    pendingContracts: {
      type: Array,
      default: () => []
    },
    pendingInvitation: {
      type: Boolean,
      default: false
    },
    companies: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    emptyContracts() {
      return this.contracts && this.contracts.length === 0;
    }
  }
};
</script>

<style scoped>
.section {
  margin-bottom: 2rem;
}

.box {
  background-color: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
}

.w100 {
  width: 100%;
}

.styled-form {
  margin-bottom: 1.5rem;
}

.styled-form h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
}

.form-group {
  margin-top: 1rem;
}

.btn {
  display: inline-block;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: background-color 0.2s, color 0.2s;
}

.btn-primary {
  background-color: #3182ce;
  color: white;
}

.btn-outline {
  border: 1px solid #3182ce;
  color: #3182ce;
}

.inactive {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.text-link {
  color: #3182ce;
  text-decoration: none;
}

.my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
</style>