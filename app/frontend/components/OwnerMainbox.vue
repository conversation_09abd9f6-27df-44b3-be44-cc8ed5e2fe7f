<template>
  <div class="mainbox-section">
    <!-- Notifications Section - Commented out for now -->
    <!-- <div v-if="notifications && notifications.length" class="mb-4">
      <div class="card-content !p-0">
        <ul class="divide-y divide-gray-100">
          <li v-for="notification in notifications" :key="notification.id" class="flex items-center justify-between p-4 hover:bg-gray-50">
            <div class="flex items-center gap-3">
              <div class="p-2 rounded-full">
                <component :is="getNotificationIcon(notification.notification_type)" :size="16" class="text-blue-600" />
              </div>
              <div class="text-sm flex-1">
                <p class="font-medium text-gray-800">{{ notification.title }}</p>
                <p class="text-gray-600">{{ notification.message }}</p>
                <p class="text-xs text-gray-500 mt-1">{{ formatDateTime(notification.created_at) }}</p>
              </div>
            </div>
            <div class="flex items-center gap-2">
              <button 
                v-if="notification.notifiable_type === 'Work' && notification.notifiable_id"
                @click="viewWork(notification.notifiable_id)" 
                class="btn btn-light text-sm"
              >
                {{ $t('notifications.view_work', 'Zobrazit zakázku') }}
              </button>
              <button 
                @click="markAsRead(notification.id)" 
                class="btn btn-light text-sm"
              >
                {{ $t('notifications.dismiss', 'Zavřít') }}
              </button>
            </div>
          </li>
        </ul>
      </div>
    </div> -->

    <!-- Events Section -->
    <div v-if="events && events.length" class="card">
      <div class="card-content !p-0">
        <ul class="divide-y divide-gray-100">
          <li v-for="event in events" :key="event.id" class="flex items-center justify-between p-4 hover:bg-gray-50">
            <div class="flex items-center gap-3">
              <div class="p-2 rounded-full">
                  <BellRing :size="16" class="text-blue-600" />
              </div>
              <div class="text-sm">
                <p class="font-medium text-gray-800">{{ event.name }}</p>
                <p class="text-gray-600">{{ event.t_event_type }} - {{ formatDate(event.start_time) }}</p>
              </div>
            </div>
            <button @click="confirmEventHandler(event.id)" class="btn btn-small btn-light">{{ $t('confirm', 'Potvrdit') }}</button>
          </li>
        </ul>
      </div>
    </div>

    <!-- Placeholder for TeamSummary if needed -->
    <!-- <TeamSummary /> -->
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
import { Clock, MailQuestion, Flag, Timer, TimerOff, Briefcase, Calendar, Mail, UserPlus, AlertCircle, BellRing } from 'lucide-vue-next';
import { sendFlashMessage } from '../utils/flashMessage';
import TeamSummary from '../components/TeamSummary.vue';
import axios from 'axios';
import { getLocaleString } from '../utils/dateFormatter';

export default {
  components: {
    Clock, Flag, MailQuestion, Timer, TimerOff, Briefcase, Calendar, Mail, UserPlus, AlertCircle, BellRing,
    TeamSummary
  }, 
  data() {
    return {
      notifications: []
    };
  },
  computed: {
    ...mapGetters('ownerStore', ['employees', 'events']),
  },
  methods: {
    ...mapActions('ownerStore', ['fetchEmployees', 'fetchEvents', 'confirmEvent']),
    fetchPendingEvents() {
      this.fetchEvents('pending');
    },
    async fetchNotifications() {
      // Commented out notification system for now
      // try {
      //   const response = await axios.get('/api/v1/notifications/unread');
      //   this.notifications = response.data.notifications;
      // } catch (error) {
      //   console.error('Error fetching notifications:', error);
      // }
    },
    async markAsRead(notificationId) {
      // Commented out notification system for now
      // try {
      //   await axios.patch(`/api/v1/notifications/${notificationId}/mark_as_read`);
      //   this.notifications = this.notifications.filter(n => n.id !== notificationId);
      //   sendFlashMessage(this.$t('notification_dismissed', 'Oznámení označeno jako přečtené'), 'success');
      // } catch (error) {
      //   console.error('Error marking notification as read:', error);
      //   sendFlashMessage(this.$t('error_dismissing_notification', 'Chyba při označování oznámení'), 'error');
      // }
    },
    viewWork(workId) {
      window.location.href = `/${this.$i18n.locale}/works/${workId}`;
    },
    getNotificationIcon(type) {
      const iconMap = {
        'work_assignment_added': 'Briefcase',
        'work_assignment_removed': 'Briefcase',
        'work_status_changed': 'Briefcase',
        'work_check_in_reminder': 'AlertCircle',
        'booking_received': 'Calendar',
        'invitation_received': 'UserPlus',
        'event_pending': 'BellRing'
      };
      return iconMap[type] || 'BellRing';
    },
    formatDateTime(date) {
      if (!date) return '';
      const options = { day: '2-digit', month: '2-digit', year: 'numeric', hour: '2-digit', minute: '2-digit' };
      const locale = getLocaleString(this.$i18n.locale);
      return new Date(date).toLocaleDateString(locale, options);
    },
    async confirmEventHandler(eventId) {
      try {
        const result = await this.confirmEvent(eventId);
        if (result && result.success) {
          sendFlashMessage(this.$t('event_confirmed', "Událost potvrzená"), "success");
        } else {
          sendFlashMessage(this.$t('confirmation_error', "Při potvrzování nastala chyba"), 'error');
        }
      } catch (error) {
        sendFlashMessage(this.$t('confirmation_error', "Při potvrzování nastala chyba"), 'error');
      }
    },
    formatDate(date) {
      const locale = getLocaleString(this.$i18n.locale);
      return new Date(date).toLocaleDateString(locale, {
        day: 'numeric',
        month: 'long',
        year: 'numeric',
      });
    },
    formatTime(date) {
      const locale = getLocaleString(this.$i18n.locale);
      return new Date(date).toLocaleTimeString(locale, {
        hour: '2-digit',
        minute: '2-digit',
      });
    }
  },
  mounted() {
    this.fetchPendingEvents();
    // this.fetchNotifications();
    // Refresh notifications every 30 seconds - commented out for now
    // this.notificationInterval = setInterval(() => {
    //   this.fetchNotifications();
    // }, 30000);
  },
  beforeUnmount() {
    // if (this.notificationInterval) {
    //   clearInterval(this.notificationInterval);
    // }
  }
};
</script>

<!-- Removed scoped styles -->