<template>
  <button @click="generatePDF" class="btn btn-primary" >
    Export PDF
  </button>
</template>

<script>
import { jsPDF } from 'jspdf';
import 'jspdf-autotable';
import { FileText } from 'lucide-vue-next'

export default {
  name: 'PDFExport',
  components: {
    FileText
  },
  props: {
    days: {
      type: Array,
      required: true
    },
    selectedDate: {
      type: Date,
      required: true
    },
    totalHours: {
      type: Number,
      required: true
    },
    companyName: { 
      type: String,
      default: ''
    },
    employeeName: {
      type: String,
      default: ''
    },
    includeSummaryFields: {
      type: Boolean,
      default: false
    },
    daysWorked: {
      type: Number,
      default: 0
    },
    eventCounts: {
      type: Object,
      default: () => ({
        vacation: 0,
        illness: 0,
        day_care: 0,
        family_sick: 0,
        other: 0
      })
    }
  },

  methods: {
    encodeSpecialChars(text) {
      return text
        .replace(/ň/g, String.fromCharCode(0x0148))
        .replace(/č/g, String.fromCharCode(0x010D))
        .replace(/ť/g, String.fromCharCode(0x0165))
        .replace(/š/g, String.fromCharCode(0x0161))
        .replace(/ž/g, String.fromCharCode(0x017E))
        .replace(/ý/g, String.fromCharCode(0x00FD))
        .replace(/á/g, String.fromCharCode(0x00E1))
        .replace(/í/g, String.fromCharCode(0x00ED))
        .replace(/é/g, String.fromCharCode(0x00E9))
        .replace(/ú/g, String.fromCharCode(0x00FA))
        .replace(/ä/g, String.fromCharCode(0x00E4))
        .replace(/ô/g, String.fromCharCode(0x00F4))
        .replace(/ď/g, String.fromCharCode(0x010F));
    },

    formatTime(time) {
      if (!time) return '';
      return new Date(time).toLocaleTimeString(this.$i18n.locale, { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: false 
      });
    },

    formatDuration(seconds) {
      if (!seconds) return '';
      const hours = seconds / 3600;
      return hours.toFixed(1);
    },

    generatePDF() {
      const doc = new jsPDF();
      doc.addFont('https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.66/fonts/Roboto/Roboto-Regular.ttf', 'Roboto', 'normal');
      doc.setFont('Roboto');

      const monthYear = this.selectedDate && this.selectedDate.toLocaleString ? 
        this.selectedDate.toLocaleString(this.$i18n.locale, { 
          month: 'long',
          year: 'numeric'
        }).replace(/^./, match => match.toUpperCase()) :
        new Date().toLocaleString(this.$i18n.locale, { 
          month: 'long',
          year: 'numeric'
        }).replace(/^./, match => match.toUpperCase());

      // Header
      doc.setFontSize(18);
      
      const pageWidth = doc.internal.pageSize.getWidth();
      const text = this.encodeSpecialChars(this.$t('attendance', 'DOCHÁZKA'));
      const textWidth = doc.getTextWidth(text);
      const xPosition = (pageWidth - textWidth) / 2;
      doc.text(text, xPosition, 15);

      doc.setFontSize(11);
      doc.text(this.encodeSpecialChars(this.$t('employer', 'Zaměstnavatel') + ': ' + this.companyName), 30, 25);
      doc.text(this.encodeSpecialChars(this.$t('employee', 'Zaměstnanec') + ': ' + this.employeeName), 30, 32);
      // doc.text(this.encodeSpecialChars(this.$t('work_report', 'Výkaz práce') + ' - ' + monthYear), 30, 39);

      // Table data
      const tableData = this.days.map(day => {
        if (day.event) {
          return [
            { content: day.day, styles: { halign: 'center' } },
            { content: this.encodeSpecialChars(day.event.description), colSpan: 2, styles: { halign: 'center' } },
            '-',
            '-',
            '-'
          ];
        }
        return [
          day.day,
          this.formatTime(day.log?.start_time),
          this.formatTime(day.log?.end_time),
          this.formatTime(day.log?.break_start),
          this.formatTime(day.log?.break_end),
          this.formatDuration(day.log?.duration)
        ];  
      });

      // Headers
      const headers = [
        [
          {content: this.encodeSpecialChars(this.$t('report', 'Výkaz') + ' - ' + monthYear), colSpan: 6}
        ],
        [
          {content: this.encodeSpecialChars(this.$t('day', 'Den')), rowSpan: 2}, 
          {content: this.encodeSpecialChars(this.$t('work_time', 'Pracovní doba')), colSpan: 2}, 
          {content: this.encodeSpecialChars(this.$t('break', 'Přestávka')), colSpan: 2}, 
          {content: this.encodeSpecialChars(this.$t('total_hours', 'Celkem hodin')), rowSpan: 2} 
        ],
        [
          this.encodeSpecialChars(this.$t('start', 'Začátek')), 
          this.encodeSpecialChars(this.$t('end', 'Konec')),   
          this.encodeSpecialChars(this.$t('start', 'Začátek')), 
          this.encodeSpecialChars(this.$t('end', 'Konec')),   
        ]
      ];

      // Generate table
      doc.autoTable({
        head: headers,
        body: tableData,
        startY: 36,
        theme: 'grid',
        styles: {
          font: 'Roboto',
          fontSize: 10,
          cellPadding: 0.75,
          lineColor: [0, 0, 0],
          lineWidth: 0.1,
          halign: 'center'
        },
        headStyles: {
          fillColor: [255, 255, 255],
          textColor: [0, 0, 0],
          fontStyle: 'bold',
          halign: 'center',
          valign: 'middle'
        },
        columnStyles: {
          0: { cellWidth: 10, halign: 'center' }, // Den
          1: { cellWidth: 30, halign: 'center' }, // Začátek
          2: { cellWidth: 30, halign: 'center' }, // Konec
          3: { cellWidth: 30, halign: 'center' }, // Přestávka
          4: { cellWidth: 30, halign: 'center' }, // Konec
          5: { cellWidth: 30, halign: 'center' }, // Celkem hodin
        },
        margin: { left: 30 }, 
      });

      // SummaryFields
      const finalY = doc.previousAutoTable.finalY || 10;
      doc.text(this.encodeSpecialChars(this.$t('total_hours', 'Celkový čas') + ': ' + this.formatDuration(this.totalHours) + ' hod.'), 30, finalY + 10);
      if (this.includeSummaryFields) {
        const finalY = doc.previousAutoTable.finalY || 60;
        doc.text(this.encodeSpecialChars(this.$t('attendance_report.days_worked', 'Odpracované dny') + ': ' + this.daysWorked), 30, finalY + 15);
        doc.text(this.encodeSpecialChars(this.$t('event_type.vacation', 'Dovolená') + ': ' + this.eventCounts.vacation), 30, finalY + 20);
        doc.text(this.encodeSpecialChars(this.$t('event_type.illness', 'Nemocenská') + ': ' + this.eventCounts.illness), 30, finalY + 25);
        doc.text(this.encodeSpecialChars(this.$t('event_type.day_care', 'Návštěva lékaře') + ': ' + this.eventCounts.day_care), 30, finalY + 30);
        doc.text(this.encodeSpecialChars(this.$t('event_type.family_sick', 'OČR') + ': ' + this.eventCounts.family_sick), 30, finalY + 35);
        doc.text(this.encodeSpecialChars(this.$t('event_type.other', 'Jiné absence') + ': ' + this.eventCounts.other), 30, finalY + 40);
      }

      // Footer and signatures
      
      
      doc.text(this.encodeSpecialChars(this.$t('employee_signature', 'Podpis zaměstnance')) + ': ', 30, finalY + 50);
      doc.text(this.encodeSpecialChars(this.$t('date', 'Datum')) + ': ', 30, finalY + 60);

      // Save
      doc.save(`vykaz_prace_${monthYear.toLowerCase()}.pdf`);
    }
  }
}
</script>
