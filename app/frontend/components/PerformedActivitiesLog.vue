// ABOUTME: This component displays a log of all activities performed during the work day
// ABOUTME: It shows both work-related activities and other activities in chronological order
<template>
  <div class="performed-activities-log">
    <div >
      <div class="bg-white rounded-lg">
        <button @click="toggleShowLogs" class="w-full flex justify-between items-center p-3">
          <h2 class="text-title">
            {{ $t('todays_activities', "Záznam aktivity") }}
          </h2>
          <ChevronDown v-if="!showLogs" :size="18" class="text-gray-500" />
          <ChevronUp v-else :size="18" class="text-gray-500" />
        </button>

        <div v-if="showLogs" class="p-3 space-y-2">
          <div v-for="activity in todayActivities" :key="activity.id" class="flex items-start p-3 bg-gray-50 rounded-md hover:bg-gray-100 transition-colors">
            <div class="text-sm text-gray-500 mr-3 w-14 flex-shrink-0">{{ formatTime(activity.start_time) }}</div>
            <div class="flex-1 min-w-0">
              <div class="flex items-start">
                <LandPlot v-if="activity.work_id" :size="14" class="mr-1.5 text-blue-500 mt-0.5 flex-shrink-0" />
                <div class="flex-1 min-w-0">
                  <p class="text-sm break-words">{{ activity.description }}</p>
                  <p v-if="activity.work && activity.work.title" class="text-xs text-blue-600 mt-1 break-words">
                    {{ activity.work.title }}
                  </p>
                  <p v-if="activity.duration_in_text" class="text-xs text-gray-500 mt-1">{{ activity.duration_in_text }}</p>
                </div>
              </div>
            </div>
          </div>

          <div v-if="todayActivities.length === 0" class="text-center text-gray-500 py-3">
            {{ $t('no_activities_today', 'Žádné aktivity pro dnešní den') }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';
import { ChevronDown, ChevronUp, LandPlot } from 'lucide-vue-next';

export default {
  name: 'PerformedActivitiesLog',
  components: {
    ChevronDown,
    ChevronUp,
    LandPlot
  },
  
  data() {
    return {
      todayActivities: [],
      showLogs: false
    };
  },

  methods: {
    // Cookie helpers
    getDropdownState(key, defaultValue) {
      const value = this.getCookie(key);
      return value !== null ? value === 'true' : defaultValue;
    },
    
    setDropdownState(key, value) {
      this.setCookie(key, value.toString(), 365);
    },
    
    getCookie(name) {
      const value = `; ${document.cookie}`;
      const parts = value.split(`; ${name}=`);
      if (parts.length === 2) return parts.pop().split(';').shift();
      return null;
    },
    
    setCookie(name, value, days) {
      const date = new Date();
      date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
      const expires = `expires=${date.toUTCString()}`;
      document.cookie = `${name}=${value};${expires};path=/`;
    },
    
    toggleShowLogs() {
      this.showLogs = !this.showLogs;
      this.setDropdownState('performedActivitiesLog_showLogs', this.showLogs);
    },
    
    formatTime(time) {
      return new Date(time).toLocaleTimeString('cs-CZ', {
        hour: '2-digit', 
        minute: '2-digit'
      });
    },

    async fetchActivities() {
      try {
        const response = await axios.get('/daily_activities', {
          params: { date: new Date().toISOString().split('T')[0] }
        });
        this.todayActivities = response.data;
      } catch (error) {
        console.error('Error fetching activities:', error);
      }
    },

    async refreshActivities() {
      await this.fetchActivities();
    }
  },

  created() {
    // Initialize dropdown state from cookies
    this.showLogs = this.getDropdownState('performedActivitiesLog_showLogs', false);
  },
  
  mounted() {
    this.fetchActivities();

    // Listen for work activity changes to refresh the log
    document.addEventListener('work-activity-started', this.refreshActivities);
    document.addEventListener('work-activity-ended', this.refreshActivities);
  },

  beforeDestroy() {
    // Clean up event listeners
    document.removeEventListener('work-activity-started', this.refreshActivities);
    document.removeEventListener('work-activity-ended', this.refreshActivities);
  }
};
</script>