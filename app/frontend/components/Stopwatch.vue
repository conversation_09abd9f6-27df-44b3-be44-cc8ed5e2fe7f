<template>
  <div class="stopwatch">
    <p>{{ formattedTime }}</p>
    <button @click="reset">Reset</button>
  </div>
</template>

<script>
export default {
  data() {
    return {
      startTime: null,
      elapsedTime: 0,
      intervalId: null,
    };
  },
  computed: {
    formattedTime() {
      const totalSeconds = Math.floor(this.elapsedTime / 1000);
      const hours = String(Math.floor(totalSeconds / 3600)).padStart(2, '0');
      const minutes = String(Math.floor((totalSeconds % 3600) / 60)).padStart(2, '0');
      const seconds = String(totalSeconds % 60).padStart(2, '0');
      return `${hours}:${minutes}:${seconds}`;
    },
  },
  methods: {
    start() {
      this.startTime = Date.now() - this.elapsedTime;
      this.intervalId = setInterval(() => {
        this.elapsedTime = Date.now() - this.startTime;
        this.saveState();
      }, 1000);
    },
    reset() {
      clearInterval(this.intervalId);
      this.elapsedTime = 0;
      this.startTime = null;
      this.saveState();
    },
    saveState() {
      document.cookie = `stopwatch=${this.elapsedTime}; path=/`;
    },
    loadState() {
      const match = document.cookie.match(/(^| )stopwatch=([^;]+)/);
      if (match) {
        this.elapsedTime = parseInt(match[2], 10);
      }
    },
  },
  mounted() {
    this.loadState();
    this.start();
  },
  beforeDestroy() {
    clearInterval(this.intervalId);
  },
};
</script>

<style scoped>
.stopwatch {
  display: flex;
  flex-direction: column;
  align-items: center;
}
</style>