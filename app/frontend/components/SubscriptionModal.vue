<template>
  <div v-if="isVisible" class="modal-overlay" @click.self="close">
    <div class="modal-container">
      <div class="modal-header">
        <h2>{{ $t('order_request_title', 'Objednávka') }}</h2>
        <button type="button" class="modal-close" @click="close">&times;</button>
      </div>
      <div class="modal-content">
        <p>{{ $t('order_request.text', 'Objednáváte si Týmbox - ') }} <strong>{{ tier.toUpperCase() }}</strong>.</p>
        
        <form @submit.prevent="submitForm" class="subscription-form">
          <input type="hidden" v-model="formData.tier">
          
          <div class="billing-options">
            <div class="radio-group">
              <label class="radio-option">
                <input 
                  type="radio" 
                  name="billing_period" 
                  value="monthly" 
                  v-model="formData.billing_period"
                >
                <div class="option-content">
                  <div class="option-title">{{ $t('order_request.monthly', 'Měs<PERSON>čně') }}</div>
                  <div class="option-price">{{ monthlyPrice }} Kč</div>
                </div>
              </label>
              
              <label class="radio-option">
                <input 
                  type="radio" 
                  name="billing_period" 
                  value="annual" 
                  v-model="formData.billing_period"
                >
                <div class="option-content">
                  <div class="option-title">{{ $t('order_request.yearly', 'Na 1 rok') }}<span class="discount-badge">{{ $t('order_request.savings', 'Ušetříte 25%') }}</span></div>
                  <div class="option-price">{{ annualPrice }} Kč</div>
                </div>
              </label>
            </div>
          </div>
          
          <div class="form-group">
            <label for="company_name">Název společnosti</label>
            <input 
              type="text" 
              id="company_name" 
              v-model="formData.company_name" 
              required 
              class="form-control">
            <div v-if="errors.company_name" class="error-message">{{ errors.company_name }}</div>
          </div>
          
          <div class="form-group">
            <label for="company_id">IČO</label>
            <input 
              type="text" 
              id="company_id" 
              v-model="formData.company_id" 
              required 
              class="form-control">
            <div v-if="errors.company_id" class="error-message">{{ errors.company_id }}</div>
          </div>
          
          <div class="form-group">
            <label for="email">Email</label>
            <input 
              type="email" 
              id="email" 
              v-model="formData.email" 
              required 
              class="form-control">
            <div v-if="errors.email" class="error-message">{{ errors.email }}</div>
          </div>
          
          <div class="form-group">
            <label for="additional_info">Dodatečné informace (nepovinné)</label>
            <textarea 
              id="additional_info" 
              v-model="formData.additional_info" 
              class="form-control"></textarea>
          </div>
          
          <div class="form-actions">
            <button type="button" class="btn btn-outline" @click="close">Zrušit</button>
            <button type="submit" class="btn btn-primary" :disabled="isSubmitting">
              {{ isSubmitting ? 'Odesílání...' : 'Odeslat žádost' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  data() {
    return {
      isVisible: false,
      tier: '',
      isSubmitting: false,
      formData: {
        company_name: '',
        company_id: '',
        email: '',
        additional_info: '',
        tier: '',
        billing_period: 'monthly' 
      },
      errors: {},
      pricing: {
        plus: {
          monthly: 590,
          annual: 5310
        },
        premium: {
          monthly: 890,
          annual: 8010
        }
      }
    };
  },
  computed: {
    monthlyPrice() {
      return this.pricing[this.tier]?.monthly || 0;
    },
    annualPrice() {
      return this.pricing[this.tier]?.annual || 0;
    }
  },
  methods: {
    open(tier) {
      this.tier = tier;
      this.formData.tier = tier;
      this.isVisible = true;
      this.errors = {};
      document.body.classList.add('modal-open');
    },
    close() {
      this.isVisible = false;
      document.body.classList.remove('modal-open');
    },
    async submitForm() {
      this.isSubmitting = true;
      this.errors = {};
      
      try {
        const response = await axios.post('/subscriptions/process_request', {
          subscription_request: this.formData
        });
        
        this.close();
        
        document.dispatchEvent(new CustomEvent('flashMessage', {
          detail: { 
            text: 'Vaše objednávka byla odeslána.', 
            type: 'success' 
          }
        }));
        
        this.formData = {
          company_name: '',
          company_id: '',
          email: '',
          additional_info: '',
          tier: ''
        };
      } catch (error) {
        if (error.response && error.response.data && error.response.data.errors) {
          this.errors = error.response.data.errors;
        } else {
          document.dispatchEvent(new CustomEvent('flashMessage', {
            detail: { 
              text: 'Došlo k chybě při odesílání. Zkuste to prosím znovu.', 
              type: 'error' 
            }
          }));
        }
      } finally {
        this.isSubmitting = false;
      }
    }
  },
  mounted() {
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isVisible) {
        this.close();
      }
    });
  }
};
</script>

<style>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 900;
}

.modal-container {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6b7280;
}

.modal-content {
  padding: 1rem;
}

.subscription-form .form-group {
  margin-bottom: 1rem;
}

.subscription-form label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.subscription-form .form-control {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  margin-top: 1.5rem;
}

.error-message {
  color: #dc2626;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

body.modal-open {
  overflow: hidden;
}

.billing-options {
  margin-bottom: 1.5rem;
  padding: 1rem;
}

.billing-options h3 {
  margin-top: 0;
  margin-bottom: 0.75rem;
  font-size: 1rem;
  font-weight: 600;
}

.radio-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.radio-option {
  display: flex;
  align-items: flex-start;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
}

.radio-option:hover {
  border-color: #94a3b8;
}

.radio-option input[type="radio"] {
  margin-top: 0.25rem;
  margin-right: 0.75rem;
}

.radio-option input[type="radio"]:checked + .option-content {
  font-weight: 500;
}

.radio-option:has(input[type="radio"]:checked) {
  border-color: #0066cc;
  background-color: #f0f7ff;
}

.option-content {
  flex: 1;
}

.option-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.option-price {
  font-size: 1rem;
  font-weight: 600;
  margin-top: 0.25rem;
}

.monthly-equivalent {
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.discount-badge {
  background-color: #22C55E;
  color: white;
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  margin-left: 0.5rem;
}

@media (min-width: 640px) {
  .radio-group {
    flex-direction: row;
  }
  
  .radio-option {
    flex: 1;
  }
}

</style>