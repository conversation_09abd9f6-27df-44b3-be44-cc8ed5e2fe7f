<template>
  <br/>
  <div class="mainbox-title">
    {{ $t('summary', '<PERSON><PERSON><PERSON>led') }} {{ currentMonth }}
  </div>
  <div class="list">
    <div class="section-header">
    </div>
    <div v-if="loading" class="loading">
      {{ $t('loading', 'Načítání...') }}
    </div>
    <div v-else-if="error" class="text-muted center">
      {{ error }}
    </div>
    <div v-else-if="summaries.length === 0" class="text-muted center">
      {{ $t('no_data', 'Žádná data k zobrazení') }}
    </div>
    <div v-else class="contracts-list">
      <div 
        v-for="summary in summaries" 
        :key="summary.employee_id" 
        class="contracts-item"
      >
        <div class="contracts-item-main">
          <div class="contracts-item-info">
            <h3 class="contracts-item-name">{{ summary.employee_name }}</h3>
            <div class="contracts-item-meta">
              <span class="contracts-item-type">{{ $t('days_worked', 'Odpracované dny') }}: {{ summary.days_worked }}</span>
              <span class="contracts-item-type">{{ $t('total_hours', 'Celkem hodin') }}: {{ formatHours(summary.total_hours) }}</span>
            </div>
          </div>
          <div class="contracts-item-meta">
            <span class="text-muted">
              {{ $t('last_active', 'Naposledy aktivní') }}: {{ formatDate(summary.last_active) }}
            </span>
          </div>
        </div>
        <div>
        </div>
      </div>
    </div>
  </div>
</template>


<script>
import axios from 'axios';

export default {
  name: 'TeamSummary',
  data() {
    return {
      summaries: [],
      loading: true,
      error: null
    }
  },
  computed: {
    currentMonth() {
      return new Date().toLocaleDateString('cs-CZ', { 
        month: 'long', 
        year: 'numeric' 
      });
    }
  },
  methods: {
    async fetchTeamSummary() {
      this.loading = true;
      this.error = null;

      try {
        const response = await axios.get('/daily_logs/team_summary');
        this.summaries = response.data;
        this.loading = false;
      } catch (error) {
        console.error('Failed to fetch team summary', error);
        this.error = this.$t('team_summary.no_data_error', 'Nepodařilo se načíst přehled týmu');
        this.loading = false;
      }
    },
    formatHours(hours) {
      return hours ? `${hours.toFixed(2)} hod.` : '0 hod.';
    },
    formatDate(date) {
      if (!date) return this.$t('no_activity', 'Žádná aktivita');
      return new Date(date).toLocaleDateString('cs-CZ', {
        day: 'numeric',
        month: 'long',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    },
    getDetailUrl(summary) {
      return `/employees/${summary.employee_id}/details`;
    }
  },
  mounted() {
    this.fetchTeamSummary();
  }
}
</script>
