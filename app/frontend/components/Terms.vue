<template>
  <div v-if="show" class="modal-overlay" @click.self="close">
    <div class="modal-container">
      <div class="modal-header">
        <h2>{{ title }}</h2>
        <button @click="close" class="modal-close">&times;</button>
      </div>
      <div class="modal-content" v-html="content"></div>
      <div class="modal-footer">
        <button @click="close" class="btn btn-outline">Zavřít</button>
      </div>
    </div>
  </div>
</template>

<script>
import { legalContent, legalTitles } from '@/data/legalContent.js'

export default {
  data() {
    return {
      show: false,
      docType: 'tos', // 'tos' or 'gdpr'
      locale: this.detectLocale()
    }
  },
  computed: {
    title() {
      // Fallback to Czech if Slovak title is missing
      return legalTitles[this.locale]?.[this.docType] || legalTitles.cs[this.docType]
    },
    content() {
      // Fallback logic: if the requested locale or docType doesn't exist, default to Czech
      const contentForLocale = legalContent[this.locale] || legalContent.cs
      return contentForLocale[this.docType] || contentForLocale.tos
    }
  },
  mounted() {
    document.addEventListener('openTerms', this.handleTerms)
    document.addEventListener('keydown', this.handleEscKey)
    console.log('Terms component initialized with locale:', this.locale)
  },
  beforeUnmount() {
    document.removeEventListener('openTerms', this.handleTerms)
    document.removeEventListener('keydown', this.handleEscKey)
  },
  methods: {
    detectLocale() {
      // Try multiple ways to detect the locale
      const htmlLang = document.documentElement.lang
      const pathLocale = window.location.pathname.split('/')[1]
      const metaLocale = document.querySelector('meta[name="locale"]')?.getAttribute('content')
      
      console.log('Locale detection:', { 
        htmlLang, 
        pathLocale, 
        metaLocale,
        isLocaleInPath: /^\/(en|sk|cs)\//.test(window.location.pathname)
      })
      
      // If URL path starts with a locale like /sk/, use that
      if (pathLocale && ['en', 'sk', 'cs'].includes(pathLocale)) {
        return pathLocale
      }
      
      // Otherwise try HTML lang attribute
      if (htmlLang) {
        return htmlLang
      }
      
      // Or meta tag if available
      if (metaLocale) {
        return metaLocale
      }
      
      // Fallback to Czech (default)
      return 'cs'
    },
    handleEscKey(event) {
      if (event.key === 'Escape' && this.show) {
        this.close()
      }
    },
    handleTerms(event) {
      const type = event.detail
      console.log(`Opening terms modal: type='${type}', locale='${this.locale}'`)
      this.docType = type
      this.show = true
    },
    close() {
      this.show = false
    }
  }
}
</script>

<style scoped>
  /* Modal styles */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 900;
  }

  .modal-container {
    background-color: white;
    padding: 20px;
    border-radius: 12px;
    min-width: 380px;
    max-width: 600px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e5e5e5;
    padding-bottom: 10px;
    margin-bottom: 20px;
  }

  .modal-header h2 {
    margin: 0;
    font-size: 24px;
  }

  .modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
  }

  .modal-content {
    max-height: 60vh;
    overflow-y: auto;
  }

  .modal-footer {
    display: flex;
    justify-content: flex-end;
    padding-top: 10px;
    border-top: 1px solid #e5e5e5;
  }

  .btn-outline {
    background: none;
    border: 1px solid #ccc;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
  }

  .btn-outline:hover {
    background-color: #f0f0f0;
  }
</style>

<style>
  /* Core variables */
  :root {
    --primary-dark: #18973c;
    --neutral-dark: #2a2d34;
    --text: #2a2d34;
    --text-muted: #707580;
  }

  /* Terms and Conditions Styles - Global for modal content */
  .modal-content .terms {
    list-style-type: decimal;
    padding-left: 1.5em;
    font-size: 14px;
    color: var(--text);
    line-height: 1.5;
  }

  .modal-content .terms > li {
    margin-bottom: 1em;
  }

  .modal-content .terms ol {
    list-style-type: lower-alpha;
    padding-left: 1.5em;
  }

  .modal-content .terms ol ol {
    list-style-type: decimal;
    padding-left: 1.5em;
  }

  .modal-content h2 {
    font-size: 24px;
    color: var(--neutral-dark);
    margin-top: 0.875em;
    margin-bottom: 0.5em;
  }

  .modal-content .my-1 {
    margin: 1em 0;
  }

  .modal-content .terms-container {
    font-family: inherit;
  }

  .modal-content .terms-container h2 {
    font-size: 20px;
    color: var(--neutral-dark);
    margin-top: 1.5em;
    margin-bottom: 0.75em;
    font-weight: 600;
  }

  .modal-content .terms-container h3 {
    font-size: 18px;
    color: var(--neutral-dark);
    margin-top: 1.25em;
    margin-bottom: 0.5em;
    font-weight: 500;
  }

  .modal-content .terms-container p {
    margin-bottom: 1em;
    line-height: 1.6;
    color: var(--text);
  }

  .modal-content .terms-container ul, 
  .modal-content .terms-container ol {
    margin-bottom: 1em;
    padding-left: 1.5em;
  }

  .modal-content .terms-container li {
    margin-bottom: 0.5em;
    line-height: 1.5;
  }

  .modal-content .terms-container strong {
    font-weight: 600;
    color: var(--neutral-dark);
  }
</style>