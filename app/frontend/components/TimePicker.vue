<template>
  <div class="custom-time-picker">
    <button type="button" @click="togglePicker" class="picker-toggle-button" :disabled="disabled">
      {{ formattedTime || '--:--' }}
    </button>
    <div v-if="showPicker" class="picker-dropdown" ref="pickerDropdown">
      <div class="picker-section">
        <div class="picker-label">{{ $t('hour', 'Hodina') }}:</div>
        <div class="hour-grid">
          <button 
            v-for="hour in 24" 
            :key="`h-${hour - 1}`" 
            @click="selectHour(hour - 1)" 
            class="time-unit-button"
            :class="{ 'active': selectedHour === (hour - 1) }"
            :disabled="disabled"
            type="button"  
          >
            {{ String(hour - 1).padStart(2, '0') }}
          </button>
        </div>
      </div>
      <div class="picker-section" v-if="selectedHour !== null">
        <div class="picker-label">{{ $t('minute', 'Minuta') }}:</div>
        <div class="minute-options">
          <button 
            v-for="minute in [0, 15, 30, 45]" 
            :key="`m-${minute}`" 
            @click="selectMinute(minute)" 
            class="time-unit-button"
            :class="{ 'active': selectedMinute === minute }"
            :disabled="disabled"
            type="button"
          >
            {{ String(minute).padStart(2, '0') }}
          </button>
        </div>
      </div>
       <button @click="clearTime" class="clear-button" v-if="selectedHour !== null" :disabled="disabled">{{ $t('clear_time', 'Vymazat čas') }}</button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TimePicker',
  props: {
    modelValue: { 
      type: Object,
      default: () => ({ hour: null, minute: null })
    },
    disabled: {
        type: Boolean,
        default: false
    }
  },
  emits: ['update:modelValue'],
  data() {
    return {
      showPicker: false,
      selectedHour: this.modelValue?.hour ?? null,
      selectedMinute: this.modelValue?.minute ?? null
    };
  },
  computed: {
    formattedTime() {
      if (this.selectedHour !== null && this.selectedMinute !== null) {
        return `${String(this.selectedHour).padStart(2, '0')}:${String(this.selectedMinute).padStart(2, '0')}`;
      }
      return null;
    }
  },
  watch: {
    modelValue(newValue) {
        // Update internal state if v-model changes externally
        this.selectedHour = newValue?.hour ?? null;
        this.selectedMinute = newValue?.minute ?? null;
    }
  },
  methods: {
    togglePicker() {
      this.showPicker = !this.showPicker;
      if (this.showPicker) {
        this.$nextTick(() => {
            document.addEventListener('click', this.handleClickOutside, true);
        });
      } else {
        document.removeEventListener('click', this.handleClickOutside, true);
      }
    },
    handleClickOutside(event) {
        if (this.$refs.pickerDropdown && !this.$refs.pickerDropdown.contains(event.target) && !this.$el.contains(event.target)) {
            this.showPicker = false;
             document.removeEventListener('click', this.handleClickOutside, true);
        }
    },
    selectHour(hour) {
      this.selectedHour = hour;
      // Default minute to 00 when an hour is selected, only if minute wasn't already set for this hour
      if (this.selectedMinute === null || this.modelValue?.hour !== hour) {
          this.selectedMinute = 0;
      }
      this.emitUpdate();
    },
    selectMinute(minute) {
      this.selectedMinute = minute;
      this.emitUpdate();
      this.showPicker = false;
      document.removeEventListener('click', this.handleClickOutside, true);
    },
    clearTime() {
        this.selectedHour = null;
        this.selectedMinute = null;
        this.emitUpdate();
        this.showPicker = false;
        document.removeEventListener('click', this.handleClickOutside, true);
    },
    emitUpdate() {
      this.$emit('update:modelValue', { 
          hour: this.selectedHour, 
          minute: this.selectedMinute 
      });
    }
  },
  beforeUnmount() {
    // Clean up event listener
    document.removeEventListener('click', this.handleClickOutside, true);
  }
};
</script>

<style scoped>
.custom-time-picker {
  position: relative;
  display: inline-block;
}

.picker-toggle-button {
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: #fff;
  cursor: pointer;
  min-width: 70px;
  text-align: center;
}

.picker-toggle-button:disabled {
    background-color: #e9ecef;
    cursor: not-allowed;
    opacity: 0.7;
}

.picker-dropdown {
  position: absolute;
  background-color: white;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  margin-top: 4px;
  width: 280px; 
}

.picker-section {
  margin-bottom: 15px;
}

.picker-label {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #333;
}

.hour-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr); 
  gap: 5px;
}

.minute-options {
    display: flex;
    gap: 10px;
}

.time-unit-button {
  padding: 5px;
  border: 1px solid #eee;
  border-radius: 4px;
  background-color: #f8f9fa;
  cursor: pointer;
  text-align: center;
  font-size: 14px;
  transition: background-color 0.2s, border-color 0.2s;
}

.time-unit-button:hover {
  background-color: #e9ecef;
  border-color: #ddd;
}

.time-unit-button.active {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

.time-unit-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background-color: #e9ecef;
}

.clear-button {
    margin-top: 10px;
    padding: 6px 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
    background-color: #f1f1f1;
    cursor: pointer;
    font-size: 13px;
    color: #dc3545;
}
.clear-button:hover {
    background-color: #e2e6ea;
}
.clear-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}
</style> 