<template>
  <div class="form-container">
    <div class="form-card">
      <h3 class="form-title">{{ $t('booking.create', 'Create Booking') }}</h3>
      
      <form @submit.prevent="submitForm">
        <div class="form-group">
          <label for="client_name" class="form-label">{{ $t('name', 'Jméno') }}</label>
          <input type="text" id="client_name" v-model="form.client_name" class="form-input" required>
        </div>
        
        <div class="form-group">
          <label for="client_email" class="form-label">{{ $t('email', 'Email') }}</label>
          <input type="email" id="client_email" v-model="form.client_email" class="form-input" required>
        </div>
        
        <div class="form-group">
          <label for="client_phone" class="form-label">{{ $t('phone_optional', 'Telefon (Volitelné)') }}</label>
          <input type="tel" id="client_phone" v-model="form.client_phone" class="form-input">
        </div>
        
        <div class="form-group">
          <label for="preferred_date" class="form-label">{{ $t('booking.preferred_date', 'Preferované Datum') }}</label>
          <input type="date" id="preferred_date" v-model="form.preferred_date" class="form-input" required>
        </div>
        
        <div class="form-group">
          <label for="preferred_period" class="form-label">{{ $t('booking.preferred_time', 'Preferovaný Čas') }}</label>
          <select id="preferred_period" v-model="form.preferred_period" class="form-input">
          <option value="">{{ $t('select_time', 'Vyberte čas') }}</option>
          <option value="morning">{{ $t('morning', 'Dopoledne') }}</option>
          <option value="afternoon">{{ $t('afternoon', 'Odpoledne') }}</option>
          <option value="evening">{{ $t('evening', 'Večer') }}</option>
          </select>
        </div>
            
        <div class="form-group">
          <label for="message" class="form-label">{{ $t('message_optional', 'Zpráva (Volitelné)') }}</label>
          <textarea id="message" v-model="form.message" class="form-input form-textarea"></textarea>/>
        </div>
        
        <div class="form-actions">
          <button type="button" class="btn btn-outline" @click="$emit('cancel')">{{ $t('cancel', 'Zrušit') }}</button>
          <button type="submit" class="btn btn-primary">{{ $t('booking.create_booking', 'Vytvořit rezervaci') }}</button>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      form: {
        client_name: '',
        client_email: '',
        client_phone: '',
        preferred_date: '',
        preferred_period: '',
        message: ''
      }
    };
  },
  emits: ['submit', 'cancel'],
  methods: {
    submitForm() {
      this.$emit('submit', {...this.form});
    }
  }
};
</script>

<style scoped>
.form-container {
  margin-bottom: 1.5rem;
}

.form-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  padding: 1.5rem;
}

.form-title {
  font-size: 1.25rem;
  font-weight: 500;
  margin-bottom: 1.5rem;
}

.form-group {
  margin-bottom: 1.25rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.form-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-input:focus {
  border-color: #22C55E;
  box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.2);
  outline: none;
}

.form-textarea {
  min-height: 100px;
  resize: vertical;
  
}

input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="tel"],
input[type="url"],
input[type="search"],
input[type="date"],
select {
  all: unset; 
  font-family: "Inter" !important;
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #dee2e6;
  border-radius: 0.5rem;
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
  background-color: white;

  &:focus {
    border-color: #22C55E;
    box-shadow: 0 0 0 2px rgba(#22C55E, 0.2);
    outline: none;
  }

  &::placeholder {
    color: #666;
    font-family: "Inter" !important;
  }

  &:disabled {
    background-color: #f8f9fa;
    cursor: not-allowed;
  }
}

textarea#message.form-input.form-textarea {
  font-family: "Inter" !important;
  appearance: none !important;
  background-color: rgb(255, 255, 255) !important;
  border: 1px solid rgb(222, 226, 230) !important;
  border-radius: 8px !important;
  box-sizing: border-box !important;
  color: rgb(0, 0, 0) !important;
  padding: 12px !important;
  width: 100% !important;
  min-height: 100px !important;
  resize: vertical !important;
  font-size: 16px !important;
  line-height: 1.5 !important;
  white-space: pre-wrap !important;
  overflow-wrap: break-word !important;
  transition: border-color 0.2s, box-shadow 0.2s !important;
}

/* Additional override for extra specificity */
.form-card form textarea {
  font-family: "Inter" !important;
}

.form-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 1.5rem;
}

@media (max-width: 768px) {
  .form-actions {
    flex-direction: column;
  }
  
  .form-actions .btn,
  .form-actions .btn-secondary {
    width: 100%;
  }
}
</style>