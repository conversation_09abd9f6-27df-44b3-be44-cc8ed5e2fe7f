<template>
  <div class="mt-4 md:mt-0">
    <advanced-feature 
      feature="booking" 
      :title="$t('booking.system', 'Rezervační systém')"
      @access-determined="handleAccessDetermined"
    >
      <div class="flex flex-wrap justify-between items-center mb-4 md:mb-6 px-4 md:px-0 gap-4">
        <h2 class="text-xl md:text-2xl font-semibold text-gray-800">{{ $t('booking.system', 'Rezervační systém') }}</h2>
        <div class="flex gap-4">
          <button 
            class="btn"
            :class="activeTab === 'bookings' ? 'btn-primary' : 'btn-secondary'"
            @click="setTab('bookings')"
          >
            {{ $t('bookings', 'Rezervace') }}
          </button>
          <button 
            class="btn"
            :class="activeTab === 'links' ? 'btn-primary' : 'btn-secondary'"
            @click="setTab('links')"
          >
            {{ $t('booking.pages', 'Rezervačn<PERSON> stránky') }}
          </button>
        </div>
      </div>

      <!-- Booking Links Tab -->
      <div v-if="activeTab === 'links'">
        <div class="flex flex-wrap justify-between items-center mb-4 md:mb-6 px-4 md:px-0 gap-4">
          <h3 class="text-lg font-semibold text-gray-800">{{ $t('booking.pages', 'Rezervační stránky') }}</h3>
          <button 
            class="btn btn-primary"
            @click="showNewForm = true"
          >
            {{ $t('booking.new_page', 'Nová rezervační stránka') }}
          </button>
        </div>

        <div v-if="isLoading" class="flex justify-center items-center p-8">
          <span class="text-gray-500">{{ $t('loading', 'Načítání...') }}</span> 
        </div>

        <div v-else-if="bookingLinks.length === 0 && !isLoading" class="card">
          <div class="card-content">
            <p class="text-gray-600">{{ $t('booking.no_pages', 'Nemáte žádný rezervační stránky. Vytvořte svoji první.') }}</p>
          </div>
        </div>

        <div v-else class="flex flex-wrap gap-4 md:gap-6">
          <div v-for="link in bookingLinks" 
               :key="link.id" 
               class="w-full sm:w-[calc(50%-0.75rem)] lg:w-[calc(33.333%-1rem)] min-w-[380px] max-w-[500px]">
            <booking-link-detail
              :booking-link="link" 
              :company-slug="companySlug"
              @edit="editLink" 
              @delete="deleteLink" 
              @view="viewLinkBookings"
            />
          </div>
        </div>
      </div>

      <!-- Bookings Tab -->
      <div v-if="activeTab === 'bookings'">
        <div class="content-panel mb-4 md:mb-6">
          <div class="flex flex-wrap items-center gap-4">
            <div class="flex-grow">
            </div>
            <div class="flex-shrink-0">
              <label for="status-filter" class="form-label hidden">{{ $t('status', 'Stav') }}:</label>
              <select 
                id="status-filter" 
                v-model="statusFilter" 
                class="form-select"
              >
                <option value="active">{{ $t('active', 'Aktivní') }}</option>
                <option value="">{{ $t('all', 'Vše') }}</option>
                <option value="pending">{{ $t('new_f', 'Nová') }}</option>
                <option value="confirmed">{{ $t('confirmed_f', 'Potvrzená') }}</option>
                <option value="cancelled">{{ $t('cancelled_f', 'Zrušená') }}</option>
                <option value="rescheduled">{{ $t('rescheduled_f', 'Přeplánovaná') }}</option>
              </select>
            </div>
          </div>
        </div>

        <div v-if="isLoading" class="flex justify-center items-center p-8">
          <span class="text-gray-500">{{ $t('loading', 'Načítání...') }}</span> 
        </div>

        <div v-else-if="!filteredBookings || filteredBookings.length === 0" class="card">
          <div class="card-content">
            <p class="text-gray-600">{{ $t('booking.no_bookings', 'Žádné rezervace nenalezeny.') }}</p>
          </div>
        </div>

        <div v-else class="flex flex-wrap gap-4 md:gap-6">
          <div v-for="booking in filteredBookings" 
               :key="booking.id" 
               class="w-full sm:w-[calc(50%-0.75rem)] lg:w-[calc(33.333%-1rem)] min-w-[380px] max-w-[500px]">
            <show-booking 
              :booking="booking" 
              @confirm="confirmBooking" 
              @cancel="cancelBooking"
              @convert-to-work="convertToWork"
              @update="updateBooking"
            />
          </div>
        </div>
      </div>

      <!-- Modal for Selected Link Bookings -->
      <div v-if="selectedLink" class="modal-overlay">
        <div class="modal-container">
          <div class="modal-header">
            <h3 class="text-lg font-semibold">{{ $t('booking.for', 'Rezervace pro') }} {{selectedLink.name}}</h3>
            <button @click="selectedLink = null" class="close-btn">×</button>
          </div>
          <div class="central-modal-content">
            <div v-if="linkBookings.length > 0" class="flex flex-wrap gap-4 md:gap-6">
              <div v-for="booking in linkBookings" 
                   :key="booking.id" 
                   class="w-full">
                <show-booking 
                  :booking="booking" 
                  @confirm="confirmBooking" 
                  @cancel="cancelBooking"
                  @convert-to-work="convertToWork"
                  @update="updateBooking"
                />
              </div>
            </div>
            <div v-else class="card">
              <div class="card-content">
                <p class="text-gray-600">{{ $t('booking.no_link_bookings', 'Žádné rezervace pro tento odkaz.') }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Modal for Edit Link -->
      <div v-if="showEditForm && editingLink" class="modal-overlay">
        <div class="modal-container">
          <div class="modal-header">
            <h3 class="text-lg font-semibold">{{ $t('booking.edit_page', 'Upravit rezervační stránku') }}</h3>
            <button @click="() => { showEditForm = false; editingLink = null; }" class="close-btn">×</button>
          </div>
          <div class="central-modal-content">
            <booking-link-form 
              :booking-link="editingLink" 
              :is-edit="true"
              @submit="updateBookingLink" 
              @cancel="() => { showEditForm = false; editingLink = null; }"
            />
          </div>
        </div>
      </div>

      <!-- Modal for New Link -->
      <div v-if="showNewForm" class="modal-overlay" @click.self="showNewForm = false">
        <div class="modal-container">
          <div class="modal-header">
            <h3 class="text-lg font-semibold">{{ $t('booking.new_page', 'Nová rezervační stránka') }}</h3>
            <button @click="showNewForm = false" class="close-btn">×</button>
          </div>
          <div class="central-modal-content">
            <booking-link-form 
              @submit="createBookingLink" 
              @cancel="showNewForm = false"
            />
          </div>
        </div>
      </div>
    </advanced-feature>
  </div>
</template>

<script>
import AdvancedFeature from '../AdvancedFeature.vue';
import axios from 'axios';
import ShowBookingLink from './ShowBookingLink.vue';
import BookingLinkDetail from './BookingLinkDetail.vue';
import ShowBooking from './ShowBooking.vue';
import BookingLinkForm from './BookingLinkForm.vue';
import CentralModal from '../shared/CentralModal.vue';
import { sendFlashMessage } from '../../utils/flashMessage.js';

export default {
  components: {
    AdvancedFeature,
    ShowBookingLink,
    BookingLinkDetail,
    ShowBooking,
    BookingLinkForm,
    CentralModal
  },
  data() {
    return {
      activeTab: 'bookings',
      bookingLinks: [],
      bookings: [],
      companySlug: '',
      statusFilter: 'active', // Changed default value
      showNewForm: false,
      selectedLink: null,
      linkBookings: [],
      showEditForm: false,
      editingLink: null,
      isLoading: false
    };
  },
  computed: {
    filteredBookings() {
      if (this.statusFilter === 'active') {
        return this.bookings.filter(booking => booking.status !== 'cancelled');
      }
      if (!this.statusFilter) return this.bookings;
      return this.bookings.filter(booking => booking.status === this.statusFilter);
    }
  },
  methods: {
    handleAccessDetermined(hasAccess) {
      if (hasAccess) {
        this.fetchData();
      }
    },
    sendFlashMessage,
    setTab(tab) {
      this.activeTab = tab;
    },
    async fetchData() {
      if (this.isLoading) return;

      this.isLoading = true;
      try {
        const [linksResponse, bookingsResponse] = await Promise.all([
          axios.get('/booking_links', {
            headers: { 'Accept': 'application/json' }
          }),
          axios.get('/bookings/fetch', {
            headers: { 'Accept': 'application/json' }
          })
        ]);
        this.bookingLinks = linksResponse.data.booking_links;
        this.bookings = bookingsResponse.data.bookings;
        this.companySlug = bookingsResponse.data.company_slug;
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        this.isLoading = false;
      }
    },
    async createBookingLink(linkData) {
      try {
        const response = await axios.post('/booking_links', { booking_link: linkData }, {
          headers: { 'Accept': 'application/json' }
        });
        this.bookingLinks.push(response.data);
        this.showNewForm = false;
      } catch (error) {
        console.error('Error creating booking link:', error);
      }
    },
    editLink(link) {
      this.editingLink = link;
      this.showEditForm = true;
    },
    async deleteLink(link) {
      if (confirm(this.$t('booking.confirm_delete_page', 'Opravdu chcete smazat tuto rezervační stránku?'))) {
        try {
          await axios.delete(`/booking_links/${link.id}`);
          this.bookingLinks = this.bookingLinks.filter(l => l.id !== link.id);
          // Display flash message
          const flashMessage = { message: this.$t('booking.page_deleted', 'Rezervační stránka byla smazána.'), type: 'success' };
          this.$store.dispatch('flashMessages/addMessage', flashMessage);
        } catch (error) {
          console.error('Error deleting booking link:', error);
        }
      }
    },
    async viewLinkBookings(link) {
      this.selectedLink = link;
      try {
        const response = await axios.get(`/booking_links/${link.id}`);
        this.linkBookings = response.data.bookings || [];
      } catch (error) {
        console.error('Error fetching link bookings:', error);
      }
    },
    async confirmBooking(booking, options = {}) {
      try {
        const bookingData = {};
        if (options.specificTime) {
          bookingData.specific_time = options.specificTime;
        }
        if (options.preferredDate) {
          bookingData.preferred_date = options.preferredDate;
        }
        
        const response = await axios.post(
          `/bookings/${booking.id}/confirm`, 
          { booking: bookingData }, 
          { headers: { 'Accept': 'application/json' } }
        );
        
        if (response.data.success) {
          await this.fetchData();
          if (this.selectedLink) {
            await this.viewLinkBookings(this.selectedLink);
          }
          // Display flash message
          const flashMessage = { message: this.$t('booking.confirmed_success', 'Rezervace byla potvrzena.'), type: 'success' };
          this.$store.dispatch('flashMessages/addMessage', flashMessage);
        }
      } catch (error) {
        console.error('Error confirming booking:', error);
        alert(this.$t('booking.confirm_failed', 'Nepodařilo se potvrdit rezervaci.'));
      }
    },
    async updateBooking(booking, options = {}) {
      try {
        const bookingData = {};
        if (options.specificTime) {
          bookingData.specific_time = options.specificTime;
        }
        if (options.preferredDate) {
          bookingData.preferred_date = options.preferredDate;
        }
        if (options.preferredPeriod) {
          bookingData.preferred_period = options.preferredPeriod;
        }
        
        const response = await axios.put(
          `/bookings/${booking.id}`, 
          { booking: bookingData },
          { headers: { 'Accept': 'application/json' } }
        );
        
        await this.fetchData();
        
        if (this.selectedLink) {
          await this.viewLinkBookings(this.selectedLink);
        }
        
        alert(this.$t('booking.rescheduled_success', 'Rezervace byla úspěšně přeplánována.'));
      } catch (error) {
        console.error('Error updating booking:', error);
        alert(this.$t('booking.reschedule_error', 'Nastala chyba při přeplánování rezervace.'));
      }
    },
    async cancelBooking(booking) {
      if (confirm(this.$t('booking.confirm_cancel', 'Opravdu chcete zrušit tuto rezervaci?'))) {
        try {
          const response = await axios.post(`/bookings/${booking.id}/cancel`, {}, {
            headers: { 'Accept': 'application/json' }
          });
          if (response.data.success) {
            await this.fetchData();
            if (this.selectedLink) {
              await this.viewLinkBookings(this.selectedLink);
            }
            // Display flash message
            this.sendFlashMessage(this.$t('booking.cancelled_success', 'Rezervace byla zrušena.'), 'success');
          }
        } catch (error) {
          console.error('Error cancelling booking:', error);
          alert(this.$t('booking.cancel_failed', 'Nepodařilo se zrušit rezervaci.'));
        }
      }
    },
    async updateBookingLink(linkData) {
      try {
        const response = await axios.put(`/booking_links/${this.editingLink.id}`, { booking_link: linkData });
        const updatedIndex = this.bookingLinks.findIndex(link => link.id === this.editingLink.id);
        if (updatedIndex !== -1) {
          this.bookingLinks[updatedIndex] = response.data;
        }
        this.showEditForm = false;
        this.editingLink = null;
      } catch (error) {
        console.error('Error updating booking link:', error);
      }
    },
    async convertToWork(booking) {
      if (confirm(this.$t('booking.confirm_convert_to_work', 'Opravdu chcete převést tuto rezervaci na realizaci?'))) {
        try {
          const response = await axios.post(`/bookings/${booking.id}/convert_to_work`, {}, {
            headers: { 'Accept': 'application/json' }
          });
          if (response.data.success) {
            await this.fetchData();
            if (this.selectedLink) {
              await this.viewLinkBookings(this.selectedLink);
            }
            // Display flash message
            const flashMessage = { message: this.$t('booking.convert_to_work_success', 'Rezervace byla převedena na realizaci.'), type: 'success' };
            this.$store.dispatch('flashMessages/addMessage', flashMessage);
          }
        } catch (error) {
          console.error('Error converting to work:', error);
          alert(this.$t('booking.convert_to_work_failed', 'Nepodařilo se převést rezervaci na realizaci.'));
        }
      }
    }
  },
  mounted() {
    this.fetchData();
  }
};
</script>

<style scoped>
/* Consider moving shared modal styles to a global CSS file or a shared component style */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6); /* Darker overlay */
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000; /* Ensure modal is on top */
  padding: 1rem; /* Add some padding for smaller screens */
}

.modal-container {
  background-color: white;
  padding: 1.5rem; /* Increased padding */
  border-radius: 0.5rem; /* Softer rounded corners */
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15); /* More pronounced shadow */
  width: 100%;
  max-width: 600px; /* Limit max width for larger modals */
  max-height: 90vh; /* Limit max height and allow scrolling within modal content */
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e2e8f0; /* Lighter border */
  padding-bottom: 1rem;
  margin-bottom: 1rem;
}

.modal-header h3 {
  margin: 0; /* Remove default margin */
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.75rem; /* Larger close button */
  font-weight: bold;
  color: #4a5568; /* Darker gray for close button */
  cursor: pointer;
  padding: 0.25rem 0.5rem; /* Add some padding for easier clicking */
  line-height: 1; /* Ensure proper vertical alignment */
}

.close-btn:hover {
  color: #2d3748; /* Even darker gray on hover */
}

.central-modal-content {
  overflow-y: auto; /* Enable scrolling for content that exceeds max-height */
  padding-right: 0.5rem; /* Add a little padding to prevent scrollbar overlap */
}
</style>