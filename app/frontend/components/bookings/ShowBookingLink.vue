<template>
  <div class="booking-link-urls">
    <div class="url-section">
      <span class="url-label">{{ bookingLink.name }}</span>
      <div class="url-display">
        <span class="url-text">{{ publicBookingUrl }}</span>
        <button class="url-copy-btn" @click="copyUrl(publicBookingUrl)" title="Copy URL">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
          </svg>
        </button>
      </div>
    </div>
  </div>
    
</template>

<script>
export default {
  props: {
    bookingLink: {
      type: Object,
      required: true
    },
    compact: {
      type: Boolean,
      default: false
    },
    companySlug: {
      type: String,
      required: true
    }
  },
  emits: ['edit', 'delete', 'view'],
  computed: {
    publicBookingUrl() {
      return `${window.location.origin}/r/${this.companySlug}/${this.bookingLink.slug || this.bookingLink.id}`;
    },
  },
  methods: {
    copyUrl(url) {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(url)
          .then(() => {
            const event = new CustomEvent('flashMessage', {
              detail: { text: this.$t('url_copied', 'Adresa skopírovaná'), type: 'notice' }
            });
            document.dispatchEvent(event);
          })
          .catch(err => {
            console.error('Could not copy text: ', err);
          });
      } else {
        console.error('Clipboard API not supported');
      }
    }
  }
};
</script>

<style scoped>

.booking-link-urls {
  padding: 0.25rem;
  margin-bottom: 0.5rem;
}

.url-section {
  margin-bottom: 0.75rem;
}

.url-section:last-child {
  margin-bottom: 0;
}

.url-label {
  display: block;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.url-display {
  display: flex;
  align-items: center;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 0.5rem;
}

.url-text {
  flex: 1;
  font-size: 0.875rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.url-copy-btn {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.url-copy-btn:hover {
  color: #22C55E;
}

.booking-link-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  align-content: center;
  padding: 1rem;
  border-top: 1px solid #dee2e6;
  gap: 0.5rem;
}

@media (max-width: 768px) {
  .booking-link-actions {
    flex-direction: row;
  }
  
  .booking-link-actions .btn,
  .booking-link-actions .btn-secondary {
    width: 100%;
  }
}
</style>