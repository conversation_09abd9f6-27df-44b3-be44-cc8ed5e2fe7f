// ABOUTME: Minimal meeting display component for calendar grid (no drag support)
// ABOUTME: Shows meeting title and created by name in second row

<template>
  <div 
    :class="['calendar-meeting-item', statusClass, {
      'is-compact': isCompact
    }]"
    @click="handleClick"
    :title="meetingTooltip"
  >
    <div class="meeting-content">
      <div class="meeting-title">
        {{ getMeetingTitle() }}
      </div>
      <div v-if="!isCompact && getCreatedByName()" class="meeting-creator">
        {{ getCreatedByName() }}
      </div>
    </div>
  </div>
</template>

<script>
import { Users, Calendar, Clock } from 'lucide-vue-next';

export default {
  name: 'CalendarMeetingItem',
  components: {
    Users,
    Calendar,
    Clock
  },
  props: {
    meeting: {
      type: Object,
      required: true
    },
    isCompact: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    statusClass() {
      // Base class for all meetings
      const baseClass = 'meeting-base';
      
      // Check if meeting is cancelled
      if (this.meeting.status === 'cancelled') {
        return `${baseClass} meeting-inactive`;
      }
      
      return `${baseClass} meeting-active`;
    },
    
    showTime() {
      // Show time if meeting has confirmed_date and not compact mode
      if (this.isCompact) return false;
      return this.meeting.confirmed_date;
    },
    
    formatTime() {
      if (!this.meeting.confirmed_date) return '';
      const meetingTime = new Date(this.meeting.confirmed_date);
      return meetingTime.toLocaleTimeString(this.$i18n.locale, {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });
    },
    
    getCreatedByName() {
      if (this.meeting.created_by) {
        return this.meeting.created_by.email || 'Unknown';
      }
      return null;
    },
    
    meetingTooltip() {
      const parts = [this.getMeetingTitle()];
      if (this.showTime) parts.push(this.formatTime);
      if (this.meeting.description) parts.push(this.meeting.description);
      if (this.getCreatedByName()) parts.push(`Created by: ${this.getCreatedByName()}`);
      return parts.join(' - ');
    }
  },
  methods: {
    getMeetingTitle() {
      // Try various fields that might contain the meeting title
      return this.meeting.title || 
             this.meeting.subject || 
             this.meeting.name || 
             this.meeting.description ||
             `Meeting ${this.meeting.id}`;
    },
    
    handleClick() {
      this.$emit('click', this.meeting);
    }
  }
};
</script>

<style scoped>
.calendar-meeting-item {
  padding: 0.25rem 0.25rem;
  margin-bottom: 0.125rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: flex-start;
  min-height: 1.75rem;
  overflow: hidden;
}

.calendar-meeting-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.calendar-meeting-item.is-compact {
  padding: 0.125rem 0.375rem;
  font-size: 0.75rem;
  min-height: 1.25rem;
}

/* Meeting base styling - #657381 (blue-gray) */
.meeting-base {
  background-color: #657381;
  color: white;
}

.meeting-active {
  background-color: #657381;
  color: white;
}

.meeting-inactive {
  background-color: rgba(101, 115, 129, 0.4); /* Lighter version for cancelled */
  color: rgba(255, 255, 255, 0.7);
}

/* Content elements */
.meeting-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
  min-width: 0;
}

.meeting-title {
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
}

.meeting-creator {
  font-size: 0.75rem;
  opacity: 0.9;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
}
</style>