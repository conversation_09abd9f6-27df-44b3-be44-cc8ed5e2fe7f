// ABOUTME: Minimal work item display component for calendar grid with drag-drop support
// ABOUTME: Shows work title, location in second row, and highlights work items by status

<template>
  <div
    :class="['calendar-work-item', statusClass, {
      'is-draggable': isDraggable,
      'is-compact': isCompact,
      'needs-attention': needsAttention,
      'work-scheduled': isScheduled,
      'work-completed': work.status === 'completed'
    }]"
    @click="handleClick"
    :title="workTooltip"
  >
    <div class="work-icon">
      <LandPlot :size="14" />
    </div>
    <div class="work-content">
      <div v-if="!isCompact && getWorkTitle()" class="work-name" :class="{ 'completed-text': work.status === 'completed' }">
        {{ getWorkTitle() }}
      </div>
      <div v-if="work.location" class="work-location" :class="{ 'completed-text': work.status === 'completed' }">
        {{ work.location }}
      </div>
    </div>
  </div>
</template>

<script>
import { BellRing, Briefcase, Hand, LandPlot } from 'lucide-vue-next';

export default {
  name: 'CalendarWorkItem',
  components: {
    BellRing,
    LandPlot,
    Hand
  },
  props: {
    work: {
      type: Object,
      required: true
    },
    isDraggable: {
      type: Boolean,
      default: true
    },
    isCompact: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    statusClass() {
      // Base class for all work items
      const baseClass = 'work-base';

      // Check if work is completed
      if (this.work.status === 'completed') {
        return `${baseClass} work-completed-status`;
      }

      // Check if work is cancelled or archived
      if (this.work.status === 'cancelled' || this.work.status === 'archived') {
        return `${baseClass} work-inactive`;
      }

      return `${baseClass} work-active`;
    },

    needsAttention() {
      // Work items that need attention should shine/highlight
      return this.work.status === 'pending' || this.work.status === 'draft' || this.work.priority === 'high';
    },

    isScheduled() {
      // Work items that are scheduled to a specific time
      return this.work.scheduled_date || this.work.start_time;
    },

    workTooltip() {
      const parts = [this.work.title || this.work.name];
      if (this.work.location) parts.push(this.work.location);
      if (this.work.priority) parts.push(`Priority: ${this.work.priority}`);
      if (this.work.status) parts.push(`Status: ${this.work.status}`);
      return parts.join(' - ');
    }
  },
  methods: {
    getWorkTitle() {
      // Try various fields that might contain the work title
      return this.work.title ||
             this.work.name ||
             this.work.description ||
             `Work ${this.work.id}`;
    },

    handleClick() {
      this.$emit('click', this.work);

      // Could also emit edit request if needed
      if (this.isDraggable) {
        this.$emit('edit-request', this.work);
      }
    }
  }
};
</script>

<style scoped>
/*
 * OPTIMIZATION NOTES:
 * 1. Removed `box-shadow` from hover and drag states. It's very expensive to render.
 * 2. Removed the `linear-gradient` striped background. It requires a lot of "paint" work from the browser when many are on screen.
 * 3. Changed `transition: all` to be specific, which is more efficient.
 * 4. Enhanced the `transform` on the drag state to give feedback without the performance cost of a shadow.
*/

.calendar-work-item {
  position: relative;
  padding: 0.25rem;
  margin-bottom: 0.125rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: transform 0.2s ease;
  display: flex;
  align-items: flex-start;
  min-height: 1.75rem;
  overflow: hidden;
  gap: 0.15rem;
  border: 1px solid #b3e5fc;
  background-color: #e1f5fe;
  width: 100%;
  z-index: 1;
}

.calendar-work-item:hover {
  transform: translateY(1px);
}

.calendar-work-item.is-compact {
  padding: 0.125rem 0.375rem;
  font-size: 0.75rem;
  min-height: 1.25rem;
}

.work-base {
  background-color: #e1f5fe;
  color: #333;
}

.work-active {
  background-color: #e1f5fe;
  color: #333;
}

.work-inactive {
  background-color: #e1f5fe;
  color: #777;
}

.calendar-work-item.is-draggable {
  cursor: grab;
  background-color: #e1f5fe;
}

.calendar-work-item.needs-attention {
  --attention-bg: #fefce8;
  --attention-border: #facc15;
  color: #3f3f46;
  border-left: 3px solid var(--attention-border);
  background-color: var(--attention-bg);
}

.calendar-work-item.work-scheduled {
  --scheduled-bg: #f0f9ff;
  --scheduled-border: #0ea5e9;
  border-left: 3px solid var(--scheduled-border);
  background-color: var(--scheduled-bg);
}

.work-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
  min-width: 0;
}

.work-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  font-weight: 500;
}

.work-location {
  font-size: 0.75rem;
  opacity: 0.9;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
}

/* Dragging state - Optimized for fluid motion */
.calendar-work-item.sortable-ghost {
  opacity: 0.4;
  background: #e0e7ff; /* Added a subtle background to better indicate the drop zone */
}

.calendar-work-item.sortable-drag {
  cursor: grabbing;
  opacity: 0.9;
  /* A slightly larger scale provides a clear "lifted" effect without a shadow. */
  transform: scale(1.05);
}

/* Completed work styling */
.work-completed:hover {
  border-color: #4caf50;
  opacity: 0.9;
}

.work-completed-status {
  background-color: #e8f5e8;
}

.completed-text {
  text-decoration: line-through;
  color: #757575 !important;
}

.status-completed {
  font-size: 0.6rem;
  font-weight: 600;
  color: #2e7d32;
  text-transform: uppercase;
  background-color: #e8f5e8;
  padding: 1px 4px;
  border-radius: 2px;
  margin-top: 2px;
  align-self: flex-start;
}

.drag-handle-icon {
  position: absolute;
  top: 0.125rem;
  right: 0.125rem;
  color: #8b5cf6; /* Saturated purple */
  opacity: 0.6;
}

.work-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

/* Touch-friendly sizing for mobile */
@media (max-width: 768px) {
  .calendar-work-item {
    padding: 0.375rem;
    width: 90%;
    min-width: 300px;
  }

  .work-name {
    font-size: 0.875rem;
  }

  .work-location {
    font-size: 0.75rem;
  }
}
</style>