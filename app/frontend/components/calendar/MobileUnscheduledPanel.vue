<!-- ABOUTME: Mobile unscheduled works panel with tap-to-schedule functionality -->
<!-- ABOUTME: Provides expandable panel interface for scheduling unscheduled works on mobile devices -->

<template>
  <div v-if="unscheduledWorks.length > 0" class="mobile-unscheduled-panel" data-vue-component="mobile-unscheduled-panel">
    <!-- Panel Header -->
    <div 
      class="panel-header" 
      @click="togglePanel"
      data-action="toggle-panel"
      data-testid="mobile-panel-header"
    >
      <div class="header-content">
        <LandPlot :size="16" />
        <span class="header-title">
          {{ $t('unscheduled', 'Nezpracované') }} ({{ unscheduledWorks.length }})
        </span>
        <ChevronUp v-if="isExpanded" :size="16" />
        <ChevronDown v-else :size="16" />
      </div>
      
    </div>

    <!-- Panel Content -->
    <transition name="panel-slide">
      <div v-if="isExpanded" class="panel-content">
        
        <!-- Works List (always visible) -->
        <div class="works-list">
          <MobileWorkCard
            v-for="work in unscheduledWorks"
            :key="work.id"
            :work="work"
            :is-selected="selectedWork && selectedWork.id === work.id"
            @click.native="selectWork(work)"
            data-action="select-work"
          />
          
        </div>
        
        
      </div>
    </transition>
  </div>
</template>

<script>
import { LandPlot, ChevronUp, ChevronDown } from 'lucide-vue-next';
import { mapGetters, mapActions } from 'vuex';
import MobileWorkCard from './MobileWorkCard.vue';

export default {
  name: 'MobileUnscheduledPanel',
  components: {
    LandPlot,
    ChevronUp,
    ChevronDown,
    MobileWorkCard
  },
  
  data() {
    return {
      isExpanded: false,
      selectedWork: null,
      isScheduling: false
    };
  },
  
  computed: {
    ...mapGetters('calendarStore', ['unscheduledWorks'])
  },
  
  watch: {
    unscheduledWorks: {
      handler(newWorks) {
        console.log('🔍 MobileUnscheduledPanel: unscheduledWorks changed:', newWorks?.length || 0, newWorks);
      },
      immediate: true
    }
  },
  
  mounted() {
    // Check if calendar data needs to be fetched
    if (this.$store.state.calendarStore.works.length === 0) {
      this.$store.dispatch('calendarStore/fetchCalendarData', new Date());
    }
    
    // Add event listener for outside clicks
    document.addEventListener('click', this.handleOutsideClick);
  },
  
  beforeUnmount() {
    document.removeEventListener('click', this.handleOutsideClick);
  },
  
  methods: {
    ...mapActions('calendarStore', ['updateWorkDate', 'findFirstFreeSlot']),
    
    togglePanel() {
      this.isExpanded = !this.isExpanded;
      
      // Auto-expand when work is selected
      if (this.selectedWork && !this.isExpanded) {
        this.isExpanded = true;
      }
    },
    
    selectWork(work) {
      // Toggle selection - if same work is clicked, deselect it
      if (this.selectedWork && this.selectedWork.id === work.id) {
        this.selectedWork = null;
        this.$emit('work-deselected');
      } else {
        this.selectedWork = work;
        this.isExpanded = true;
        this.$emit('work-selected', work);
      }
    },
    
    
    async autoSchedule() {
      if (!this.selectedWork) return;
      
      this.isScheduling = true;
      
      try {
        const optimalDate = await this.findFirstFreeSlot(this.selectedWork);
        
        if (optimalDate) {
          await this.scheduleWork(this.selectedWork, optimalDate);
          this.clearSelection();
          this.$emit('work-scheduled', {
            work: this.selectedWork,
            date: optimalDate
          });
        } else {
          this.showNoFreeSlotsMessage();
        }
      } catch (error) {
        console.error('Auto-scheduling failed:', error);
        this.$emit('scheduling-error', error);
      } finally {
        this.isScheduling = false;
      }
    },
    
    async scheduleWork(work, date) {
      const dateString = date.toLocaleDateString('en-CA');
      
      await this.updateWorkDate({
        workId: work.id,
        newDate: dateString
      });
    },
    
    showNoFreeSlotsMessage() {
      // Show toast or modal
      this.$emit('no-free-slots', this.selectedWork);
    },
    
    handleOutsideClick(event) {
      if (!this.$el.contains(event.target) && this.selectedWork) {
        // Don't clear selection if clicking on schedule buttons
        if (event.target.closest('.schedule-btn')) {
          return;
        }
        this.selectedWork = null;
        this.$emit('work-deselected');
      }
    }
  }
};
</script>

<style scoped>
.mobile-unscheduled-panel {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #e5e7eb;
  z-index: 100;
}

.panel-header {
  padding: 0.5rem;
  background: #27A844;
  color: white;
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s ease;
  touch-action: manipulation;
}

.panel-header:active {
  background: #e9ecef;
}

.panel-header.has-selection {
  background: #e3f2fd;
  border-bottom: 2px solid #2196f3;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: space-between;
}

.header-title {
  font-weight: 600;
  flex: 1;
}

.header-title.selected {
  color: #1976d2;
}

.selection-hint {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.panel-content {
  max-height: 30vh;
  overflow-y: auto;
  background: white;
}

.works-list {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.empty-state {
  text-align: center;
  color: #6b7280;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.empty-state p {
  margin: 0;
  font-style: italic;
}

.selection-state {
  padding: 1rem;
}

.selection-actions {
  display: flex;
  gap: 0.75rem;
  margin-top: 1rem;
}

.selection-actions .btn {
  flex: 1;
  padding: 0.75rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  touch-action: manipulation;
}

.btn-outline {
  background: #f8f9fa;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-outline:hover {
  background: #e9ecef;
}

.btn-outline:active {
  transform: scale(0.98);
}

.btn-primary {
  background: #2196f3;
  color: white;
}

.btn-primary:hover {
  background: #1976d2;
}

.btn-primary:active {
  transform: scale(0.98);
}

.btn-primary:disabled {
  background: #9e9e9e;
  cursor: not-allowed;
}

/* Panel slide animation */
.panel-slide-enter-active,
.panel-slide-leave-active {
  transition: all 0.3s ease;
}

.panel-slide-enter-from,
.panel-slide-leave-to {
  max-height: 0;
  opacity: 0;
}

.panel-slide-enter-to,
.panel-slide-leave-from {
  max-height: 30vh;
  opacity: 1;
}

/* Touch-friendly sizing */
@media (max-width: 768px) {
  .panel-header {
    padding: 1rem 0.5rem;
  }
  
  .selection-actions .btn {
    padding: 1rem;
    font-size: 1.125rem;
  }
}
</style>