<!-- ABOUTME: Mobile-optimized work card component for touch-friendly scheduling interface -->
<!-- ABOUTME: Displays unscheduled works with tap-to-select functionality in mobile unscheduled panel -->

<template>
  <div
    class="mobile-work-card"
    :class="{ 'selected': isSelected }"
    data-vue-component="mobile-work-card"
  >
    <div class="work-header">
      <h4 class="work-title">{{ work.title }}</h4>
      <div class="work-meta">
        <div v-if="work.priority" class="work-priority" :class="`priority-${work.priority}`">
          {{ formatPriority(work.priority) }}
        </div>
        <div v-if="work.status" class="work-status" :class="`status-${work.status}`">
          {{ formatStatus(work.status) }}
        </div>
      </div>
    </div>
    
    <div class="work-details">
      <div v-if="work.confirmed_time || work.specific_time || work.preferred_period" class="work-time">
        <Clock :size="12" />
        <span>{{ formatWorkTime() }}</span>
      </div>

      <div v-if="work.location" class="work-location">
        <MapPin :size="12" />
        <span>{{ work.location }}</span>
      </div>

      <div v-if="work.assigned_users_count || (work.work_assignments && work.work_assignments.length)" class="work-team">
        <Users :size="12" />
        <span>{{ getAssignedCount() }} </span>
      </div>

      <div v-if="work.estimated_duration || work.expected_duration" class="work-duration">
        <Clock :size="12" />
        <span>{{ formatDuration(work.estimated_duration || work.expected_duration) }}</span>
      </div>
    </div>
    
  </div>
</template>

<script>
import { MapPin, Users, Clock, CheckCircle } from 'lucide-vue-next';

export default {
  name: 'MobileWorkCard',
  components: {
    MapPin,
    Users,
    Clock,
    CheckCircle
  },
  props: {
    work: {
      type: Object,
      required: true
    },
    isSelected: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    formatPriority(priority) {
      const priorities = {
        low: this.$t('priority.low', 'Nízká'),
        medium: this.$t('priority.medium', 'Střední'),
        high: this.$t('priority.high', 'Vysoká'),
        urgent: this.$t('priority.urgent', 'Urgentní')
      };
      return priorities[priority] || priority;
    },

    formatStatus(status) {
      const statuses = {
        scheduled: this.$t('works.scheduled', 'Naplánováno'),
        in_progress: this.$t('works.in_progress', 'Probíhá'),
        completed: this.$t('works.completed', 'Dokončeno'),
        cancelled: this.$t('statuses.cancelled', 'Zrušeno'),
        unprocessed: this.$t('works.unprocessed', 'Nezpracováno')
      };
      return statuses[status] || status;
    },

    formatWorkTime() {
      if (this.work.confirmed_time) {
        return this.formatTime(this.work.confirmed_time);
      }
      if (this.work.specific_time) {
        return this.formatTime(this.work.specific_time);
      }
      if (this.work.preferred_period) {
        const periods = {
          morning: this.$t('morning', 'Dopoledne'),
          afternoon: this.$t('afternoon', 'Odpoledne'),
          allday: this.$t('all_day', 'Celý den')
        };
        return periods[this.work.preferred_period] || this.work.preferred_period;
      }
      return '';
    },

    formatTime(dateString) {
      const date = new Date(dateString);
      return date.toLocaleTimeString('cs-CZ', {
        hour: '2-digit',
        minute: '2-digit'
      });
    },

    getAssignedCount() {
      if (this.work.assigned_users_count) {
        return this.work.assigned_users_count;
      }
      if (this.work.work_assignments && this.work.work_assignments.length) {
        return this.work.work_assignments.length;
      }
      return 0;
    },

    formatDuration(minutes) {
      if (!minutes) return '';
      if (minutes < 60) {
        return `${minutes}min`;
      }
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;

      if (remainingMinutes === 0) {
        return `${hours}h`;
      }
      return `${hours}h ${remainingMinutes}min`;
    },

    truncateDescription(description) {
      if (!description) return '';
      return description.length > 80
        ? description.substring(0, 80) + '...'
        : description;
    },
    
  }
};
</script>

<style scoped>
.mobile-work-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  touch-action: manipulation;
  margin-bottom: 0.25rem;
}

.mobile-work-card:hover {
  border-color: #d1d5db;
}

.mobile-work-card:active {
}

.mobile-work-card.selected {
  background: #e3f2fd;
  border-color: #2196f3;
}

.work-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.work-title {
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0;
  flex: 1;
  margin-right: 0.5rem;
}

.work-meta {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
  align-items: flex-end;
}

.work-priority {
  padding: 0.125rem 0.375rem;
  border-radius: 3px;
  font-size: 0.625rem;
  font-weight: 600;
  text-transform: uppercase;
}

.work-status {
  padding: 0.125rem 0.375rem;
  border-radius: 3px;
  font-size: 0.625rem;
  font-weight: 600;
  text-transform: uppercase;
}

.priority-low {
  background: #f0f9ff;
  color: #0369a1;
}

.priority-medium {
  background: #fffbeb;
  color: #d97706;
}

.priority-high {
  background: #fef2f2;
  color: #dc2626;
}

.priority-urgent {
  background: #7c2d12;
  color: white;
}

.status-scheduled {
  background: #e0f2fe;
  color: #0277bd;
}

.status-in_progress {
  background: #fff3e0;
  color: #f57c00;
}

.status-completed {
  background: #e8f5e8;
  color: #2d7d32;
}

.status-cancelled {
  background: #fce4ec;
  color: #c2185b;
}

.status-unprocessed {
  background: #fefce8;
  color: #854d0e;
}

.work-details {
  display: flex;
  flex-wrap: wrap;
  gap: 0.375rem;
  margin-bottom: 0.375rem;
}

.work-time,
.work-location,
.work-team,
.work-duration {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.90rem;
}

.work-description {
  font-size: 0.90rem;
  line-height: 1.3;
  margin-bottom: 0.375rem;
}

.selection-indicator {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #1976d2;
  font-size: 0.9rem;
  font-weight: 500;
  margin-top: 0.375rem;
  padding-top: 0.375rem;
  border-top: 1px solid #e3f2fd;
}

/* Touch-friendly sizing for mobile */
@media (max-width: 768px) {
  .mobile-work-card {
    padding: 0.625rem;
    margin-bottom: 0.375rem;
  }

  .work-title {
    font-size: 0.9rem;
  }

  .work-details {
    gap: 0.375rem;
  }

  .work-time,
  .work-location,
  .work-team,
  .work-duration {
    font-size: 0.9rem;
  }
}
</style>