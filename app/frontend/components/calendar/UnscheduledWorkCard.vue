<!-- ABOUTME: Small card component for displaying unscheduled works in the sidebar for drag-and-drop scheduling -->
<!-- ABOUTME: Provides basic work information to help with scheduling decisions -->

<template>
  <div class="unscheduled-work-card">
    <div class="card-header">
      <div class="work-title">{{ work.title }}</div>
      <div class="work-date">
      </div>
    </div>
    
    <div v-if="work.location" class="work-location">
      <MapPin :size="12" />
      {{ work.location }}
    </div>
    
    <div v-if="assignedPeople.length > 0" class="assigned-people-section">
      <div class="assigned-people-list">
        <span 
          v-for="(person, index) in assignedPeople" 
          :key="person.id"
          class="person-chip"
        >
          {{ person.first_name }} {{ person.last_name }}
        </span>
      </div>
    </div>
    
    <div class="work-details">
      <div v-if="work.status" class="status" :class="statusClass">
        {{ $t(`works.${work.status}`, work.status) }}
      </div>
      
      <div v-if="work.expected_duration" class="duration">
        <Clock :size="12" />
        {{ work.expected_duration }}h
      </div>
      
      <div v-if="work.priority" class="priority" :class="priorityClass">
        {{ $t(`priority.${work.priority}`, work.priority) }}
      </div>
    </div>
  </div>
</template>

<script>
import { Clock, MapPin, Users } from 'lucide-vue-next';

export default {
  name: 'UnscheduledWorkCard',
  components: {
    Clock,
    MapPin,
    Users
  },
  props: {
    work: {
      type: Object,
      required: true
    }
  },
  computed: {
    priorityClass() {
      return `priority-${this.work.priority}`;
    },
    
    statusClass() {
      return `status-${this.work.status}`;
    },
    
    assignedPeople() {
      // Works have work_assignments array with contract relationships
      if (this.work.work_assignments && this.work.work_assignments.length > 0) {
        return this.work.work_assignments.map(assignment => assignment.contract).filter(Boolean);
      }
      return [];
    },
    
  },
  methods: {
    formatCreatedDate(dateString) {
      const date = new Date(dateString);
      return date.toLocaleDateString(this.$i18n.locale, { 
        month: 'short', 
        day: 'numeric' 
      });
    },
    
    truncateText(text, maxLength) {
      if (!text) return '';
      if (text.length <= maxLength) return text;
      return text.substring(0, maxLength) + '...';
    }
  }
};
</script>

<style scoped>
.unscheduled-work-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
  cursor: grab;
  transition: all 0.2s ease;
}

.unscheduled-work-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.unscheduled-work-card:active {
  cursor: grabbing;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.work-title {
  color: #1f2937;
  line-height: 1.4;
  flex: 1;
  margin-right: 8px;
}

.work-date {
  color: #6b7280;
  white-space: nowrap;
}

.work-details {
  display: flex;
  gap: 6px;
  margin-bottom: 4px;
  align-items: center;
  flex-wrap: wrap;
}

.duration {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #6b7280;
}

.priority {
  padding: 2px 6px;
  border-radius: 12px;
  text-transform: uppercase;
}

.priority-high {
  background-color: #fef2f2;
  color: #dc2626;
}

.priority-medium {
  background-color: #fefbf0;
  color: #d97706;
}

.priority-low {
  background-color: #f0fdf4;
  color: #16a34a;
}

.status {
  padding: 2px 10px;
  border-radius: 12px;
  text-transform: uppercase;
}

.status-scheduled {
  background-color: #e0f2fe;
  color: #0277bd;
}

.status-in_progress {
  background-color: #fff3e0;
  color: #f57c00;
}

.status-completed {
  background-color: #e8f5e8;
  color: #2d7d32;
}

.status-cancelled {
  background-color: #fce4ec;
  color: #c2185b;
}

.status-unprocessed {
  background-color: #fefce8;
  color: #3f3f46;
  border: 1px solid #facc15;
}

.work-location {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #6b7280;
  margin-bottom: 6px;
}

/* Assigned People Section - Critical for Scheduling */
.assigned-people-section {
  margin-bottom: 6px;
}

.assigned-people-header {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #6b7280;
  margin-bottom: 3px;
}

.people-label {
  text-transform: uppercase;
}

.assigned-people-list {
  display: flex;
  flex-wrap: wrap;
  gap: 3px;
}

.person-chip {
  padding: 2px 6px;
  background-color: #f3f4f6;
  color: #374151;
  border-radius: 10px;
  border: 1px solid #e5e7eb;
  white-space: nowrap;
}

/* Future: When contract colors are added */
.person-chip.contract-color {
  /* This will be used for contract-based color coding */
}

/* Dragging styles */
.sortable-ghost {
  opacity: 0.4;
}

.sortable-drag {
  opacity: 0;
}
</style>