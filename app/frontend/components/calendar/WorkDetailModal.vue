<!-- ABOUTME: Mobile-optimized work detail modal component -->
<!-- ABOUTME: Full-screen modal with slide-up animation for displaying WorkShow component -->

<template>
  <Teleport to="body">
    <Transition name="modal-fade">
      <div
        v-if="modelValue"
        class="modal-overlay"
        @click="closeModal"
        data-vue-component="work-detail-modal"
      >
        <Transition name="modal-slide-up" appear>
          <div
            class="modal-panel"
            role="dialog"
            aria-modal="true"
            :aria-labelledby="modalTitleId"
            tabindex="-1"
            @click.stop
          >
            <header class="modal-header">
              <h2 :id="modalTitleId" class="modal-title">
                {{ work.title || 'Work Details' }}
              </h2>
              <button 
                class="modal-close-btn" 
                @click="closeModal" 
                aria-label="Close"
                data-action="close-modal"
              >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                </svg>
              </button>
            </header>
            <div class="modal-content">
              <!-- Embed the existing WorkShow component here -->
              <WorkShow :work="work" />
            </div>
          </div>
        </Transition>
      </div>
    </Transition>
  </Teleport>
</template>

<script>
import WorkShow from '../works/WorkShow.vue';

export default {
  name: 'WorkDetailModal',
  components: {
    WorkShow
  },
  props: {
    modelValue: {
      type: Boolean,
      required: true
    },
    work: {
      type: Object,
      required: true
    }
  },
  emits: ['update:modelValue'],
  data() {
    return {
      modalTitleId: `modal-title-${Math.random().toString(36).substring(2, 9)}`
    };
  },
  watch: {
    modelValue(isOpen) {
      if (isOpen) {
        document.body.style.overflow = 'hidden';
        // Focus the modal container after animation
        this.$nextTick(() => {
          const modalPanel = this.$el.querySelector('.modal-panel');
          if (modalPanel) {
            modalPanel.focus();
          }
        });
      } else {
        // Small delay ensures the style is removed after the transition
        setTimeout(() => {
          document.body.style.overflow = '';
        }, 350); // Match transition duration
      }
    }
  },
  mounted() {
    document.addEventListener('keydown', this.handleKeydown);
  },
  beforeUnmount() {
    // Cleanup: ensure body scroll is restored if component is destroyed while open
    document.body.style.overflow = '';
    document.removeEventListener('keydown', this.handleKeydown);
  },
  methods: {
    closeModal() {
      this.$emit('update:modelValue', false);
    },
    handleKeydown(e) {
      if (e.key === 'Escape' && this.modelValue) {
        this.closeModal();
      }
    }
  }
};
</script>

<style scoped>
/* Overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: flex-end; /* Aligns panel to the bottom for slide-up */
  z-index: 999;
}

/* Modal Panel */
.modal-panel {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 95vh; /* A bit less than full-screen feels more like a modal */
  max-width: 700px; /* Max-width for tablet/desktop */
  background-color: white;
  border-radius: 16px 16px 0 0; /* Rounded top corners */
  box-shadow: 0px -4px 20px rgba(0, 0, 0, 0.15);
  overflow: hidden; /* Ensures content respects the border-radius */
  z-index: 1000;
}

/* Header */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e5e7eb; /* Subtle separator */
  flex-shrink: 0; /* Prevents header from shrinking */
  background: white;
}

.modal-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  color: #374151;
}

.modal-close-btn {
  background: #f3f4f6;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #4b5563;
  transition: background-color 0.2s ease;
}

.modal-close-btn:hover {
  background: #e5e7eb;
}

.modal-close-btn:active {
  transform: scale(0.95);
}

.modal-close-btn svg {
  width: 20px;
  height: 20px;
}

/* Content */
.modal-content {
  flex-grow: 1; /* Takes up all remaining space */
  overflow-y: auto; /* This is the key for scrollable content */
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
  padding: 16px;
  background: white;
}

/* Transitions */
.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}

.modal-slide-up-enter-active,
.modal-slide-up-leave-active {
  transition: transform 0.35s cubic-bezier(0.16, 1, 0.3, 1);
}

.modal-slide-up-enter-from,
.modal-slide-up-leave-to {
  transform: translateY(100%);
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .modal-panel {
    height: 95vh;
    border-radius: 16px 16px 0 0;
  }
  
  .modal-header {
    padding: 16px;
  }
  
  .modal-title {
    font-size: 1.2rem;
  }
  
  .modal-close-btn {
    width: 36px;
    height: 36px;
  }
  
  .modal-content {
    padding: 16px;
  }
}
</style>