<template>
  <div class="company-logo-container">
    <img v-if="logoUrl" :src="logoUrl" :alt="companyName + ' ' + $t('logo_alt_suffix', 'Logo')" class="company-logo">
    <div v-else class="logo-placeholder">{{ companyName ? companyName.substring(0, 2).toUpperCase() : $t('question_mark', '?') }}</div>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  name: 'CompanyLogo',
  props: {
    companyId: {
      type: [Number, String],
      required: true
    },
    companyName: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      logoUrl: ''
    };
  },
  created() {
    this.fetchLogo();
  },
  methods: {
    fetchLogo() {
      axios.get(`/companies/${this.companyId}/edit.json`)
        .then(response => {
          this.logoUrl = response.data.company.logo_url;
        })
        .catch(error => {
          console.error('Error fetching company logo:', error);
        });
    }
  }
};
</script>

<style scoped>
.company-logo-container {
  margin-right: 15px;
  width: 100px;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #eee;
  overflow: hidden;
  border-radius: 4px;
  flex-shrink: 0;
  background-color: #fff;
}

.company-logo {
  max-width: 100%;
  max-height: 100%;
  object-fit: cover;
}

.logo-placeholder {
  font-size: 18px;
  font-weight: bold;
  color: #aaa;
  text-align: center;
}
</style> 