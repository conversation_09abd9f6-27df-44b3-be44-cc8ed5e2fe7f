<!--
ABOUTME: ContractShow.vue displays detailed contract information in a modal-style dialog with user avatar, contact info, and management actions
ABOUTME: Features grid layout, status badges, dropdown actions, and confirmation modal for contract operations like suspend/terminate/delete
-->
<template>
  <div ref="modalContent" @click="handleModalClick">
    <div v-if="loading" class="p-6">{{ $t('loading', 'Načítání...') }}</div>
    <div v-else-if="error" class="p-6 text-red-500 text-center">{{ error }}</div>
    <template v-else>
      <!-- Main Content -->
      <div>
        <!-- User Profile Section -->
        <div class="flex items-start justify-between mb-6">
          <div class="flex items-center">
            <div class="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mr-4" data-testid="user-avatar">
              <span class="text-2xl font-bold text-gray-500">{{ userInitials }}</span>
            </div>
            <div>
              <h3 class="text-lg font-bold text-gray-900">{{ contract.first_name }} {{ contract.last_name }}</h3>
              <p class="text-gray-500">{{ contract.job_title || $t('no_job_title', 'Bez pracovního titulu') }}</p>
            </div>
          </div>
          <span v-if="isCurrentUser" class="badge badge-warning">{{ $t('contracts.this_is_you', 'Tohle jste vy') }}</span>
        </div>

        <!-- Details Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6 mb-6 contract-details-grid">
          <div class="contract-details-item">
            <label class="block font-medium text-gray-500 mb-1 contract-label">{{ $t('contracts.contract_type_label', 'Typ pracovního poměru') }}</label>
            <p class="text-gray-900 font-medium truncate contract-value" :title="contract.contract_type || $t('unspecified_f', 'Neuvedeno')">{{ contract.contract_type || $t('unspecified_f', 'Neuvedeno') }}</p>
          </div>
          
          <div class="contract-details-item">
            <label class="block font-medium text-gray-500 mb-1 contract-label">{{ $t('email', 'Email') }}</label>
            <a v-if="contract.email" :href="'mailto:' + contract.email" class="text-blue-600 font-medium block truncate contract-value" :title="contract.email">
              {{ contract.email }}
            </a>
            <p v-else class="text-gray-900 font-medium contract-value">{{ $t('no_email_present', 'Bez e-mailu') }}</p>
          </div>
          
          <div class="contract-details-item">
            <label class="block font-medium text-gray-500 mb-1 contract-label">{{ $t('phone', 'Telefon') }}</label>
            <a v-if="contract.phone" :href="'tel:' + contract.phone" class="text-gray-900 font-medium block truncate contract-value" :title="contract.phone">
              {{ contract.phone }}
            </a>
            <p v-else class="text-gray-900 font-medium contract-value">{{ $t('no_phone_present', 'Bez telefonu') }}</p>
          </div>
          
          <div class="contract-details-item">
            <label class="block font-medium text-gray-500 mb-1 contract-label">{{ $t('status_label', 'Status') }}</label>
            <div class="flex items-center">
              <p class="text-green-600 font-medium truncate contract-value" v-if="contract.status === 'active'" :title="translatedStatus">
                {{ translatedStatus }}
              </p>
              <p class="text-yellow-600 font-medium truncate contract-value" v-else-if="contract.status === 'suspended'" :title="translatedStatus">
                {{ translatedStatus }}
              </p>
              <p class="text-red-600 font-medium truncate contract-value" v-else-if="contract.status === 'terminated'" :title="translatedStatus">
                {{ translatedStatus }}
              </p>
              <p class="text-gray-600 font-medium truncate contract-value" v-else :title="translatedStatus">
                {{ translatedStatus }}
              </p>
            </div>
          </div>
          
          <div class="contract-details-item">
            <label class="block font-medium text-gray-500 mb-1 contract-label">{{ $t('access_label', 'Přístup') }}</label>
            <div class="flex flex-col">
              <p v-if="contract.user_id" class="text-green-600 font-medium contract-value">
                {{ $t('connected', 'Připojen') }}
              </p>
              <p v-else class="text-yellow-600 font-medium contract-value">
                {{ $t('waiting_for_confirmation', 'Čeká na potvrzení') }}
              </p>
              <p v-if="!contract.user_id && invitationDate" class="text-gray-500 text-xs mt-1 contract-invitation-date">
                ({{ $t('sent_on', 'Odesláno:') }} {{ invitationDate }})
              </p>
            </div>
          </div>

          <!-- Role Selection for connected users -->
          <div v-if="contract.user_id && isOwner" class="md:col-span-2 contract-details-item">
            <label class="block font-medium text-gray-500 mb-1 contract-label">{{ $t('role_label', 'Role') }}</label>
            <select v-model="selectedRole" class="form-select form-select-sm max-w-xs contract-value">
              <option v-for="role in availableRoles" :key="role" :value="role">
                {{ roleTranslations[role] || role }}
              </option>
            </select>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="p-6 bg-gray-50 rounded-b-lg">
        <div class="flex flex-col sm:flex-row items-center justify-between">
          <div class="flex items-center space-x-2 mb-4 sm:mb-0">
            <button @click="$emit('edit')" class="btn btn-small btn-secondary" data-testid="edit-button">
              {{ $t('edit', 'Upravit') }}
            </button>
            
            <!-- Dropdown Menu -->
            <div class="relative" ref="dropdown">
              <button @click="toggleDropdown" class="btn btn-small btn-outline flex items-center" data-testid="actions-dropdown">
                {{ $t('contracts.more_actions', 'Další akce') }}
                <ChevronDown :size="16" class="ml-1" />
              </button>
              <div v-if="showDropdown" class="absolute bottom-full mb-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                <template v-if="contract.status === 'active'">
                  <a @click="suspendContract" class="block px-4 py-2 text-sm text-gray-700  cursor-pointer">
                    {{ $t('suspend', 'Pozastavit') }}
                  </a>
                  <a @click="terminateContract" class="block px-4 py-2 text-sm text-gray-700  cursor-pointer">
                    {{ $t('terminate', 'Ukončit') }}
                  </a>
                </template>
                <template v-if="contract.status === 'suspended'">
                  <a @click="reactivateContract" class="block px-4 py-2 text-sm text-gray-700  cursor-pointer">
                    {{ $t('activate', 'Aktivovat') }}
                  </a>
                  <a @click="terminateContract" class="block px-4 py-2 text-sm text-gray-700  cursor-pointer">
                    {{ $t('terminate', 'Ukončit') }}
                  </a>
                </template>
                <a v-if="isOwner && contract.status !== 'deleted'" @click="confirmDelete" class="block px-4 py-2 text-sm text-red-600  cursor-pointer">
                  {{ $t('contracts.delete_permanently', 'Trvale smazat') }}
                </a>
              </div>
            </div>
          </div>
          
          <div class="flex items-center space-x-2">
            <button v-if="canResendInvitation"
                    @click="resendInvitation"
                    class="btn btn-small btn-light"
                    data-testid="resend-invitation-button">
              {{ $t('contracts.resend_invitation', 'Znovu odeslat pozvání') }}
            </button>
            <button v-if="contract.user_id && isOwner"
                    @click="updateRole" 
                    class="btn btn-small btn-primary"
                    data-testid="save-button">
              {{ $t('save', 'Uložit') }}
            </button>
          </div>
        </div>
      </div>

      <!-- Delete Confirmation Modal (basic structure, can be improved) -->
      <div v-if="showDeleteConfirm" class="modal-overlay" @click.self="cancelDelete">
        <div class="modal-container">
           <div class="modal-header">
            <h3>{{ $t('contracts.permanent_delete_warning_title', 'POZOR! Nevratné smazání') }}</h3>
            <button class="close-btn" @click="cancelDelete">&times;</button>
           </div>
          <div class="central-modal-content">
            <p>{{ $t('contracts.permanent_delete_warning_text_1', 'Trvale smažete kontrakt') }} <strong>{{ contract.first_name }} {{ contract.last_name }}</strong> {{ $t('contracts.permanent_delete_warning_text_2', 'včetně všech záznamů, aktivit a událostí!') }}</p>
            <p class="mt-4">{{ $t('to_confirm_enter', 'Pro potvrzení zadejte:') }} <strong>{{ deleteConfirmationCode }}</strong></p>
            <input v-model="confirmationInput" type="text" class="form-input mt-2" :placeholder="$t('enter_confirmation_code', 'Zadejte potvrzovací kód')">

          </div>
          <div class="modal-footer">
             <button @click="cancelDelete" class="btn btn-outline">{{ $t('cancel', 'Zrušit') }}</button>
            <button @click="executeDelete" class="btn btn-danger" :disabled="confirmationInput !== deleteConfirmationCode">
              {{ $t('contracts.delete_permanently', 'Trvale smazat') }}
            </button>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import axios from 'axios'
import { ChevronDown } from 'lucide-vue-next'

export default {
  components: {
    ChevronDown
  },
  props: {
    contractId: {
      type: [Number, String],
      required: true
    },
    currentUserId: {
      type: Number,
      default: null
    },
    userRole: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      contract: {},
      loading: true,
      error: null,
      showDeleteConfirm: false,
      confirmationInput: '',
      deleteConfirmationCode: '',
      selectedRole: '',
      availableRoles: [],
      showDropdown: false,
    }
  },
  computed: {
    userInitials() {
      if (!this.contract.first_name || !this.contract.last_name) return 'N/A'
      return (this.contract.first_name.charAt(0) + this.contract.last_name.charAt(0)).toUpperCase()
    },
    roleTranslations() {
      return {
        owner: this.$t('roles.owner', 'Vlastník'),
        employee: this.$t('roles.employee', 'Zaměstnanec'),
        admin: this.$t('roles.admin', 'Administrátor'),
        supervisor: this.$t('roles.supervisor', 'Supervizor'),
        manager: this.$t('roles.manager', 'Manažer')
      }
    },
    isCurrentUser() {
      return this.contract.user_id === this.currentUserId
    },
    isOwner() {
      return this.userRole === 'owner';
    },
    canResendInvitation() {
      return this.contract.email && !this.contract.user_id
    },
    invitationDate() {
      if (!this.contract.invitation_sent_at) return 'N/A';
      return new Date(this.contract.invitation_sent_at).toLocaleString(this.$i18n.locale, {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    },
    translatedStatus() {
       const statusTranslations = {
        active: this.$t('statuses.active_m', 'Aktívny'),
        suspended: this.$t('statuses.suspended_m', 'Pozastavený'),
        terminated: this.$t('statuses.terminated_m', 'Ukončený'),
        deleted: this.$t('statuses.deleted_m', 'Smazaný'),
        pending_invitation: this.$t('statuses.pending_invitation', 'Čaká na pozvání') 
      };
      return statusTranslations[this.contract.status] || this.contract.status;
    },
    statusBadgeClass() {
      const statusClasses = {
        active: 'badge-success',
        suspended: 'badge-warning',
        terminated: 'badge-danger',
        deleted: 'badge-danger',
        pending_invitation: 'badge-gray'
      };
       return statusClasses[this.contract.status] || 'badge-gray';
    },
     invitationStatusText() {
      return this.contract.user_id ? this.$t('connected', 'Připojen') : this.$t('waiting_for_confirmation', 'Čeká na potvrzení');
    },
    invitationStatusBadgeClass() {
      return this.contract.user_id ? 'badge-success' : 'badge-gray';
    }
  },
  mounted() {
    this.fetchContract()
  },
  beforeDestroy() {
    // No document listeners to clean up anymore
  },
  methods: {
    fetchContract() {
      this.loading = true
      
      axios.get(`/contracts/${this.contractId}`, {
        headers: { 'Accept': 'application/json' }
      })
      .then(response => {
        this.contract = response.data
        this.selectedRole = response.data.current_role || ''
        this.availableRoles = response.data.available_roles || []
        this.loading = false
      })
      .catch(error => {
        console.error('Error fetching contract:', error)
        this.error = this.$t('contracts.error_loading_data', 'Nepodařilo se načíst data o kontraktu')
        this.loading = false
      })
    },
    toggleDropdown() {
      this.showDropdown = !this.showDropdown
    },
    handleModalClick(event) {
      // Handle click-outside for dropdown within the modal
      if (this.showDropdown && this.$refs.dropdown && !this.$refs.dropdown.contains(event.target)) {
        this.showDropdown = false
      }
    },
    resendInvitation() {
      if (!confirm(this.$t('contracts.confirm_resend_invitation', 'Potvrďte opětovné odeslání pozvánky'))) return
      
      const invitationData = {
        email: this.contract.email,
        first_name: this.contract.first_name,
        last_name: this.contract.last_name
      }
      
      axios.post('/api/v1/auth/send_invitation', invitationData, {
        headers: { 'Accept': 'application/json' }
      })
        .then(response => {
          this.$emit('message', { 
            type: 'success', 
            text: response.data.message || this.$t('contracts.invitation_resent', 'Pozvánka odeslána znovu') 
          })
        })
        .catch(error => {
          console.error('Error resending invitation:', error)
          const errorMessage = error.response?.data?.error || this.$t('contracts.error_resending_invitation', 'Chyba při odesílání pozvánky')
          this.$emit('message', { type: 'error', text: errorMessage })
        })
    },
    updateRole() {
      if (!this.selectedRole) return
      
      axios.post(`/contracts/${this.contractId}/update_role`, {
        role_name: this.selectedRole
      })
      .then(response => {
        this.$emit('message', { type: 'success', text: response.data.message })
      })
      .catch(error => {
        console.error('Error updating role:', error)
        this.$emit('message', { 
          type: 'error', 
          text: error.response?.data?.message || this.$t('contracts.error_updating_role', 'Chyba při aktualizaci role') 
        })
      })
    },
    suspendContract() {
      this.showDropdown = false
      if (!confirm(this.$t('contracts.confirm_suspend', 'Opravdu chcete pozastavit tento kontrakt? Zaměstnanec nebude mít přístup do pracovního prostoru.'))) return
      
      axios.post(`/contracts/${this.contractId}/suspend`)
        .then(response => {
          this.contract.status = 'suspended';
          this.$emit('message', { type: 'success', text: response.data.message || this.$t('contracts.contract_suspended', 'Kontrakt byl pozastaven') })
        })
        .catch(error => {
          console.error('Error suspending contract:', error)
          this.$emit('message', { type: 'error', text: this.$t('contracts.error_suspending_contract', 'Chyba při pozastavení kontraktu') })
        })
    },
    
    reactivateContract() {
      this.showDropdown = false
      axios.post(`/contracts/${this.contractId}/reactivate`)
        .then(response => {
          this.contract.status = 'active';
          this.$emit('message', { type: 'success', text: response.data.message || this.$t('contracts.contract_reactivated', 'Kontrakt byl znovu aktivován') })
        })
        .catch(error => {
          console.error('Error reactivating contract:', error)
          this.$emit('message', { type: 'error', text: this.$t('contracts.error_reactivating_contract', 'Chyba při aktivaci kontraktu') })
        })
    },
    
    terminateContract() {
      this.showDropdown = false
      if (!confirm(this.$t('contracts.confirm_terminate', 'POZOR: Opravdu chcete UKONČIT tento kontrakt? Tato akce je nevratná!'))) return
      
      axios.post(`/contracts/${this.contractId}/terminate`)
        .then(response => {
          this.contract.status = 'terminated';
          this.$emit('message', { type: 'success', text: response.data.message || this.$t('contracts.contract_terminated', 'Kontrakt byl ukončen') })
        })
        .catch(error => {
          console.error('Error terminating contract:', error)
          this.$emit('message', { type: 'error', text: this.$t('contracts.error_terminating_contract', 'Chyba při ukončení kontraktu') })
        })
    },
    confirmDelete() {
      this.showDropdown = false
      this.deleteConfirmationCode = `SMAZAT-${this.contractId}`;
      this.showDeleteConfirm = true;
    },
    
    cancelDelete() {
      this.showDeleteConfirm = false;
      this.confirmationInput = '';
    },
    
    executeDelete() {
      if (this.confirmationInput !== this.deleteConfirmationCode) return;
      
      axios.delete(`/contracts/${this.contractId}`, {
        data: { confirmation_code: this.confirmationInput }
      })
        .then(() => {
          this.$emit('deleted')
          this.$emit('message', { type: 'success', text: this.$t('contracts.contract_deleted_permanently', 'Kontrakt byl trvale smazán') })
          this.showDeleteConfirm = false;
        })
        .catch(error => {
          console.error('Error deleting contract:', error)
          this.$emit('message', { type: 'error', text: error.response?.data?.errors?.[0] || this.$t('contracts.error_deleting_contract', 'Chyba při mazání kontraktu') })
        })
    }
  }
}
</script>