<template>
  <div v-for="log in logs" :key="log.id" class="log-entry">
    <div class="log-time">
      {{ new Date(log.start_time).toLocaleString('cz-CS', {
          hour: '2-digit',
          minute: '2-digit',
          hour12: false,
        }) 
      }}
      -
      <span v-if="!log.end_time" class="log-runs">
        {{ '<PERSON><PERSON><PERSON><PERSON> b<PERSON>' }}
      </span>
      {{ log.end_time ? new Date(log.end_time).toLocaleString('cz-CS', {
          hour: '2-digit',
          minute: '2-digit',
          hour12: false,
        }) : '' 
      }}
      <span v-if="showDelete" class="trash-icon" @click="confirmDelete(log.id)">&#x2716;</span> 
      <strong>
        {{ log.duration ? (Math.floor(log.duration / 3600) > 0 ? Math.floor(log.duration / 3600) + ' hod. ' : '') + Math.floor((log.duration % 3600) / 60) + ' min.' : '' }}
      </strong>
    </div>
    <div class="log-description">
      <span v-if="!log.isEditing" @click="editLog(log)">
        {{ log.description ? log.description : ' - ' }}
      </span>
      <div v-else class="edit-container">
        <textarea 
          v-model="log.description" 
          @blur="saveLog(log)" 
          class="edit-textarea">
        </textarea>
        <button @click="saveLog(log)" class="save-button">Uložit</button>
      </div>

    </div>
  </div>
</template>
  
<script>
import axios from 'axios';
import { updateLog } from '@/services/logService';


export default {
  props: {
    logs: {
      type: Array,
      required: true
    },
    showDelete: {
      type: Boolean,
      default: true
    }
  },
  emits: ['delete-log', 'log-updated'],
  computed: {
    isSpa() {
      // Check if we're in SPA mode by looking for Vue Router
      return this.$route !== undefined;
    }
  },
  methods: {
    editLog(log) {
      log.isEditing = true;
    },
    async saveLog(log) {
      log.isEditing = false;
      const locale = !this.isSpa && this.$route?.params?.locale ? this.$route.params.locale : null;
      const result = await updateLog(log.id, log.description, locale, this.isSpa);
      if (result.success) {
        this.$emit('log-updated'); // Notify parent to refresh logs
      } else {
        console.error('Error updating log:', result.error);
      }
    },
    confirmDelete(logId) {
      const log = this.logs.find(l => l.id === logId);
      const activityCount = log && log.daily_activities ? log.daily_activities.length : 0;
      
      let confirmMessage = 'Skutečně chcete smazat tento záznam?';
      if (activityCount > 0) {
        confirmMessage += `\n\nTímto bude smazáno také ${activityCount} aktivit spojených s tímto záznamem.`;
      }
      
      if (window.confirm(confirmMessage)) {
        this.deleteLog(logId);
      }
    },
    async deleteLog(logId) {
      try {
        // Use API endpoint in SPA mode, legacy endpoint otherwise
        const url = this.isSpa ? `/api/v1/daily_logs/${logId}` : `/daily_logs/${logId}`;
        await axios.delete(url);
        this.$emit('delete-log', logId);
      } catch (error) {
        console.error('Error deleting log:', error);
      }
    },
  },
};
</script>

<style scoped>
  .log-entry {
    text-align: left;
    margin: 12px 0;
    padding: 0 12px;
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 400px;
  }
  .log-runs {
    color: white;
    background-color: #FFD700;
    padding: 1px 6px;
    border-radius: 5px;
  }
  .log-description {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .edit-container {
    display: flex;
    align-items: center;
    width: 100%;
  }
  .edit-textarea {
    flex-grow: 1;
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 8px;
    resize: none;
    font-family: "Inter";
    line-height: 1.5;
    width: 100%;
    border: 1px solid #ccc;
    border-radius: 5px;
    background-color: #fff;
    color: #333;
    outline: none;
    transition: border-color 0.3s;
  }
  .trash-icon {
    cursor: pointer;
    margin-right: 10px;
    color: #ccc;
  }
  .save-button {
    margin: 8px;
    padding: 8px 16px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }
  .save-button:hover {
    background-color: #45a049;
  }
</style>