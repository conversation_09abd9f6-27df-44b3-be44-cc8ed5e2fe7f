<template>
  <div class="daily-logs-index">
    <div class="box-header">
      <div>
      </div>
      <div v-if="!showReport" class="log-navigation">
        <span @click="previousDay" class="arrow-left"></span>
        <h2>{{ formatDate(selectedDate) }}</h2>
        <span @click="nextDay" class="arrow-right"></span>
      </div>
    </div>
    <div class="daily-logs-box">  
      <daily-logs-app v-if="!showReport" :selected-date="selectedDate"></daily-logs-app>
      <daily-logs-report v-else :selected-month="selectedMonth"></daily-logs-report>
    </div>
  </div>
</template>
  
<script>
import DailyLogsApp from './DailyLogsApp.vue';
import DailyLogsReport from './DailyLogsReport.vue';

export default {
  components: { DailyLogsApp, DailyLogsReport },
  data() {
    return {
      selectedDate: new Date(), // Initially show logs for today
      showReport: false, // Initially show daily logs
      selectedMonth: this.getCurrentMonth(), // Initially show the current month
    };
  },
  methods: {
    toggleReport() {
      this.showReport = !this.showReport;
    },
    previousDay() {
      this.selectedDate.setDate(this.selectedDate.getDate() - 1);
      this.selectedDate = new Date(this.selectedDate); // Trigger reactivity
      this.showForm = false;
    },
    nextDay() {
      this.selectedDate.setDate(this.selectedDate.getDate() + 1);
      this.selectedDate = new Date(this.selectedDate); // Trigger reactivity
    },
    formatDate(date) {
      return new Date(date).toLocaleString('cs-CZ', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
      });
    },
    getCurrentMonth() {
      const today = new Date();
      // return `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}`;
      return today
    },
  },
};
</script>
  
<style scoped>
  .arrow-left {
    display: inline-block;
    width: 0;
    height: 0;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-right: 10px solid #22C55E;
    cursor: pointer;
    margin-right: 8px;
  }
  .arrow-right {
    display: inline-block;
    width: 0;
    height: 0;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-left: 10px solid #22C55E;
    cursor: pointer;
    margin-left: 8px;
  }
  .log-navigation {
    font-weight: 600;
    width: 100%;
    padding: 8px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
  .daily-logs-index {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }
  .daily-logs-box {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    min-width: 400px;
    max-width: 400px;
  }

  .box-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    max-width: 400px;
    margin: 10px;
    position: sticky;
    top: 0;
    background-color: white;
    z-index: 800;
    padding: 10px;
  }


  .toggle-report-link {
    cursor: pointer;
    text-decoration: underline;
    font-weight: 600;
  }

  .toggle-report-link:hover {
    color: black;
  }

</style>