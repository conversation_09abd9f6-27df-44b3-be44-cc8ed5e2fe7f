<template>
  <div class="daily-logs-report">
    <div class="month-selector">
    </div>
    <table>
    <thead>
      <tr>
      <th>Date</th>
      <th>Start Time</th>
      <th>End Time</th>
      <th>Duration</th>
      <th>Note</th>
      </tr>
    </thead>
    <tbody>
      <tr
      v-for="(day, index) in daysInMonth"
      :key="index"
      :class="{ weekend: isWeekend(day.date), free: isHoliday(day.date) }"
      >
      <td>{{ day.date }}</td>
      <td>{{ formatTime(day.start_time) || '-' }}</td>
      <td>{{ formatTime(day.end_time) || '-' }}</td>
      <td>{{ formatDuration(day.duration) || '-' }}</td>
      <td>{{ day.description || '-' }}</td>
      </tr>
    </tbody>
    </table>
  </div>
</template>
  
<script>
import axios from 'axios';

export default {
  props: {
    selectedMonth: {
      type: Date,
      required: true,
    },
  },
  data() {
    return {
      logs: [],
      // selectedMonth: this.getCurrentMonth(),
      holidays: [],
    };
  },
  computed: {
    daysInMonth() {
      // Logic to generate all days for the current month
      const today = new Date();
      const year = today.getFullYear();
      const month = today.getMonth();
      const days = new Date(year, month +1, 0).getDate(); // Days in month
      const result = [];
      for (let i = 2; i <= days+1; i++) {
        const date = new Date(year, month, i).toISOString().split('T')[0];
        result.push({
          date,
          ...this.logs.find((log) => log.date === date), // Merge log data
        });
      }
      return result;
    },
  },
  methods: {
    isWeekend(date) {
      const day = new Date(date).getDay();
      return day === 0 || day === 6; // Sunday or Saturday
    },
    isHoliday(date) {
      return this.holidays.includes(date);
    },
    // getCurrentMonth() {
    //   const today = new Date();
    //   return `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}`;
    // },
    async fetchLogs() {
      try {
        const formattedMonth = this.selectedMonth.toISOString().split('T')[0];
        const response = await axios.get('/daily_logs/report', {
          params: { date: formattedMonth },
        });
        this.logs = response.data.logs;
        this.holidays = response.data.holidays;
      } catch (error) {
        console.error('Error fetching logs:', error);
      }
    },
    formatTime(datetime) {
      if (!datetime) return null;
      const date = new Date(datetime);
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      return `${hours}:${minutes}`;
    },
    formatDuration(duration) {
      if (!duration) return null;
      const hours = Math.floor(duration / 3600);
      const minutes = Math.floor((duration % 3600) / 60);
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
    },
  },
  mounted() {
    this.fetchLogs();
  },
};
</script>
  
<style>
  .weekend {
    background-color: #f0f8ff;
  }
  .free {
    background-color: #ffe4e1;
  }
  .daily-logs-report {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    min-width: 400px;
  }
  .month-selector {
    margin-bottom: 16px;
  }
</style>