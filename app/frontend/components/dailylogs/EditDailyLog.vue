<template>
  <div>
    <form @submit.prevent="submitEdit">
      <textarea
        v-model="log.description"
        placeholder="Krátký souhrn aktivit (1-2 věty)?"
        class="styled-textarea"
        autofocus
      ></textarea>

      <div class="predefined-links">
        <div>
          <a href="#" @click.prevent="setDescription('')" class="bubble-link">Bez textu</a>
        </div>
        <div>
          <a href="#" @click.prevent="setDescription('Byl jsem na oběde.')" class="bubble-link">Oběd</a>
        </div>
        <div>
          <a href="#" @click.prevent="setDescription('Byl jsem u klienta.')" class="bubble-link">Byl jsem u klienta</a>
        </div>
        <div>
          <a href="#" @click.prevent="setDescription('Na dnes konec.')" class="bubble-link">Nashle</a>
        </div>
      </div>
     
      <div>
        <button type="submit">Ukončit</button>
      </div>

    </form>
  </div>
</template>

<script>
import axios from 'axios';
import { updateLog } from '@/services/logService';


export default {
  props: {
    logs: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      log: { description: '' },
      isLoading: true,
      error: null,
    };
  },
  computed: {
    isSpa() {
      // Check if we're in SPA mode by looking for Vue Router
      return this.$route !== undefined;
    }
  },
  methods: {
    setDescription(value) {
      this.log.description = value;
    },
    fetchLastLog() {
      // Use API endpoint in SPA mode, legacy endpoint otherwise
      const url = this.isSpa ? '/api/v1/daily_logs/last' : '/daily_logs/last';
      
      axios
        .get(url)
        .then((response) => {
          this.log = response.data;
        })
        .catch((error) => {
          console.error('Error fetching last log:', error);
        });
    },
    async submitEdit() {
      try {
        // First update the description
        const locale = !this.isSpa && this.$route?.params?.locale ? this.$route.params.locale : null;
        await updateLog(this.log.id, this.log.description, locale, this.isSpa);
        
        // Then end the daily log
        const url = this.isSpa ? `/api/v1/daily_logs/${this.log.id}` : `/daily_logs/${this.log.id}`;
        const endTime = new Date().toISOString();
        
        await axios.put(url, { 
          daily_log: { 
            description: this.log.description,
            end_time: endTime 
          } 
        });
        
        this.$emit('log-updated');
        this.$emit('switch-to-form');
      } catch (error) {
        console.error('Error updating log:', error);
      }
    }
  },
  mounted() {
    this.fetchLastLog();
  },
};
</script>

<style scoped>
  form {
    width: 100%;
    max-width: 600px;
    padding: 12px;
    background-color: #fff;
    display: flex;
    flex-direction: column;
  }
  .styled-textarea {
    font-family: "Inter";
    line-height: 1.5;
    width: 100%;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 5px;
    font-size: 16px;
    background-color: #fff;
    color: #333;
    outline: none;
    transition: border-color 0.3s;
    resize: vertical;
  }

  .styled-textarea:focus {
    border-color: #589F2D;
    box-shadow: 0 0 5px rgba(252, 103, 34, 0.3);
  }

  .styled-textarea::placeholder {
    text-align: right;
  }

  button[type='submit'] {
    background-color: #FFD700;
    color: #000;
    padding: 15px 33px;
    border: none;
    border-radius: 100px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
  }

  .predefined-links {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    gap: 8px;
    margin: 8px 4px 20px 4px;
  }

  .bubble-link {
    display: inline-block;
    padding: 2px 6px;
    border: 1px solid #ccc;
    border-radius: 5px;
    background-color: #f0f0f0;
    color: #333;
    text-decoration: none;
    font-size: 12px;
    font-weight: 400;
    transition: background-color 0.3s, transform 0.2s;
  }
</style>
  