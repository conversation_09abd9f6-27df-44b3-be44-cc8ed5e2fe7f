<template>
  <div v-if="dailyLog">
    <form @submit.prevent="submitEdit">
      <input
        type="text"
        v-model="dailyLog.description"
        placeholder="Edit last activity"
        autofocus
      />
      <button type="submit">Save</button>
    </form>
    <p v-if="error" class="error">{{ error }}</p>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  props: ['lastLogId'],
  data() {
    return {
      dailyLog: null, // Stores the last DailyLog
      error: null, // Error message for the user
    };
  },
  mounted() {
    this.fetchLastLog(); // Fetch the last log on mount
  },
  methods: {
    async fetchLastLog() {
      try {
        const response = await axios.get(`/daily_logs/${this.lastLogId}`);
        this.dailyLog = response.data;
      } catch (err) {
        this.error = 'Failed to fetch the last DailyLog.';
        console.error(err);
      }
    },
    async submitEdit() {
      try {
        const response = await axios.put(`/daily_logs/${this.dailyLog.id}`, {
          daily_log: { description: this.dailyLog.description },
        });

        // Update the current log and create a new one
        this.dailyLog = response.data.new_log;
        this.$emit('log-updated', response.data.updated_log); // Notify the parent
      } catch (err) {
        this.error = 'Failed to update the DailyLog.';
        console.error(err);
      }
    },
  },
};
</script>

<style scoped>
.error {
  color: red;
}
</style>