<template>
  <div class="modal-overlay" data-vue-component="add-event-form">
    <div class="modal-container">
      <div class="modal-header">
        <h3>{{ $t('add_event_for_date', 'Přidat událost {date}', { date: formattedDate1 }) }}</h3>
        <button @click="closeForm" class="modal-close" 
                data-action="close-form" data-testid="close-event-form">×</button>
      </div>
      
      <div class="modal-content">
        <p class="text-muted"></p>
        <form @submit.prevent="submitForm" data-form="event-form">
          <div class="event-types">
            <label v-for="type in eventTypes" 
                   :key="type.value" 
                   :class="['event-type-option', { 'active': eventType === type.value }]">
              <input type="radio" 
                     v-model="eventType" 
                     :value="type.value" 
                     :data-event-type="type.value"
                     :data-testid="`event-type-${type.value}`"
                     required>
              {{ type.text }}
            </label>
          </div>

          <div class="modal-actions">
            <button type="button" @click="closeForm" class="btn btn-outline"
                    data-action="cancel" data-testid="cancel-event-form">
              {{ $t('cancel', 'Zrušit') }}
            </button>
            <button type="submit" class="btn btn-primary"
                    data-action="submit-event" data-testid="submit-event-form">
              {{ $t('add', 'Přidat') }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    date: {
      type: Date,
      required: true
    },
    position: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      eventType: 'travel',
      startTime: '',
      endTime: '',
      eventTypes: [
        { value: 'travel', text: this.$t('event_types.travel', 'Cesta') },
        { value: 'vacation', text: this.$t('event_types.vacation', 'Dovolená') },
        { value: 'illness', text: this.$t('event_types.illness', 'Pracovní neschopnost') },
        { value: 'family_sick', text: this.$t('event_types.family_sick', 'Ošetřování člena rodiny ') },
        { value: 'day_care', text: this.$t('event_types.day_care', 'Návštěva lékaře') },
        { value: 'other', text: this.$t('event_types.other', 'Jiná absence') }
      ]
    };
  },
  computed: {
    formattedDate1() {
      return this.date.toLocaleDateString('cs-CZ', {
        day: '2-digit',
        month: 'long',
        year: 'numeric'
      });
    },
    formattedDate2() {
      return this.date.toLocaleDateString('cs-CZ', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        });
    },
  },
  methods: {
    submitForm() {
      const event = {
        event_type: this.eventType,
        start_time: this.formattedDate2,
        end_time: this.formattedDate2
      };
      this.$emit('add-event', { event });
      this.closeForm();
    },
    closeForm() {
      this.$emit('close-form');
    },
    handleKeydown(event) {
      if (event.key === 'Escape') {
        this.closeForm();
      }
    }
  },
  mounted() {
    document.addEventListener('keydown', this.handleKeydown);
  },
  beforeDestroy() {
    document.removeEventListener('keydown', this.handleKeydown);
  }
};
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  z-index: 9000;
}

.modal-container {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 500px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modal-header {
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #dee2e6;
}

.modal-header h3 {
  font-size: 1.25rem;
  font-weight: 500;
  margin: 0;
}

.modal-close {
  font-size: 1.5rem;
  color: #666;
  cursor: pointer;
  padding: 0.5rem;
  line-height: 1;
}

.modal-content {
  padding: 1.5rem;
}

.event-types {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 2rem;
}

.event-type-option {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.event-type-option:hover {
  border-color: #22C55E;
}

.event-type-option.active {
  background: #22C55E;
  border-color: #22C55E;
  color: white;
}

.event-type-option input {
  display: none;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-top: 1.5rem;
}

@media (max-width: 768px) {
  .modal-container {
    margin: 1rem;
  }
  
  .modal-actions {
    flex-direction: column;
  }
  
  .modal-actions button {
    width: 100%;
  }
}
</style>