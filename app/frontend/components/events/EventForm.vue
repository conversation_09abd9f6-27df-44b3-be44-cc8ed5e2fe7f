<template>
  <div class="event-form">
    <form @submit.prevent="submitForm">

      <div class="event-types">
        <label v-for="type in eventTypes"
              :key="type.value"
              :class="['event-type-option', { active: event.event_type === type.value }]">
          <input type="radio"
                v-model="event.event_type"
                :value="type.value"
                required>
          {{ type.text }}
        </label>
      </div>

      <div v-if="event.event_type === 'travel'">
        <div class="form-group">
          <VueDatePicker 
            v-model="event.dateRange"
            :format="formatDateRange"
            :range="{ 
              partialRange: true, 
            }"
            :enable-time-picker="false"
            :placeholder="$t('events.travel_duration', 'Trvání cesty')"
            week-start="1"
            :locale= "$i18n.locale"
            :cancelText="$t('cancel', 'Zrušit')" 
            :selectText="$t('select', 'Vybrat')"
            @update:modelValue="logDate"
          />
        </div>
        <div class="form-group">
          <button type="submit" class="btn btn-primary">{{ $t('submit', 'Odeslat') }}</button>
        </div>
      </div>

      <div v-if="event.event_type && event.event_type !== 'travel'">
        <div class="form-group">
          <VueDatePicker 
            v-model="event.dateRange"
            :format="formatDateRange"
            :range="{ 
                partialRange: true, 
              }"
            :enable-time-picker="false"
            :placeholder="$t('events.select_days', 'Vyberte den/dny')"
            week-start="1"
            :locale= "$i18n.locale"
            :cancelText="$t('cancel', 'Zrušit')" 
            :selectText="$t('select', 'Vybrat')"
            @update:modelValue="logDate"
          />
        </div>
        <div class="form-group action-buttons mt-6">
          <button type="submit" class="btn btn-primary">{{ $t('submit', 'Odeslat') }}</button>
        </div>
      </div>
  

    </form>
  </div>
</template>

<script>
import axios from 'axios';
import VueDatePicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css'
import { sendFlashMessage } from '../../utils/flashMessage';

export default {
  components: {
    VueDatePicker
  },
  props: {
    initialDate: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      date: [],
      event: {
        dateRange: [],
        event_type: '',
        start_time: '',
        end_time: '',
        place: '',
        description: ''
      },
      eventTypes: [
        { value: 'travel', text: this.$t('event_type.travel_all_day', 'Celodenní pracovní cesta') },
        { value: 'vacation', text: this.$t('event_type.vacation', 'Dovolená') },
        { value: 'illness', text: this.$t('event_type.illness', 'Pracovní neschopnost') },
        { value: 'family_sick', text: this.$t('event_type.family_sick', 'Ošetřování člena rodiny ') },
        { value: 'day_care', text: this.$t('event_type.day_care', 'Návštěva lékaře') },
        { value: 'other', text: this.$t('event_type.other', 'Jiná absence') }
      ]
    };
  },
  created() {
    if (this.initialDate) {
      const date = new Date(this.initialDate);
      this.event.dateRange = [date, null];
    }
  },
  methods: {
    formatDateRange(date) {
      const [date0, date1] = date;
      if (!date0) return '';
      const day0 = date0.getDate();
      const month0 = date0.getMonth() + 1;
      if (!date1) return `${day0}.${month0}.`;
      const day1 = date1.getDate();
      const month1 = date1.getMonth() + 1;
      return `${day0}.${month0}.-${day1}.${month1}.`;
    },
    customFormat1(date) {
      return new Date(date).toLocaleDateString('cs-CZ', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        });
    },
    submitForm() {
      const [startDate, endDate] = this.event.dateRange;
      const formData = {
        event_type: this.event.event_type,
        place: this.event.place,
        description: this.event.description,
        start_time: startDate ? this.customFormat1(startDate) : null,
        end_time: endDate ? this.customFormat1(endDate) : this.customFormat1(startDate)
      };
      
      // Always use API endpoint in SPA mode
      axios
        .post('/api/v1/events', { event: formData }, {
          headers: { 'Accept': 'application/json' }
        })
        .then(response => {
          // Handle API response format
          const eventData = response.data.event || response.data;
          
          console.log('📝 New event created:', eventData);
          
          // Update store with new event
          this.$store.commit('calendarStore/ADD_EVENT', eventData);
          
          this.$emit('event-added', eventData); 
          
          // Show success message
          if (response.data.message) {
            sendFlashMessage(response.data.message, 'success');
          }
          
          this.resetForm(); 
        })
        .catch(error => {
          console.error('Error creating event:', error);
          const errorMessage = error.response?.data?.message || this.$t('events.create_error', 'Nepodařilo se vytvořit událost');
          sendFlashMessage(errorMessage, 'error');
        });
    },
    resetForm() {
      this.event = {
        dateRange: [],
        event_type: '',
        start_time: '',
        end_time: '',
        place: '',
        description: ''
      };
    },
    logDate(date) {
    },
  }
};
</script>

<style scoped>
.event-types {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 2rem;
  font-size: 0.875rem;
}

.event-type-option {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.event-type-option:hover {
  border-color: #22C55E;
}

.event-type-option.active {
  background: #22C55E;
  border-color: #22C55E;
  color: white;
}

.event-type-option input {
  display: none;
}

form {
  width: 100%;
  max-width: 600px;
  padding: 12px;
  display: flex;
  flex-direction: column;
  align-self: center ;
}

input[type='text'] {
  width: 100%;
  padding: 15px; /* Increased padding to match the button height */
  border: 1px solid #ccc;
  border-radius: 5px;
  font-size: 16px;
  background-color: #fff;
  color: #333;
  outline: none;
  transition: border-color 0.3s;
  margin-top: 15px;
}

input[type='text']:focus {
  border-color: #007bff;
  box-shadow: 0 0 5px rgba(252, 103, 34, 0.3);
}

input::placeholder {
  text-align: left;
}

textarea {
  flex-grow: 1;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 8px;
  resize: none;
  font-family: "Inter";
  font-size: 16px;
  line-height: 1.5;
  width: 100%;
  border: 1px solid #ccc;
  border-radius: 5px;
  background-color: #fff;
  color: #333;
  outline: none;
  transition: border-color 0.3s;
}
.form-group {
  margin-bottom: 0.5rem;
}
.form-control {
  display: block;
  width: 100%;
  padding: 0.375rem 0.75rem;
  margin: 0.375rem ;
  font-size: 1rem;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
  border-color: #80bdff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
</style>
