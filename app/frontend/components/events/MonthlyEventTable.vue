<template>
  <div class="calendar-wrapper">
    <div v-if="showDeleteConfirm" class="delete-modal">
      <div class="delete-modal-content">
        <h3 class="delete-modal-title">{{ $t('delete_record', 'Smazat záznam') }}</h3>
        <p>{{ $t('confirm_delete_record', 'Opravdu chcete smazat tento záznam?') }}</p>
        <div class="delete-modal-actions">
          <button @click="showDeleteConfirm = false" class="btn btn-outline">
            {{ $t('cancel', 'Zrušit') }}
          </button>
          <button @click="deleteEvent" class="btn btn-danger">
            {{ $t('delete', 'Smazat') }}
          </button>
        </div>
      </div>
    </div>
    <div class="calendar">
      <div class="header">
        <div class="nav-control">
          <button @click="prevMonth" class="text-link">
            <ChevronLeft :size="20" />
          </button>
          <h2>{{ capitalize(monthYear) }}</h2>
          <button @click="nextMonth" class="text-link">
            <ChevronRight :size="20" />
          </button>
        </div>
      </div>
      <div class="header">
        <div class="filter-controls mt-4">
          <button @click="toggleFilter" :class="['text-link-action', 'blue']">
            {{ showOnlyEvents ? $t('show_all', 'Zobrazit vše') : $t('events.show_only_current', 'Jenom aktuální') }}
          </button>
          <button v-if="hasOlderDays" @click="toggleShowAllDays" :class="['text-link-action', 'blue']">
            {{ showAllDays ? $t('events.hide_older_days', 'Skrýt starší dny') : $t('events.show_older_days', 'Zobrazit starší dny') }}
          </button>
          <LocalizedLink :to="'/holidays'" class="text-link-action blue" :use-anchor="true">
            {{ $t('holidays', 'Svátky') }}
          </LocalizedLink>
        </div>
      </div>

      <div class="day-list">
        <div v-for="day in filteredDays"
            :key="day.date"
            :class="['day-row', {
              'weekend': isWeekend(day.date),
              'holiday': isHoliday(day.date),
              'today': isToday(day.date),
            }]">
          <div class="day-header" :class="{
                    'weekend-header': isWeekend(day.date),
                    'holiday-header': isHoliday(day.date)
                    }">
            <div class="day-number">
              <div :class="['day-date', { 'today': isToday(day.date) }]">
                {{ formatDay(day.date) }}
              </div>
              <div class="day-weekday">
                {{ formatWeekday(day.date) }}
              </div>
            </div>
          </div>

          <div class="day-content">
            <!-- Events section -->
            <div v-if="hasEvent(day.date)" class="">
              <event-show
                v-for="event in getEvents(day.date)"
                :key="'event-' + event.id"
                :event="event"
                @delete-requested="openDeleteConfirm"
                @event-approved="handleEventApproved"
              />
            </div>

            <!-- Works section -->
            <CalendarWorkItem
              v-for="work in getWorks(day.date)"
              :key="'work-' + work.id"
              :work="work"
              @click="openWorkModal(work)"
            />

            <!-- Meetings section -->
            <meeting-card
              v-for="meeting in getMeetings(day.date)"
              :key="'meeting-' + meeting.id"
              :meeting="meeting"
              @updated="$emit('meeting-updated', $event)"
              @deleted="$emit('meeting-deleted', $event)"
            />

            <div class="action-buttons">
              <button @click="emitShowAddEventForm(day.date, $event)" class="add-btn" :title="$t('add_event', 'Přidat událost')">
                <CalendarPlus :size="14" />
              </button>
              <button @click="emitShowAddWorkForm(day.date, $event)" class="add-btn" :title="$t('add_work', 'Přidat práci')">
                <FolderPlus :size="14" />
              </button>
              <button
                v-if="isMobileView"
                @click="scheduleWorkToDay(day.date, $event)"
                class="add-btn schedule-btn"
                :class="{ 'disabled': !selectedWorkForScheduling || isScheduling }"
                :disabled="!selectedWorkForScheduling || isScheduling"
                :title="getScheduleButtonTitle(day.date)"
                :aria-label="getScheduleButtonAriaLabel(day.date)"
                data-action="schedule-work-here"
              >
                <div v-if="isScheduling" class="spinner"></div>
                <Plus v-else :size="14" />
              </button>
            </div>

          </div>
        </div>
      </div>
    </div>

    <!-- Mobile Unscheduled Panel - only visible on mobile -->
    <MobileUnscheduledPanel
      v-if="isMobileView"
      @work-selected="handleWorkSelected"
      @work-deselected="handleWorkDeselected"
      @work-scheduled="handleWorkScheduled"
      @scheduling-error="handleSchedulingError"
      @no-free-slots="handleNoFreeSlots"
      data-vue-component="mobile-unscheduled-panel"
    />

    <!-- Work Detail Modal -->
    <WorkDetailModal
      v-if="selectedModalWork"
      v-model="showWorkModal"
      :work="selectedModalWork"
    />

  </div>
</template>

<script>
import { HelpCircle, CheckCheck, ChevronLeft, ChevronRight, Calendar, Plus, FolderPlus, CalendarPlus } from 'lucide-vue-next';
import { mapState, mapGetters } from 'vuex';
import WorkShow from '../works/WorkShow.vue';
import EventShow from '../events/EventShow.vue';
import MeetingCard from '../meetings/MeetingCard.vue';
import LocalizedLink from '../LocalizedLink.vue';
import MobileUnscheduledPanel from '../calendar/MobileUnscheduledPanel.vue';
import CalendarWorkItem from '../calendar/CalendarWorkItem.vue';
import WorkDetailModal from '../calendar/WorkDetailModal.vue';

export default {
  components: {
    HelpCircle,
    CheckCheck,
    ChevronLeft,
    ChevronRight,
    Calendar,
    FolderPlus,
    CalendarPlus,
    Plus,
    WorkShow,
    EventShow,
    MeetingCard,
    LocalizedLink,
    MobileUnscheduledPanel,
    CalendarWorkItem,
    WorkDetailModal
  },
  props: {
    meetings: {
      type: Array,
      required: true
    },
    holidays: {
      type: Array,
      required: true
    },
    currentDate: {
      type: Date,
      default: () => new Date()
    }
  },
  data() {
    return {
      showOnlyEvents: false,
      showConfirmDialog: false,
      pendingDeleteId: null,
      showDeleteConfirm: false,
      deleteEventId: null,
      showAllDays: false,
      today: new Date(),
      selectedWorkForScheduling: null, // Work selected from mobile panel
      isMobileView: false, // Reactive mobile detection
      showWorkModal: false, // Modal state for work details
      selectedModalWork: null, // Work selected for modal display
      isScheduling: false, // Loading state for scheduling operation
    };
  },
  created() {
    // Load saved states from localStorage
    const savedShowOnlyEvents = localStorage.getItem('showOnlyEvents');
    const savedShowAllDays = localStorage.getItem('showAllDays');

    if (savedShowOnlyEvents !== null) {
      this.showOnlyEvents = JSON.parse(savedShowOnlyEvents);
    }
    if (savedShowAllDays !== null) {
      this.showAllDays = JSON.parse(savedShowAllDays);
    }

    // Set up mobile detection
    this.checkMobileView();
    window.addEventListener('resize', this.checkMobileView);
  },

  beforeUnmount() {
    window.removeEventListener('resize', this.checkMobileView);
  },
  computed: {
    // Map Vuex state and getters
    ...mapState('calendarStore', {
      isLoadingEvents: state => state.loadingStates.events,
      isLoadingWorks: state => state.loadingStates.works,
      currentUserContractId: state => state.currentUserContractId
    }),
    ...mapGetters('calendarStore', ['itemsByDate']),

    isDataLoading() {
      return this.isLoadingEvents || this.isLoadingWorks;
    },

    monthYear() {
      return this.currentDate.toLocaleDateString(this.$i18n.locale, { month: 'long', year: 'numeric' });
    },
    daysInMonth() {
      const year = this.currentDate.getFullYear();
      const month = this.currentDate.getMonth();
      const days = new Date(year, month + 1, 0).getDate();

      const currentMonthDays = Array.from({ length: days }, (_, i) => {
        const date = new Date(year, month, i + 1);
        return { date };
      });

      // Switched off for now
      const nextMonthDays = Array.from({ length: 0 }, (_, i) => {
        const date = new Date(year, month + 1, i + 1);
        return { date };
      });

      return [...currentMonthDays, ...nextMonthDays];

    },
    filteredDays() {
      let days = this.daysInMonth;

      if (this.showOnlyEvents) {
        return days.filter(day => this.hasEvent(day.date) || this.hasWork(day.date) || this.hasMeeting(day.date));
      }

      if (!this.showAllDays) {
        const threeDaysAgo = new Date();
        threeDaysAgo.setDate(threeDaysAgo.getDate() - 1);

        days = days.filter(day => day.date >= threeDaysAgo);
      }

      return days;
    },
    hasOlderDays() {
      const threeDaysAgo = new Date();
      threeDaysAgo.setDate(threeDaysAgo.getDate() - 1);

      return this.daysInMonth.some(day => day.date < threeDaysAgo);
    }
  },
  methods: {
    capitalize(str) {
      return str[0].toLocaleUpperCase() + str.slice(1)
    },

    getWorkStatusClass(status) {
      switch(status) {
        case 'scheduled': return 'work-scheduled';
        case 'in_progress': return 'work-progress';
        case 'completed': return 'work-completed';
        case 'cancelled': return 'work-cancelled';
        default: return '';
      }
    },

    openDeleteConfirm(eventId) {
      this.deleteEventId = eventId;
      this.showDeleteConfirm = true;
    },

    async deleteEvent() {
      const eventId = this.deleteEventId;

      try {
        // Handle deletion directly using store instead of emitting to parent
        await this.$store.dispatch('calendarStore/deleteEvent', eventId);

        // Close modal
        this.showDeleteConfirm = false;
        this.deleteEventId = null;
      } catch (error) {
        console.error('Error deleting event:', error);
        // Close modal even on error
        this.showDeleteConfirm = false;
        this.deleteEventId = null;
      }
    },

    toggleFilter() {
      this.showOnlyEvents = !this.showOnlyEvents;
      localStorage.setItem('showOnlyEvents', JSON.stringify(this.showOnlyEvents));
    },

    toggleShowAllDays() {
      this.showAllDays = !this.showAllDays;
      localStorage.setItem('showAllDays', JSON.stringify(this.showAllDays));
    },

    isWeekend(date) {
      const day = date.getDay();
      return day === 0 || day === 6;
    },

    isHoliday(date) {
      const dateString = date.toLocaleDateString('en-CA');
      return this.holidays.includes(dateString);
    },

    hasEvent(date) {
      const events = this.getEvents(date);
      return events.length > 0;
    },

    hasWork(date) {
      const works = this.getWorks(date);
      return works.length > 0;
    },

    hasMeeting(date) {
      const dateString = date.toLocaleDateString('en-CA');
      return this.meetings.some(meeting => {
        const meetingDate = new Date(meeting.scheduled_date || meeting.confirmed_date).toLocaleDateString('en-CA');
        return dateString === meetingDate;
      });
    },

    getEvents(date) {
      // Use the store's itemsByDate getter which already has visibility logic
      const items = this.itemsByDate(date);
      return items.events || [];
    },

    // Convenience wrapper for getting events for a specific day
    getEventsForDay(date) {
      return this.getEvents(date);
    },

    getWorks(date) {
      // Use the store's itemsByDate getter which already has visibility logic
      const items = this.itemsByDate(date);
      return items.works || [];
    },

    // Convenience wrapper for getting works for a specific day
    getWorksForDay(date) {
      return this.getWorks(date);
    },

    getMeetings(date) {
      const dateString = date.toLocaleDateString('en-CA');
      return this.meetings.filter(meeting => {
        const meetingDate = new Date(meeting.scheduled_date || meeting.confirmed_date).toLocaleDateString('en-CA');
        return dateString === meetingDate;
      });
    },

    prevMonth() {
      const newDate = new Date(this.currentDate);
      newDate.setMonth(newDate.getMonth() - 1);
      this.$emit('month-changed', newDate);
    },

    nextMonth() {
      const newDate = new Date(this.currentDate);
      newDate.setMonth(newDate.getMonth() + 1);
      this.$emit('month-changed', newDate);
    },

    formatDay(date) {
      return date.toLocaleDateString('cs-CZ', {
        day: '2-digit',
      }).replace('.', '');
    },

    formatWeekday(date) {
      return date.toLocaleDateString('cs-CZ', {
        weekday: 'short',
      });
    },

    formatTime(startTime) {
      const date = new Date(startTime);
      const hours = date.getHours();
      const minutes = date.getMinutes();
      if (hours === 0 && minutes === 0) {
        return '';
      }
      return new Date(startTime).toLocaleTimeString('cs-CZ', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });
    },
    emitShowAddEventForm(date, event) {
      this.$emit('show-add-event-form', date, event);
    },

    emitShowAddWorkForm(date, event) {
      this.$emit('show-add-work-form', date, event);
    },

    handleEventApproved(eventId) {
      // Don't emit to parent - store already handles it
      // this.$emit('event-approved', eventId);
    },
    isToday(date) {
      const today = new Date();
      return date.getDate() === today.getDate() &&
            date.getMonth() === today.getMonth() &&
            date.getFullYear() === today.getFullYear();
    },

    // === Mobile Scheduling Methods ===

    checkMobileView() {
      this.isMobileView = window.innerWidth <= 768;
    },

    formatDateKey(date) {
      return date.toLocaleDateString('en-CA'); // YYYY-MM-DD format
    },

    handleWorkSelected(work) {
      this.selectedWorkForScheduling = work;
    },

    handleWorkDeselected() {
      this.selectedWorkForScheduling = null;
    },

    async scheduleWorkToDay(date, event) {
      if (!this.selectedWorkForScheduling || this.isScheduling) return;

      // Prevent event bubbling
      if (event) {
        event.stopPropagation();
      }

      const dateString = this.formatDateKey(date);
      this.isScheduling = true;

      try {
        // Schedule the work using Vuex store
        await this.$store.dispatch('calendarStore/updateWorkDate', {
          workId: this.selectedWorkForScheduling.id,
          newDate: dateString
        });

        // Clear selection
        this.handleWorkDeselected();

        // Emit event to parent for any additional handling
        this.$emit('work-scheduled', {
          work: this.selectedWorkForScheduling,
          date: date
        });

        // Show success message
        this.$emit('show-flash-message', {
          type: 'success',
          message: this.$t('scheduling.work_scheduled_successfully', 'Práce byla úspěšně naplánována')
        });

      } catch (error) {
        console.error('Failed to schedule work:', error);
        this.$emit('show-flash-message', {
          type: 'error',
          message: this.$t('scheduling.scheduling_failed', 'Naplánování práce se nezdařilo')
        });
      } finally {
        this.isScheduling = false;
      }
    },

    getScheduleButtonTitle(date) {
      if (this.isScheduling) {
        return this.$t('scheduling.scheduling', 'Plánování...');
      }
      if (!this.selectedWorkForScheduling) {
        return this.$t('select_work_first', 'Nejdřív vyberte práci');
      }
      return this.$t('scheduling.schedule_work_here', 'Naplánovat práci sem');
    },

    getScheduleButtonAriaLabel(date) {
      if (this.isScheduling) {
        return this.$t('scheduling.scheduling', 'Plánování...');
      }
      if (!this.selectedWorkForScheduling) {
        return this.$t('select_work_first', 'Nejdřív vyberte práci');
      }
      const dateString = date.toLocaleDateString(this.$i18n.locale);
      return this.$t('scheduling.schedule_work_to_date', 'Naplánovat {work} na {date}', {
        work: this.selectedWorkForScheduling.title,
        date: dateString
      });
    },

    handleWorkScheduled(data) {
      // Handle successful scheduling from MobileUnscheduledPanel auto-schedule
      this.$emit('work-scheduled', data);
      this.$emit('show-flash-message', {
        type: 'success',
        message: this.$t('scheduling.work_auto_scheduled', 'Práce byla automaticky naplánována')
      });
    },

    handleSchedulingError(error) {
      console.error('Scheduling error:', error);
      this.$emit('show-flash-message', {
        type: 'error',
        message: this.$t('scheduling.scheduling_error', 'Chyba při plánování práce')
      });
    },

    handleNoFreeSlots(work) {
      this.$emit('show-flash-message', {
        type: 'warning',
        message: this.$t('scheduling.no_free_slots', 'Nebyly nalezeny žádné volné termíny pro tuto práci')
      });
    },

    // Modal handlers
    openWorkModal(work) {
      this.selectedModalWork = work;
      this.showWorkModal = true;
    },

    closeWorkModal() {
      this.showWorkModal = false;
      this.selectedModalWork = null;
    },
  },
// provide: {
  //   store
  // },
};
</script>

<style scoped>
.header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0rem;
  &.stick {
    top: 0px;
    position: sticky;
    z-index: 10;
  }
}

.calendar-wrapper {
  position: relative;
}

.calendar {
  margin: 0 auto;
}

.nav-control {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.today {
  color: #3b82f6;
  background-color: #dadada;
}

.filter-controls {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.day-list {
  margin-top: 1rem;
}

.day-row {
  display: flex;
  flex-direction: row;
  gap: 10px;
  padding: 0.25rem;
  border-top: 1px solid #ababab;
  overflow: visible; /* Allow dropdowns to show outside */
}
.weekend {
  background: none;
}

.weekend-header {
  color: rgb(197, 34, 34);
}

.holiday-header {
  color: rgb(197, 34, 34);
}

.day-header {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.day-date {
  font-size: 1.5rem;
  font-weight: 600;
}
.day-weekday {
  font-size: 0.875rem;
  font-weight: 500;
}

.day-content {
  display: flex;
  width: 100%;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 0.5rem;
  overflow: visible; /* Allow dropdowns to show outside */
}

.card {
  width: 90%;
  min-width:350px;
}

.delete-btn {
  padding: 0;
  background: none;
  border: none;
  color: white;
  font-size: 1.25rem;
  line-height: 1;
  cursor: pointer;
}

.delete-btn:hover {
  color: #dc3545;
}

.action-buttons {
  display: flex;
  flex-direction: row;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.add-btn {
  align-self: flex-start;
  padding: 0.25rem 0.75rem;
  background: none;
  border: 1px dashed #dee2e6;
  color: #494949;
  border-radius: 9999px;
  cursor: pointer;
  font-size: 1.25rem;
  line-height: 1;
}

.add-btn:hover:not(:disabled) {
  border-color: #22C55E;
  color: #22C55E;
}

.schedule-btn {
  border-color: #2196f3;
  background-color: #2196f3;
  color: white;
}

.schedule-btn.disabled,
.schedule-btn:disabled {
  display: none;
}

.spinner {
  width: 14px;
  height: 14px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #2196f3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.add-work-btn {
  align-self: flex-start;
  padding: 0.25rem 0.75rem;
  background: none;
  border: 1px dashed #dee2e6;
  color: #494949;
  border-radius: 9999px;
  cursor: pointer;
  line-height: 1;
  display: flex;
  align-items: center;
}

.add-work-btn:hover {
  border-color: #4a6fa5;
  color: #4a6fa5;
}

.event-icon {
  line-height: 1;
}

.icon {
  width: 1rem;
  height: 1rem;
}

.delete-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1100; /* Higher than dropdown items */
}

.delete-modal-content {
  background: white;
  padding: 20px;
  border-radius: 8px;
  max-width: 400px;
  width: 90%;
}

.delete-modal-title {
  margin: 0 0 16px 0;
  font-size: 1.2rem;
}

.delete-modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 20px;
}

/* Mobile scheduling styles */
.mobile-schedule-target {
  margin-top: 0.5rem;
  padding: 0.75rem;
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border: 2px dashed #2196f3;
  border-radius: 8px;
  cursor: pointer;
  user-select: none;
  transition: all 0.2s ease;
  text-align: center;
  touch-action: manipulation;
}

.mobile-schedule-target:hover {
  background: linear-gradient(135deg, #bbdefb 0%, #90caf9 100%);
  border-color: #1976d2;
  transform: scale(1.02);
}

.mobile-schedule-target:active {
  transform: scale(0.98);
}

.schedule-target-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.schedule-icon {
  font-size: 1.2rem;
}

.schedule-text {
  font-weight: 600;
  color: #1976d2;
  font-size: 0.875rem;
}

/* Scheduling mode visual feedback */
.day-row.scheduling-mode {
  background: rgba(33, 150, 243, 0.05);
  border-left: 3px solid #2196f3;
  padding-left: calc(0.25rem - 3px);
}

.day-row.scheduling-mode:hover {
  background: rgba(33, 150, 243, 0.1);
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .day-date {
    font-size: 1.2rem;
  }
  .day-row {
    padding: 0.25rem;
    gap: 8px;
  }

  .event-group {
    gap: 0.25rem;
  }

  .event-tag {
    display: inline-flex;
    width: auto;
  }

  /* Enhanced mobile scheduling targets */
  .mobile-schedule-target {
    padding: 1rem;
    margin-top: 0.75rem;
  }

  .schedule-text {
    font-size: 1rem;
  }

  .schedule-icon {
    font-size: 1.5rem;
  }

  /* Ensure mobile panel doesn't interfere with calendar */
  .calendar-wrapper {
    padding-bottom: 200px; /* Space for mobile panel */
  }

  /* Scheduling mode feedback on mobile */
  .day-row.scheduling-mode {
    background: rgba(33, 150, 243, 0.08);
    border-left: 4px solid #2196f3;
  }
}

</style>