<template>
  <div class="feedback-footer">
    <button class="feedback-btn" @click="openFeedback">
      🖐 {{ $t('feedback.button', 'Pomozte nám zlepšit Týmbox.') }}
    </button>
  </div>
</template>

<script>
export default {
  name: 'FeedbackButton',
  props: {
    pageUrl: { type: String, default: '' }
  },
  methods: {
    openFeedback() {
      const url = this.pageUrl || window.location.pathname
      const event = new CustomEvent('open-central-modal', {
        detail: {
          componentName: 'FeedbackForm',
          title: this.$t('feedback.title', 'Kontakt přímo k nám'),
          props: { pageUrl: url }
        }
      })
      document.dispatchEvent(event)
    }
  }
}
</script>

<style scoped>
.feedback-footer { margin-top: 24px; display: flex; justify-content: center; }
.feedback-btn {
  font-size: 0.9rem; color: #2563eb; background: transparent; border: none; cursor: pointer;
}
.feedback-btn:hover { text-decoration: underline; }
</style>

