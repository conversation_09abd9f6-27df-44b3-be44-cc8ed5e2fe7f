<template>
  <div class="feedback-form">
    <div v-if="submitted" class="thanks" :class="{ visible: submitted }">
      {{ $t('feedback.thanks', '<PERSON>ě<PERSON>jeme za zpětnou vazbu!') }}
    </div>

    <div v-else>
      <div class="form-group">
        <label>{{ $t('feedback.category', 'Kategorie') }}</label>
        <select v-model="form.category">
          <option :value="'bug_report'">{{ $t('feedback.categories.bug_report', 'Nahlásit chybu') }}</option>
          <option :value="'something_confusing'">{{ $t('feedback.categories.something_confusing', 'Něco je nejasné') }}</option>
          <option :value="'missing_feature'">{{ $t('feedback.categories.missing_feature', 'Chybějící funkce') }}</option>
          <option :value="'general_message'">{{ $t('feedback.categories.general_message', 'Obecná zpráva') }}</option>
        </select>
      </div>

      <div class="form-group">
        <label>
          {{ $t('feedback.message', '<PERSON><PERSON><PERSON><PERSON>, prosím, svou zpětnou vazbu') }}
          <span class="char-counter">{{ remaining }}/300</span>
        </label>
        <textarea v-model="form.message" :maxlength="300" rows="5"></textarea>
      </div>


      <div class="actions">
        <button class="btn btn-primary" :disabled="loading" @click="submit">
          <span v-if="loading">{{ $t('front.saving', 'Ukládání...') }}</span>
          <span v-else>{{ $t('front.submit', 'Odeslat') }}</span>
        </button>
      </div>

      <div v-if="error" class="error">{{ error }}</div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import { mapGetters } from 'vuex'

export default {
  name: 'FeedbackForm',
  props: { pageUrl: { type: String, default: '' } },
  data() {
    return {
      loading: false,
      submitted: false,
      error: null,
      form: {
        page_url: '',
        category: 'bug_report',
        message: ''
      }
    }
  },
  computed: {
    ...mapGetters('userStore', ['isAuthenticated']),
    remaining() { return 300 - (this.form.message?.length || 0) }
  },
  mounted() {
    this.form.page_url = this.pageUrl || window.location.pathname
  },
  methods: {
    async submit() {
      this.loading = true
      this.error = null
      try {
        // Require authentication
        if (!this.isAuthenticated) {
          this.error = this.$t('flash.registration_needed', 'Pro pokračování je potřeba se registrovat.')
          this.loading = false
          return
        }
        // Basic validation
        if (!this.form.message || this.form.message.length === 0) {
          this.error = this.$t('feedback.errors.message_required', 'Zpráva je povinná')
          this.loading = false
          return
        }

        await axios.post('/api/v1/feedbacks', { feedback: this.form })
        this.submitted = true
        setTimeout(() => {
          this.$emit('close-modal')
        }, 1800)
      } catch (e) {
        const apiErrors = e.response?.data?.errors
        if (Array.isArray(apiErrors)) {
          this.error = apiErrors.join(', ')
        } else if (typeof apiErrors === 'string') {
          this.error = apiErrors
        } else {
          this.error = this.$t('front.failed', 'Operace selhala.')
        }
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.feedback-form { min-width: 100%; }
.form-group { margin-bottom: 12px; display: flex; flex-direction: column; }
.form-group label { font-weight: 600; margin-bottom: 6px; display:flex; justify-content: space-between; }
.form-group textarea, .form-group input, .form-group select { border: 1px solid #e5e7eb; border-radius: 6px; padding: 8px; }
.char-counter { font-weight: normal; color: #6b7280; font-size: 0.85rem; }
.actions { margin-top: 16px; display: flex; justify-content: flex-end; }
.error { margin-top: 10px; color: #b91c1c; }

.thanks { opacity: 0; transform: translateY(10px); transition: opacity 1.2s, transform 1.2s; text-align:center; font-size: 1.1rem; }
.thanks.visible { opacity: 1; transform: translateY(0); }
</style>

