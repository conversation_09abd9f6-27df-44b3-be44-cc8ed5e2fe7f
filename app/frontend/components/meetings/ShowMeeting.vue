<template>
  <div class="card flex flex-col p-4">
    <!-- Header -->
    <div class="card-header flex justify-between items-start mb-0 p-0 border-b-0">
      <div class="flex items-center gap-2">
        <Handshake :size="16" class="text-gray-500 flex-shrink-0" /> 
        <h3 class="text-base font-semibold m-0">{{ meeting.title }}</h3>
      </div>
      <div class="relative" ref="dropdown"> 
        <button class="p-1 text-gray-500 hover:text-gray-700" @click="toggleDropdown">
          <MoreVertical :size="18" />
        </button>
        <div
          v-if="isDropdownOpen" 
          class="absolute right-0 mt-1 w-32 bg-white rounded-md shadow-lg border border-gray-200 z-10"
        >
          <button class="block w-full text-left px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100"
             @click.prevent="$emit('delete', meeting.id); isDropdownOpen = false;">
            {{ $t('delete', 'Smazat') }}
          </button>
        </div>
      </div>
    </div>
    
    <div class="flex flex-col gap-1.5 text-sm text-gray-700 mt-3 mb-3">
      <!-- Confirmed Date -->
      <!-- Status Badges/Info -->
      <div class="flex flex-wrap gap-2 mb-2">
        <span class="badge" :class="{ 'badge-success': meetingStatus === 'confirmed', 'badge-warning': meetingStatus === 'all-responded', 'badge-gray': meetingStatus === 'pending' }">
          <template v-if="meetingStatus === 'confirmed'">
            <div v-if="meeting.confirmed_date" class="flex items-center gap-1.5">
              <CalendarClock :size="14" class="text-gray-500 flex-shrink-0" /> 
              <span>
                {{ $t('meetings.confirmed_date', 'Potvrzená:') }}
                {{ formatDate(meeting.confirmed_date) }}
              </span>
            </div>
          </template>
          <template v-else-if="meetingStatus === 'all-responded'">
            {{ $t('meetings.waiting_for_date_selection', 'Čeká na výběr termínu') }}
          </template>
          <template v-else-if="meetingStatus === 'pending'">
            {{ $t('meetings.waiting_for_replies', 'Čekání na odpovědi') }}
            ({{ respondedCount }}/{{ totalParticipants }})
          </template>
          <template v-else>
            {{ $t('meetings.unknown_status', 'Neznámý stav') }}
          </template>
        </span>
      </div>
      
      <!-- Description -->
      <p v-if="meeting.description" class="text-sm text-gray-600 mb-3 break-words">
        {{ meeting.description || $t('meetings.no_description', 'Bez popisu') }} 
      </p>

    
       <!-- Participants List -->
       <div v-if="meeting.meeting_users && meeting.meeting_users.length > 0" class="mt-1">
         <span class="font-semibold text-gray-600 block mb-1">Účastníci:</span>
         <ul class="list-none p-0 m-0 flex flex-col gap-1">
           <li v-for="user in meeting.meeting_users" :key="user.id" class="flex items-center justify-between gap-1.5">
             <div class="flex items-center gap-1.5">
               <CircleUserRound :size="14" class="text-gray-500 flex-shrink-0" /> 
               <span>{{ user.email }}</span>
             </div>
             <div class="flex items-center gap-1">
                 <!-- Using existing logic for status display -->
                 <CheckCircle2 v-if="user.status === 'replied' || user.status === 'confirmed'" :size="14" class="text-green-600" />
                 <template v-else>
                    <span class="badge badge-gray text-xs">{{ $t('meetings.waiting_status_badge', 'Čeká se') }}</span>
                    <!-- Styling the existing button logic -->
                    <button @click.prevent="resendInvitation(user.id)" class="text-blue-600 hover:text-blue-800 text-xs underline">
                      {{ $t('meetings.resend_invitation_link', 'Znovu poslat') }}
                    </button>
                 </template>
             </div>
           </li>
         </ul>
       </div>
    </div>

    <!-- All Responded / Best Options Section -->
    <div v-if="meetingStatus === 'all-responded'" class="mt-2">
      <div class="flex items-center gap-2 p-2 rounded border border-yellow-300 bg-yellow-50 text-yellow-800 text-sm mb-3">
         <AlertCircle size="16" class="flex-shrink-0"/>
         <span>{{ $t('meetings.all_responded_no_common_time', 'Všichni odpověděli, ale nebyl nalezen společný termín.') }}</span>
      </div>

      <!-- Fallback Actions (using existing methods) -->
      <div class="flex flex-wrap gap-2 mb-3">
        <button @click="extendDates" class="btn btn-outline btn-small">
          {{ $t('meetings.extend_dates_7_days', 'Prodloužit termíny o 7 dní') }}
        </button>
        <button @click="showBestOptions = !showBestOptions" class="btn btn-outline btn-small">
          {{ showBestOptions ? $t('meetings.hide_best_options', 'Skrýt') : $t('meetings.show_best_options', 'Zobrazit') }} {{ $t('meetings.best_options', 'nejlepší možnosti') }}
        </button>
      </div>

      <!-- Best Options List  -->
      <div v-if="showBestOptions && filteredBestOptions.length > 0" class="flex flex-col gap-3">
         <div v-for="option in filteredBestOptions" :key="option.date" class="border border-gray-200 rounded-md p-3 bg-gray-50">
            <h4 class="text-sm font-semibold mb-2 flex justify-between items-center">
              <span>{{ $t('meetings.alternative_date', 'Alternativní termín') }}</span>
              <span class="font-normal text-xs px-1.5 py-0.5 rounded" 
                    :class="option.attendance_count === option.total_users ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'">
                {{ option.attendance_count }}/{{ option.total_users }} {{ $t('attendees', 'účastníků') }} 
              </span>
            </h4>

            <div class="flex items-center gap-1.5 text-sm text-gray-700 mb-2">
              <CalendarClock :size="14" class="text-gray-500 flex-shrink-0" />
              <span>{{ formatDate(option.date) }}</span>
            </div>

            <div class="flex flex-col gap-1.5 text-xs">
              <!-- Available users -->
              <template v-if="option.available_users && option.available_users.length > 0">
                <div
                  v-for="user in option.available_users"
                  :key="user.id"
                  class="flex items-center gap-1.5 p-1 rounded bg-green-50 border border-green-200"
                >
                  <UserCheck :size="12" class="text-green-600 flex-shrink-0" />
                  <span class="text-gray-700">{{ user.email }}</span>
                </div>
              </template>

              <!-- Missing users -->
              <template v-if="option.missing_users && option.missing_users.length > 0">
                <div
                  v-for="user in option.missing_users"
                  :key="user.id"
                  class="p-1.5 rounded bg-yellow-50 border border-yellow-200"
                >
                   <div class="flex items-center gap-1.5 mb-1">
                     <Phone :size="12" class="text-yellow-600 flex-shrink-0" />
                     <span class="text-gray-700">{{ user.email }}</span>
                   </div>
                   <div class="text-gray-600 text-[11px] ml-[18px] mb-1">
                     {{ $t('meetings.verify_availability_manual_mark', 'Ověřte dostupnost a označte ručně.') }}
                   </div>
                   <button
                    @click="addUserToDate(user.id, option.date)" 
                    class="btn btn-xs btn-outline ml-[18px]" 
                  >
                    {{ $t('meetings.mark_as_available', 'Označit jako dostupný') }}
                  </button>
                </div>
              </template>
            </div>

            <!-- Option Footer -->
            <div class="mt-2 pt-2 border-t border-gray-200">
               <div v-if="option.attendance_count === option.total_users" class="flex items-center gap-1 text-xs text-green-700 font-medium mb-2">
                  <CheckCircle :size="14" />
                  <span>{{ $t('meetings.all_available_for_this_date', 'Všichni dostupní pro tento termín') }}</span>
               </div>
               <button
                 @click="confirmDate(option.date)" 
                 class="btn btn-primary w-full btn-small"
                 :disabled="option.attendance_count !== option.total_users"
                 :class="{ 'opacity-50 cursor-not-allowed': option.attendance_count !== option.total_users }"
               >
                 {{ $t('meetings.confirm_this_date_and_notify', 'Potvrdit tento termín a oznámit') }}
               </button>
            </div>
         </div>
      </div>
       <div v-else-if="showBestOptions && filteredBestOptions.length === 0" class="text-sm text-gray-500 italic">
          {{ $t('meetings.no_alternative_dates_found_extend', 'Nebyly nalezeny žádné alternativní termíny. Zkuste prodloužit rozsah dat.') }}
       </div>
    </div>

  </div>
</template>

<script>
import { Trash, CheckCircle, Clock, AlertCircle, CircleUserRound, Phone, CalendarClock, ChevronDown, MoreVertical, Users, CheckCircle2, UserCheck, Handshake } from 'lucide-vue-next';
import axios from 'axios';

export default {
  components: {
    Trash,
    CheckCircle,
    Clock,
    AlertCircle,
    CircleUserRound,
    Phone,
    CalendarClock,
    ChevronDown,
    MoreVertical, 
    Users, 
    CheckCircle2, 
    UserCheck,
    Handshake
  },
  props: {
    meeting: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      showBestOptions: false,
      bestOptions: [],
      commonTimeFound: false,
      isParticipantsExpanded: false,
      isDropdownOpen: false
    };
  },
  computed: {
    totalParticipants() {
      return this.meeting.meeting_users?.length || 0;
    },
    respondedCount() {
      return this.meeting.meeting_users?.filter(u => Object.keys(u.selected_dates).length > 0).length || 0;
    },
    meetingStatus() {
      if (this.meeting.confirmed_date) {
        return 'confirmed';
      } else if (this.respondedCount === this.totalParticipants) {
        return 'all-responded';
      } else {
        return 'pending';
      }
    },
    hasPerfectOption() {
      return this.bestOptions.some(option => option.attendance_count === option.total_users);
    },
    filteredBestOptions() {
      if (this.hasPerfectOption) {
        return this.bestOptions.filter(option => option.attendance_count === option.total_users);
      }
      return this.bestOptions;
    }
  },
  watch: {
    respondedCount(newValue) {
      if (newValue === this.totalParticipants && !this.meeting.confirmed_date) {
        this.checkCommonTime();
      }
    },
    meeting: {
      immediate: true,
      handler(newMeeting) {
        if (newMeeting.meeting_users && 
            this.respondedCount === this.totalParticipants && 
            !newMeeting.confirmed_date) {
          this.checkCommonTime();
        }
      }
    }
  },
  mounted() {
    document.addEventListener('click', this.handleClickOutside);
  },
  beforeUnmount() {
    document.removeEventListener('click', this.handleClickOutside);
  },
  methods: {
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleString(this.$i18n.locale, {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    },
    async resendInvitation(meetingUserId) {
      try {
        const response = await axios.post(`/api/v1/meetings/${this.meeting.id}/resend_invitation`, {
          meeting_user_id: meetingUserId
        });
        if (response.data.success) {
          console.log(response.data.message || 'Pozvánka byla znovu odeslána.'); 
        } else {
          console.error(response.data.message || 'Nepodařilo se znovu odeslat pozvánku.');
        }
      } catch (error) {
        console.error("Error resending invitation:", error);
        console.error('Při odesílání pozvánky nastala chyba.');
      }
    },
    async checkCommonTime() {
      try {
        const response = await axios.get(`/api/v1/meetings/${this.meeting.id}/get_best_options`);
        this.bestOptions = response.data.best_options || [];
        
        this.commonTimeFound = this.bestOptions.some(option => 
          option.attendance_count === option.total_users);
          
        if (!this.commonTimeFound) {
          this.fetchBestOptions();
        }
      } catch (error) {
        console.error('Error checking common time:', error);
      }
    },
    async fetchBestOptions() {
      try {
        const response = await axios.get(`/api/v1/meetings/${this.meeting.id}/get_best_options`);
        this.bestOptions = response.data.best_options || [];
      } catch (error) {
        console.error('Error fetching best options:', error);
      }
    },
    async extendDates() {
      try {
        const response = await axios.post(`/api/v1/meetings/${this.meeting.id}/extend_meeting_dates`, {
          days_forward: 7
        });
        
        if (response.data.success) {
          this.$emit('meeting-updated');
        }
      } catch (error) {
        console.error('Error extending dates:', error);
      }
    },
    contactUser(user, date) {
      const subject = encodeURIComponent(`Schůzka ${this.meeting.title} - dostupnost`);
      const body = encodeURIComponent(`Dobrý den,\n\npotvrzuji termín schůzky ${this.meeting.title} na ${this.formatDate(date)}. Jste schopen/schopna se zúčastnit?\n\nS pozdravem`);
      window.open(`mailto:${user.email}?subject=${subject}&body=${body}`);
    },
    async addUserToDate(userId, date) {
      try {
        const formattedDate = typeof date === 'string' ? date : date.toISOString();

        const response = await axios.post(`/api/v1/meetings/${this.meeting.id}/add_user_to_confirmed_date`, {
          meeting_user_id: userId,
          date: formattedDate
        });

        if (response.data.success) {
          this.bestOptions = this.bestOptions.map(option => {
            if (option.date === date) {
              const userToMove = (option.missing_users || []).find(u => u.id === userId);
              if (userToMove) {
                return {
                  ...option,
                  missing_users: (option.missing_users || []).filter(u => u.id !== userId),
                  available_users: [...(option.available_users || []), userToMove],
                  attendance_count: option.attendance_count + 1
                };
              }
            }
            return option;
          });
          
          await this.fetchBestOptions();
          this.$emit('meeting-updated');
        }
      } catch (error) {
        console.error('Error adding user to date:', error);
      }
    },
    async confirmDate(date) {
      try {
        const response = await axios.post(`/api/v1/meetings/${this.meeting.id}/confirm`, {
          confirmed_date: date
        });
        
        if (response.data.success) {
          this.$emit('meeting-updated');
        }
      } catch (error) {
        console.error('Error confirming date:', error);
      }
    },
    toggleParticipants() {
      this.isParticipantsExpanded = !this.isParticipantsExpanded;
    },
    toggleDropdown(event) {
      event.stopPropagation();
      this.isDropdownOpen = !this.isDropdownOpen;
    },
    handleClickOutside(event) {
      const dropdown = this.$el.querySelector('.dropdown');
      if (dropdown && !dropdown.contains(event.target)) {
        this.isDropdownOpen = false;
      }
    }
  }
};
</script>
