<template>
  <div class="activity-dashboard p-4 lg:p-6 max-w-7xl mx-auto">
    <h1 class="text-xl lg:text-2xl font-bold mb-4 lg:mb-6">{{ $t('activities_overview', 'Přehled aktivit') }}</h1>
    
    <!-- Period selector -->
    <div class="bg-white rounded-lg shadow mb-4 lg:mb-6 p-3 lg:p-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-2 lg:space-x-4">
          <button @click="previousPeriod" class="p-1.5 lg:p-2 hover:bg-gray-100 rounded transition-colors">
            <ChevronLeft :size="20" />
          </button>
          <h2 class="text-base lg:text-lg font-medium text-center min-w-[200px]">{{ formattedPeriod }}</h2>
          <button @click="nextPeriod" class="p-1.5 lg:p-2 hover:bg-gray-100 rounded transition-colors">
            <ChevronRight :size="20" />
          </button>
        </div>
        <button @click="goToToday" class="text-sm text-blue-600 hover:text-blue-700">
          {{ $t('today', 'Dnes') }}
        </button>
      </div>
    </div>

    <!-- Loading state -->
    <div v-if="loading" class="flex justify-center py-12">
      <Clock :size="32" class="animate-spin text-gray-500" />
    </div>

    <!-- Content - Mobile first: summaries on top -->
    <div v-else class="space-y-4 lg:space-y-0 lg:grid lg:grid-cols-3 lg:gap-6">
      <!-- Summary cards - First on mobile, second column on desktop -->
      <div class="order-1 lg:order-2 lg:col-span-2 space-y-4">
        <!-- Section title -->
        <h2 class="text-lg font-medium text-gray-900">{{ $t('project_hours_report', 'Hodiny podle projektů') }}</h2>
        
        <!-- Work summary cards -->
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div v-for="(summary, workId) in workSummary" :key="workId" 
               class="bg-white rounded-lg shadow p-4 hover:shadow-md transition-shadow">
            <div class="flex justify-between items-start mb-2">
              <h4 class="font-medium text-sm text-gray-900">{{ summary.title }}</h4>
              <span class="text-xs text-gray-500">{{ summary.count }}x</span>
            </div>
            <div class="text-2xl font-bold text-gray-900 mb-1">
              {{ formatHours(summary.totalMinutes) }}
            </div>
            <p v-if="summary.location" class="text-xs text-gray-500 flex items-center">
              <MapPin :size="12" class="mr-1" />{{ summary.location }}
            </p>
          </div>
          
          <!-- Other activities card -->
          <div v-if="otherActivitiesSummary.totalMinutes > 0" 
               class="bg-gray-50 rounded-lg shadow p-4">
            <div class="flex justify-between items-start mb-2">
              <h4 class="font-medium text-sm text-gray-700">{{ $t('other_activities', 'Ostatní aktivity') }}</h4>
              <span class="text-xs text-gray-500">{{ otherActivitiesSummary.count }}x</span>
            </div>
            <div class="text-2xl font-bold text-gray-700">
              {{ formatHours(otherActivitiesSummary.totalMinutes) }}
            </div>
          </div>
        </div>
        
        <!-- Total summary card -->
        <div v-if="totalMinutes > 0" class="bg-blue-50 rounded-lg shadow p-4">
          <div class="flex justify-between items-center">
            <span class="text-lg font-medium text-blue-900">{{ $t('total_week', 'Celkem za týden') }}</span>
            <span class="text-2xl font-bold text-blue-900">{{ formatHours(totalMinutes) }}</span>
          </div>
        </div>
        
        <!-- Empty state for summaries -->
        <div v-if="Object.keys(workSummary).length === 0 && otherActivitiesSummary.totalMinutes === 0" 
             class="bg-white rounded-lg shadow p-8 text-center text-gray-500">
          <Clock :size="48" class="mx-auto mb-4 text-gray-300" />
          <p>{{ $t('no_activities_week', 'Žádné aktivity v tomto týdnu') }}</p>
        </div>
      </div>

      <!-- Activities list - Second on mobile, first column on desktop -->
      <div class="order-2 lg:order-1 lg:col-span-1 bg-white rounded-lg shadow">
        <div class="px-4 py-3 border-b">
          <h3 class="font-medium">{{ $t('week_activities', 'Aktivity týdne') }}</h3>
        </div>
        <div class="p-4">
          <div v-if="activities.length === 0" class="text-center py-8 text-gray-500">
            {{ $t('no_activities_period', 'Žádné aktivity v tomto období') }}
          </div>
          <div v-else class="space-y-3">
            <div v-for="(dayActivities, date) in groupedActivities" :key="date">
              <h4 class="text-sm font-medium text-gray-700 mb-2">{{ formatDate(date) }}</h4>
              <div class="space-y-2 mb-4">
                <div v-for="activity in dayActivities" :key="activity.id" 
                     class="flex items-start p-2 bg-gray-50 rounded-md hover:bg-gray-100 transition-colors group">
                  <div class="text-xs text-gray-500 mr-2 w-10 flex-shrink-0">
                    {{ formatTime(activity.start_time) }}
                  </div>
                  <div class="flex-1 min-w-0">
                    <p class="text-sm break-words">{{ activity.description || getActivityDescription(activity) }}</p>
                    <p v-if="activity.work" class="text-xs text-blue-600 mt-0.5">
                      {{ activity.work.title }}
                    </p>
                    <p class="text-xs text-gray-500 mt-0.5">
                      {{ activity.duration_in_text || calculateDuration(activity) }}
                    </p>
                  </div>
                  <button
                    @click="deleteActivity(activity.id)"
                    class="ml-2 p-1 text-gray-400 hover:text-red-600"
                    :title="$t('delete', 'Smazat')"
                  >
                    <Trash2 :size="16" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script>
import axios from 'axios';
import { Clock, ChevronLeft, ChevronRight, MapPin, Trash2 } from 'lucide-vue-next';
import { getLocaleString } from '../../utils/dateFormatter';

export default {
  name: 'ActivityDashboard',
  components: {
    Clock,
    ChevronLeft,
    ChevronRight,
    MapPin,
    Trash2
  },
  data() {
    return {
      loading: false,
      activities: [],
      startDate: null,
      endDate: null
    };
  },
  computed: {
    formattedPeriod() {
      if (!this.startDate || !this.endDate) return '';
      
      const start = new Date(this.startDate);
      const end = new Date(this.endDate);
      const options = { day: 'numeric', month: 'short' };
      const year = start.getFullYear();
      
      const locale = getLocaleString(this.$i18n.locale);
      return `${start.toLocaleDateString(locale, options)} - ${end.toLocaleDateString(locale, options)} ${year}`;
    },
    
    groupedActivities() {
      const grouped = {};
      
      this.activities.forEach(activity => {
        const date = activity.start_time.split('T')[0];
        if (!grouped[date]) {
          grouped[date] = [];
        }
        grouped[date].push(activity);
      });
      
      // Sort activities within each day by start time
      Object.keys(grouped).forEach(date => {
        grouped[date].sort((a, b) => new Date(a.start_time) - new Date(b.start_time));
      });
      
      return grouped;
    },
    
    workSummary() {
      const summary = {};
      
      this.activities.forEach(activity => {
        // Include all activities that have a work association
        // regardless of activity_type (work_at_location, work_remote, travel_to_work, etc.)
        if (activity.work_id && activity.work) {
          if (!summary[activity.work_id]) {
            summary[activity.work_id] = {
              title: activity.work.title,
              location: activity.work.location,
              totalMinutes: 0,
              count: 0
            };
          }
          
          summary[activity.work_id].totalMinutes += this.getActivityMinutes(activity);
          summary[activity.work_id].count++;
        }
      });
      
      return summary;
    },
    
    otherActivitiesSummary() {
      let totalMinutes = 0;
      let count = 0;
      
      this.activities.forEach(activity => {
        if (!activity.work_id) {
          totalMinutes += this.getActivityMinutes(activity);
          count++;
        }
      });
      
      return { totalMinutes, count };
    },
    
    totalMinutes() {
      return this.activities.reduce((total, activity) => {
        return total + this.getActivityMinutes(activity);
      }, 0);
    }
  },
  mounted() {
    this.initializeDates();
    this.fetchActivities();
  },
  methods: {
    initializeDates() {
      const today = new Date();
      const dayOfWeek = today.getDay();
      const diff = today.getDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1); // Monday
      
      this.startDate = new Date(today);
      this.startDate.setDate(diff);
      this.startDate.setHours(0, 0, 0, 0);
      
      this.endDate = new Date(this.startDate);
      this.endDate.setDate(this.startDate.getDate() + 6); // Sunday
      this.endDate.setHours(23, 59, 59, 999);
    },
    
    async fetchActivities() {
      this.loading = true;
      try {
        const response = await axios.get('/daily_activities', {
          params: {
            start_date: this.startDate.toISOString().split('T')[0],
            end_date: this.endDate.toISOString().split('T')[0]
          }
        });
        this.activities = response.data;
      } catch (error) {
        console.error('Error fetching activities:', error);
      } finally {
        this.loading = false;
      }
    },
    
    previousPeriod() {
      this.startDate = new Date(this.startDate);
      this.endDate = new Date(this.endDate);
      this.startDate.setDate(this.startDate.getDate() - 7);
      this.endDate.setDate(this.endDate.getDate() - 7);
      this.fetchActivities();
    },
    
    nextPeriod() {
      this.startDate = new Date(this.startDate);
      this.endDate = new Date(this.endDate);
      this.startDate.setDate(this.startDate.getDate() + 7);
      this.endDate.setDate(this.endDate.getDate() + 7);
      this.fetchActivities();
    },
    
    goToToday() {
      this.initializeDates();
      this.fetchActivities();
    },
    
    formatDate(dateString) {
      const date = new Date(dateString);
      const options = { weekday: 'long', day: 'numeric', month: 'long' };
      const locale = getLocaleString(this.$i18n.locale);
      return date.toLocaleDateString(locale, options);
    },
    
    formatTime(datetime) {
      const date = new Date(datetime);
      const locale = getLocaleString(this.$i18n.locale);
      return date.toLocaleTimeString(locale, { hour: '2-digit', minute: '2-digit' });
    },
    
    calculateDuration(activity) {
      if (!activity.end_time) return '';
      
      const minutes = this.getActivityMinutes(activity);
      return this.formatDuration(minutes);
    },
    
    getActivityMinutes(activity) {
      if (!activity.start_time) return 0;
      
      // If activity has a duration field, use it
      if (activity.duration) {
        return Math.round(activity.duration / 60);
      }
      
      // Otherwise calculate from start/end times
      if (!activity.end_time) return 0;
      
      const start = new Date(activity.start_time);
      const end = new Date(activity.end_time);
      return Math.round((end - start) / 60000);
    },
    
    formatDuration(totalMinutes) {
      const hours = Math.floor(totalMinutes / 60);
      const minutes = totalMinutes % 60;
      
      if (hours > 0) {
        return `${hours}h ${minutes}min`;
      }
      return `${minutes}min`;
    },
    
    formatHours(totalMinutes) {
      const hours = (totalMinutes / 60).toFixed(1);
      return `${hours}h`;
    },
    
    getActivityDescription(activity) {
      // Generate description based on activity type
      if (activity.activity_type === 'work_at_location') {
        return this.$t('works.activities.at_customer', 'U zákazníka');
      } else if (activity.activity_type === 'work_remote') {
        return this.$t('works.activities.work_remote', 'Práce na dálku');
      } else if (activity.activity_type === 'travel_to_work') {
        return this.$t('works.activities.travel_to_work', 'Cesta k zákazníkovi');
      } else if (activity.activity_type === 'break') {
        return this.$t('break', 'Přestávka');
      } else if (activity.activity_type === 'regular') {
        return this.$t('other_activity', 'Jiná aktivita');
      }
      return activity.activity_type || this.$t('activity', 'Aktivita');
    },
    
    async deleteActivity(activityId) {
      if (!confirm(this.$t('confirm_delete_activity', 'Opravdu chcete smazat tuto aktivitu?'))) {
        return;
      }
      
      try {
        await axios.delete(`/daily_activities/${activityId}`);
        // Remove from local data
        this.activities = this.activities.filter(a => a.id !== activityId);
        // Show success message
        if (window.flashMessage) {
          window.flashMessage(this.$t('activity_deleted', 'Aktivita byla smazána'), 'success');
        }
      } catch (error) {
        console.error('Error deleting activity:', error);
        if (window.flashMessage) {
          window.flashMessage(this.$t('error_deleting_activity', 'Chyba při mazání aktivity'), 'error');
        }
      }
    }
  }
};
</script>

<style scoped>
.activity-dashboard {
  min-height: calc(100vh - 100px);
}
</style>