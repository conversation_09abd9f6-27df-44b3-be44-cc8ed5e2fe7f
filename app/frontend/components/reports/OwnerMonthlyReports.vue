// ABOUTME: Owner monthly reports component - displays attendance reports for all employees
// ABOUTME: Allows managers to view and export monthly attendance data for their team

<template>
  <div class="owner-monthly-reports">
    <div class="reports-container">
      <!-- Employee List Sidebar -->
      <div class="employee-sidebar">
        <div class="sidebar-header">
          <h3 class="sidebar-title">{{ $t('team', 'Tým') }}</h3>
        </div>
        
        <div v-if="loadingEmployees" class="sidebar-loading">
          <Clock :size="20" class="loading-icon" />
          <span>{{ $t('loading', 'Načítání...') }}</span>
        </div>
        
        <div v-else class="employee-list">
          <div 
            v-for="employee in employees" 
            :key="employee.id"
            class="employee-item"
            :class="{ 'employee-item--selected': selectedEmployeeId === employee.id }"
            @click="selectEmployee(employee)"
          >
            <div class="employee-info">
              <div class="employee-name">
                {{ employee.first_name }} {{ employee.last_name }}
              </div>
            </div>
          </div>
          
          <div v-if="employees.length === 0" class="no-employees">
            <p>{{ $t('owner_reports.no_employees', 'Žádní zaměstnanci') }}</p>
          </div>
        </div>
      </div>

      <!-- Report Content -->
      <div class="report-content">
        <div v-if="isLoading" class="time-tracking-loading">
          <Clock :size="20" class="time-tracking-loading-icon" />
        </div>
        
        <template v-else-if="selectedEmployee">
          <div class="report">
            <div class="report-header">
              <div class="report-title-section">
                <h2 class="report-title">
                  {{ $t('attendance_report.title', 'Docházka') }} - {{ selectedEmployee.first_name }} {{ selectedEmployee.last_name }}
                </h2>
                <div class="report-subtitle">{{ currentMonth.charAt(0).toUpperCase() + currentMonth.slice(1) }}</div>
              </div>
              <div class="report-controls">
                <select :value="getCurrentMonthValue()" @change="onMonthSelect($event.target.value)" class="month-selector">
                  <option value="" disabled selected>{{ $t('select_month', 'Vyberte měsíc') }}</option>
                  <option v-for="month in availableMonths" 
                          :key="month.value" 
                          :value="month.value">
                    {{ month.label }}
                  </option>
                </select>
                <PDFExport 
                  :days="days"
                  :selectedDate="selectedDate"
                  :totalHours="totalHours / 60"
                  :employeeName="`${selectedEmployee.first_name} ${selectedEmployee.last_name}`"
                />
              </div>
            </div>

            <div class="report-table">
              <div class="report-row report-head">
                <div class="report-cell">{{ $t('day', 'Den') }}</div>
                <div class="report-cell">{{ $t('start', 'Začátek') }}</div>
                <div class="report-cell">{{ $t('end', 'Konec') }}</div>
                <div class="report-cell">{{ $t('break', 'Pauza') }}</div>
                <div class="report-cell">{{ $t('break_end', 'Konec pauzy') }}</div>
                <div class="report-cell">{{ $t('hours', 'Hodin') }}</div>
              </div>

              <div 
                v-for="day in days" 
                :key="day.day" 
                class="report-row"
                :class="{
                  'is-weekend': day.is_weekend,
                  'is-holiday': day.is_holiday
                }"
              >
                <div class="report-cell">
                  <div class="day-info">
                    <div>
                      <span class="day-number">{{ day.day }}. </span>
                      <span class="weekday">{{ getWeekday(day.date) }}</span>
                    </div>
                  </div>
                </div>
                
                <template v-if="day.log">
                  <div class="report-cell">{{ formatTime(day.log.start_time) }}</div>
                  <div class="report-cell">{{ formatTime(day.log.end_time) }}</div>
                  <div class="report-cell">{{ day.log.break_start ? formatTime(day.log.break_start) : '-' }}</div>
                  <div class="report-cell">{{ day.log.break_end ? formatTime(day.log.break_end) : '-' }}</div>
                  <div class="report-cell">{{ formatDuration(day.log.duration) }}</div>
                </template>
                
                <template v-else-if="day.event">
                  <div class="report-cell merge-cell">
                    <!-- DEBUG: {{ JSON.stringify(day.event) }} -->
                    {{ day.event.description }}
                  </div>
                </template>
                
                <template v-else>
                  <div class="report-cell">-</div>
                  <div class="report-cell">-</div>
                  <div class="report-cell">-</div>
                  <div class="report-cell">-</div>
                  <div class="report-cell">-</div>
                </template>
              </div>

              <div class="report-row report-total">
                <div class="report-cell">{{ $t('total', 'Spolu') }}</div>
                <div class="report-cell"></div>
                <div class="report-cell"></div>
                <div class="report-cell"></div>
                <div class="report-cell"></div>
                <div class="report-cell">{{ formatDuration(totalHours) }}</div>
              </div>
            </div>
            
            <!-- Stats Summary -->
            <div v-if="selectedEmployee && reportStats" class="report-stats">
              <div class="stats-grid">
                <div class="stat-item">{{ $t('attendance_report.days_worked', 'Odpracované dny') }}: {{ reportStats.daysWorked || 0 }}</div>
                <div class="stat-item">{{ $t('event_type.vacation', 'Dovolená') }}: {{ reportStats.vacation || 0 }}</div>
                <div class="stat-item">{{ $t('event_type.illness', 'Nemocenská') }}: {{ reportStats.illness || 0 }}</div>
                <div class="stat-item">{{ $t('event_type.day_care', 'Návštěva lékaře') }}: {{ reportStats.day_care || 0 }}</div>
                <div class="stat-item">{{ $t('event_type.family_sick', 'OČR') }}: {{ reportStats.family_sick || 0 }}</div>
                <div class="stat-item">{{ $t('event_type.other', 'Jiná absence') }}: {{ reportStats.other || 0 }}</div>
              </div>
            </div>
          </div>
        </template>
        
        <div v-else class="no-selection">
          <p class="text-gray-500">{{ $t('owner_reports.select_employee', 'Vyberte zaměstnance pro zobrazení výkazu') }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';
import { Clock } from 'lucide-vue-next';
import { sendFlashMessage } from '@/utils/flashMessage';
import PDFExport from '../PDFExport.vue';
import dayjs from 'dayjs';

export default {
  name: 'OwnerMonthlyReports',
  components: {
    Clock,
    PDFExport
  },
  data() {
    // Get persisted date or default to current month
    const persistedDate = localStorage.getItem('ownerReports_lastViewedMonth');
    const selectedDate = persistedDate ? dayjs(persistedDate) : dayjs();
    
    return {
      employees: [],
      loadingEmployees: true,
      selectedEmployeeId: null,
      selectedEmployee: null,
      isLoading: false,
      currentMonth: '',
      reportData: null,
      days: [],
      totalHours: 0,
      availableMonths: [],
      currentYear: selectedDate.year(),
      currentMonthNumber: selectedDate.month() + 1,
      reportStats: null
    };
  },
  computed: {
    selectedDate() {
      return dayjs().year(this.currentYear).month(this.currentMonthNumber - 1).date(1).toDate();
    }
  },
  async mounted() {
    // If contract ID provided in route, select that employee
    if (this.$route.params.contractId) {
      this.selectedEmployeeId = parseInt(this.$route.params.contractId);
    }
    
    await this.fetchEmployees();
    this.initializeMonths();
    
    // Auto-select first employee or the one from route
    if (this.selectedEmployeeId && this.employees.length > 0) {
      const employee = this.employees.find(e => e.id === this.selectedEmployeeId);
      if (employee) {
        await this.selectEmployee(employee);
      }
    } else if (this.employees.length > 0) {
      await this.selectEmployee(this.employees[0]);
    }
  },
  methods: {
    async fetchEmployees() {
      this.loadingEmployees = true;
      
      try {
        const response = await axios.get('/api/v1/contracts/colleagues', {
          headers: { 'Accept': 'application/json' },
          params: { include_self: 'true' }
        });
        
        // Use colleagues data from response
        this.employees = response.data.colleagues || [];
      } catch (error) {
        console.error('Error fetching employees:', error);
        sendFlashMessage(
          error.response?.data?.message || this.$t('owner_reports.error_loading_employees', 'Chyba při načítání zaměstnanců'),
          'error'
        );
      } finally {
        this.loadingEmployees = false;
      }
    },
    
    async selectEmployee(employee) {
      this.selectedEmployeeId = employee.id;
      this.selectedEmployee = employee;
      
      // Update URL without causing navigation
      const newPath = `/${this.$route.params.locale}/reports/owner-monthly/${employee.id}`;
      if (this.$route.path !== newPath) {
        this.$router.replace(newPath);
      }
      
      // Fetch report data for currently selected month (persisted or current)
      await this.fetchReportData(this.currentYear, this.currentMonthNumber);
    },
    
    async fetchReportData(year, month) {
      this.isLoading = true;
      
      try {
        const params = {
          contract_id: this.selectedEmployeeId,
          year: year,
          month: month
        };
        console.log('🔍 DEBUG API Call parameters:', params);
        
        const response = await axios.get('/api/v1/daily_logs/fetch_employee_report_data', {
          params: params,
          headers: { 'Accept': 'application/json' }
        });
        
        this.reportData = response.data;
        console.log('🔍 DEBUG API Response for contract', this.selectedEmployeeId, ':', response.data);
        console.log('🔍 DEBUG Events in API response:', response.data.events);
        
        this.currentYear = year;
        this.currentMonthNumber = month;
        this.processReportData(year, month);
        this.updateCurrentMonth(year, month);
        
        // Calculate stats for current report
        this.reportStats = this.calculateEmployeeStats(response.data);
      } catch (error) {
        console.error('Error fetching report data:', error);
        if (error.response?.status === 403) {
          sendFlashMessage(
            this.$t('unauthorized_access', 'Nemáte oprávnění zobrazit tyto údaje'),
            'error'
          );
        } else {
          sendFlashMessage(
            error.response?.data?.message || this.$t('owner_reports.error_loading_report', 'Chyba při načítání výkazu'),
            'error'
          );
        }
      } finally {
        this.isLoading = false;
      }
    },
    
    processReportData(year, month) {
      const daysInMonth = dayjs().year(year).month(month - 1).daysInMonth();
      const monthData = [];
      let totalMinutes = 0;

      // Create maps for efficient lookups - O(1) instead of O(n)
      const logsByDate = new Map(this.reportData.daily_logs.map(l => [
        dayjs(l.start_time).format('YYYY-MM-DD'), l
      ]));
      
      const holidaysByDate = new Map((this.reportData.holidays || []).map(h => {
        try {
          return [dayjs(h.date).format('YYYY-MM-DD'), h];
        } catch (error) {
          console.warn('Invalid holiday date:', h.date);
          return [null, null];
        }
      }).filter(([dateStr]) => dateStr !== null));
      
      // Create events map for efficient lookups
      const eventsByDate = new Map();
      console.log('🔍 DEBUG processReportData: Events from reportData:', this.reportData.events);
      console.log('🔍 DEBUG processReportData: Events array length:', this.reportData.events?.length || 0);
      
      if (this.reportData.events && this.reportData.events.length > 0) {
        console.log('🔍 DEBUG Processing', this.reportData.events.length, 'events for month', month, 'year', year);
        this.reportData.events.forEach((event, index) => {
          try {
            console.log(`🔍 DEBUG Event ${index}:`, event);
            const eventStartDate = dayjs(event.start_time).format('YYYY-MM-DD');
            const eventEndDate = event.end_time ? dayjs(event.end_time).format('YYYY-MM-DD') : eventStartDate;
            console.log(`🔍 DEBUG Event ${index} dates:`, eventStartDate, 'to', eventEndDate);
            
            // Handle multi-day events
            const start = dayjs(eventStartDate);
            const end = dayjs(eventEndDate);
            
            let current = start;
            while (current.isBefore(end) || current.isSame(end, 'day')) {
              const dateStr = current.format('YYYY-MM-DD');
              console.log(`🔍 DEBUG Adding event ${index} to date:`, dateStr);
              eventsByDate.set(dateStr, event);
              current = current.add(1, 'day');
            }
          } catch (error) {
            console.warn('Invalid event date:', event, error);
          }
        });
      } else {
        console.log('🔍 DEBUG No events found or empty events array');
      }
      
      console.log('🔍 DEBUG Final eventsByDate map:', eventsByDate);

      for (let day = 1; day <= daysInMonth; day++) {
        const date = dayjs().year(year).month(month - 1).date(day);
        const dateStr = date.format('YYYY-MM-DD');
        const dayOfWeek = date.day();
        
        // Find daily log for this date - O(1) lookup
        const log = logsByDate.get(dateStr);
        
        // Check if it's a holiday - O(1) lookup
        const holiday = holidaysByDate.get(dateStr);
        
        // Check if there's an event for this date - O(1) lookup
        const event = eventsByDate.get(dateStr);
        if (event) {
          console.log('🔍 DEBUG Found event for date', dateStr, ':', event);
        }

        let duration = 0;
        let breakStart = null;
        let breakEnd = null;
        
        if (log) {
          // Calculate duration using dayjs
          if (log.start_time && log.end_time) {
            const start = dayjs(log.start_time);
            const end = dayjs(log.end_time);
            duration = end.diff(start, 'minute'); // in minutes
            
            // Get break times if available
            if (log.breaks && log.breaks.length > 0) {
              const firstBreak = log.breaks[0];
              breakStart = firstBreak.start_time;
              breakEnd = firstBreak.end_time;
              
              // Subtract break duration from total
              if (breakStart && breakEnd) {
                const breakDuration = dayjs(breakEnd).diff(dayjs(breakStart), 'minute');
                duration -= breakDuration;
              }
            }
          }
          
          totalMinutes += duration;
        }

        monthData.push({
          day: day,
          date: date.toDate(), // Convert back to native Date for compatibility
          is_weekend: dayOfWeek === 0 || dayOfWeek === 6,
          is_holiday: !!holiday,
          log: log ? {
            start_time: log.start_time,
            end_time: log.end_time,
            break_start: breakStart,
            break_end: breakEnd,
            duration: duration
          } : null,
          event: event ? {
            description: this.$t(`event_type.${event.event_type}`, event.event_type)
          } : holiday ? {
            description: holiday.name || this.$t('holiday', 'Svátek')
          } : null
        });
        
        // Debug log for final day data
        if (event || holiday) {
          console.log('🔍 DEBUG Final day data for', dateStr, ':', {
            day: day,
            hasLog: !!log,
            hasEvent: !!event,
            hasHoliday: !!holiday,
            finalEvent: event ? {
              description: this.$t(`event_type.${event.event_type}`, event.event_type)
            } : holiday ? {
              description: holiday.name || this.$t('holiday', 'Svátek')
            } : null
          });
        }
      }

      console.log('🔍 DEBUG Final days array length:', monthData.length);
      console.log('🔍 DEBUG Days with events:', monthData.filter(d => d.event).length);
      
      this.days = monthData;
      this.totalHours = totalMinutes;
    },
    
    initializeMonths() {
      const months = [];
      let date = dayjs();
      
      // Show last 12 months using dayjs
      for(let i = 0; i < 12; i++) {
        months.unshift({
          value: `${date.year()}-${date.month() + 1}`,
          label: date.toDate().toLocaleString(this.$i18n.locale, { month: 'long', year: 'numeric' })
        });
        date = date.subtract(1, 'month');
      }
      
      this.availableMonths = months;
    },
    
    getCurrentMonthValue() {
      const now = dayjs();
      if (!this.currentMonth) {
        return `${now.year()}-${now.month() + 1}`;
      }
      // Parse current month and return in format YYYY-M
      const [year, month] = this.getYearMonthFromString(this.currentMonth);
      return `${year}-${month}`;
    },
    
    calculateEmployeeStats(reportData) {
      let daysWorked = 0;
      const eventCounts = {
        vacation: 0,
        illness: 0,
        day_care: 0,
        family_sick: 0,
        other: 0
      };
      
      // Process daily logs
      if (reportData.daily_logs) {
        reportData.daily_logs.forEach(log => {
          if (log.start_time && log.end_time) {
            daysWorked++;
          }
        });
      }
      
      // Process events
      if (reportData.events) {
        reportData.events.forEach(event => {
          const eventType = event.event_type || 'other';
          if (eventCounts.hasOwnProperty(eventType)) {
            eventCounts[eventType]++;
          } else {
            eventCounts.other++;
          }
        });
      }
      
      return {
        daysWorked,
        ...eventCounts
      };
    },
    
    async onMonthSelect(value) {
      if (!value) return;
      
      const [year, month] = value.split('-').map(Number);
      
      // Persist selected month to localStorage
      const selectedDate = dayjs().year(year).month(month - 1).startOf('month');
      localStorage.setItem('ownerReports_lastViewedMonth', selectedDate.toISOString());
      
      await this.fetchReportData(year, month);
    },
    
    updateCurrentMonth(year, month) {
      const date = dayjs().year(year).month(month - 1).date(1);
      this.currentMonth = date.toDate().toLocaleDateString(this.$i18n.locale, { month: 'long', year: 'numeric' });
    },
    
    getYearMonthFromString(monthString) {
      // Extract year and month from localized string
      const now = new Date();
      const year = parseInt(monthString.match(/\d{4}/)?.[0] || now.getFullYear());
      
      // Try to find month number
      for (let m = 0; m < 12; m++) {
        const testDate = new Date(year, m, 1);
        const testString = testDate.toLocaleDateString(this.$i18n.locale, { month: 'long' });
        if (monthString.toLowerCase().includes(testString.toLowerCase())) {
          return [year, m + 1];
        }
      }
      
      return [now.getFullYear(), now.getMonth() + 1];
    },
    
    getWeekday(date) {
      return date.toLocaleDateString(this.$i18n.locale, { weekday: 'short' });
    },
    
    formatTime(timeString) {
      if (!timeString) return '-';
      const date = new Date(timeString);
      return date.toLocaleTimeString(this.$i18n.locale, { hour: '2-digit', minute: '2-digit' });
    },
    
    formatDuration(minutes) {
      if (!minutes) return '0:00';
      const hours = Math.floor(minutes / 60);
      const mins = Math.round(minutes % 60);
      return `${hours}:${mins.toString().padStart(2, '0')}`;
    }
  }
};
</script>

<style scoped>
.owner-monthly-reports {
  padding: 1rem;
  min-height: 100vh;
}

.reports-container {
  display: flex;
  gap: 1rem;
}

.employee-sidebar {
  width: 500px;
  min-width: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 1rem;
  align-self: flex-start;
  max-height: calc(100vh - 2rem);
  display: flex;
  flex-direction: column;
}

@media (max-width: 1200px) {
  .employee-sidebar {
    width: 350px;
    min-width: 300px;
  }
}

@media (max-width: 1080px) {
  .reports-container {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .employee-sidebar {
    width: 100%;
    min-width: unset;
    order: 1;
    position: static;
    max-height: 300px;
  }
  
  .report-content {
    order: 2;
  }
}

@media (max-width: 768px) {
  .owner-monthly-reports {
    padding: 0.5rem;
  }
  
  .reports-container {
    gap: 0.5rem;
  }
}

.sidebar-header {
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.sidebar-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
}

.sidebar-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  gap: 0.5rem;
  color: #6b7280;
}

.loading-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.employee-list {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.employee-item {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #f3f4f6;
  cursor: pointer;
  transition: background-color 0.15s;
}

.employee-item:hover {
  background-color: #f9fafb;
}

.employee-item--selected {
  background-color: #eff6ff;
  border-left: 3px solid #3b82f6;
}

.employee-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.employee-name {
  font-weight: 500;
  color: #1f2937;
}

.report-stats {
  margin-top: 2rem;
  padding: 1.5rem;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
}

.stat-item {
  font-size: 0.875rem;
  color: #374151;
  font-weight: 500;
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.5rem;
  }
  
  .stat-item {
    font-size: 0.8rem;
  }
}

.no-employees {
  padding: 2rem;
  text-align: center;
  color: #6b7280;
}

.report-content {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.no-selection {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 3rem;
}

.time-tracking-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3rem;
}

.time-tracking-loading-icon {
  animation: spin 1s linear infinite;
}

.report {
  padding: 1rem;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  padding: 1rem;
  border-bottom: 2px solid #e5e7eb;
  flex-wrap: wrap;
  gap: 1rem;
}

.report-title-section {
  flex: 1;
  min-width: 250px;
}

.report-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  line-height: 1.4;
}

.report-subtitle {
  font-size: 1rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.report-controls {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  align-items: center;
}

@media (max-width: 768px) {
  .report-header {
    flex-direction: column;
    align-items: stretch;
    padding: 0.75rem;
  }
  
  .report-title {
    font-size: 1.125rem;
  }
  
  .report-controls {
    justify-content: flex-start;
    margin-top: 0.5rem;
  }
  
  .month-selector {
    width: 100%;
    max-width: 200px;
  }
}

@media (max-width: 480px) {
  .report-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }
  
  .month-selector {
    max-width: 100%;
  }
}

.month-selector {
  padding: 0.5rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  color: #374151;
  font-size: 0.875rem;
  cursor: pointer;
  min-width: 180px;
}

.month-selector:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

@media (max-width: 480px) {
  .month-selector {
    min-width: 100%;
  }
}

.report-table {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  max-width: 900px;
  margin: 0 auto;
}

.report-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1fr;
  border-bottom: 1px solid #f3f4f6;
}

@media (max-width: 768px) {
  .report-table {
    font-size: 0.8rem;
  }
  
  .report-cell {
    padding: 0.5rem;
  }
}

@media (max-width: 480px) {
  .report-row {
    grid-template-columns: 1.5fr 1fr 1fr 1fr 1fr 0.8fr;
  }
  
  .report-table {
    font-size: 0.75rem;
  }
  
  .report-cell {
    padding: 0.4rem;
  }
  
  .day-number {
    font-size: 0.8rem;
  }
  
  .weekday {
    font-size: 0.7rem;
  }
}

.report-row:last-child {
  border-bottom: none;
}

.report-head {
  background: #f9fafb;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.report-cell {
  padding: 0.75rem;
  display: flex;
  align-items: center;
  font-size: 0.9rem;
}

.report-cell.merge-cell {
  grid-column: 2 / -1;
  color: #6b7280;
  font-style: italic;
}

.day-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.day-number {
  font-weight: 500;
}

.weekday {
  color: #6b7280;
  text-transform: capitalize;
}

.is-weekend {
  background-color: #dcfce7;
}

.is-holiday {
  background-color: #fef3c7;
}

.report-total {
  background: #f3f4f6;
  font-weight: 600;
}
</style>