<template>
  <!-- Render component directly if it's MeetingForm -->
  <component 
    v-if="isVisible && componentToLoad === 'MeetingForm'"
    :is="componentToLoad" 
    v-bind="componentProps"
    @close="closeModal" 
    @meeting-created="handleMeetingCreated"
  />

  <!-- Render the standard modal wrapper for other components -->
  <div v-else-if="isVisible" class="central-modal-overlay" @click.self="closeModal">
    <div class="central-modal-container">
      <div class="central-modal-header">
        <h3>{{ modalTitle }}</h3>
        <button @click="closeModal" class="close-btn">&times;</button>
      </div>
      <div class="central-modal-content">
        <component 
          :is="componentToLoad" 
          v-bind="componentProps"
          @close-modal="closeModal" 
          @cancel="closeModal" 
          @saved="handleSave"
          @event-added="handleEventAdded"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { shallowRef } from 'vue';
import { sendFlashMessage } from '/utils/flashMessage'; // Assuming flash messages might be needed

export default {
  name: 'CentralModal',
  data() {
    return {
      isVisible: false,
      modalTitle: '',
      componentToLoad: null,
      componentProps: {},
      loadedComponentRef: shallowRef(null) // Use shallowRef for components
    };
  },
  methods: {
    openModal(event) {
      const { componentName, title, props } = event.detail;
      
      // Resolve component - assumes componentName is globally registered or imported
      // For dynamic imports (if needed later): 
      // this.loadedComponentRef = shallowRef(defineAsyncComponent(() => import(`../${componentName}.vue`))); 
      // For now, relying on global registration in application.js
      this.componentToLoad = componentName; 

      this.modalTitle = title || 'Modal';
      this.componentProps = props || {};
      this.isVisible = true;
      document.body.style.overflow = 'hidden'; 
    },
    closeModal() {
      this.isVisible = false;
      this.modalTitle = '';
      this.componentToLoad = null;
      this.componentProps = {};
      document.body.style.overflow = ''; 
    },
    // Generic handler for 'saved' event (like from WorkForm)
    handleSave(payload) {
      // Check if this is an update or create based on props
      const isUpdate = this.componentProps?.work?.id !== undefined;
      const eventName = isUpdate ? 'work-updated' : 'work-added';
      const message = isUpdate 
        ? this.$t('works.updated', 'Zakázka byla aktualizována')
        : this.$t('works.created', 'Zakázka byla vytvořena');
      
      document.dispatchEvent(new CustomEvent(eventName, { detail: { work: payload } }));
      sendFlashMessage(message, 'success'); 
      this.closeModal();
    },
    // Specific handler for 'event-added' (from EventForm)
    handleEventAdded(payload) {
      document.dispatchEvent(new CustomEvent('event-added', { detail: { event: payload } }));
      sendFlashMessage(this.$t('event_added', 'Událost byla přidána'), 'success');
      this.closeModal();
    },
    // Handler for 'meeting-created' event
    handleMeetingCreated(payload) {
      document.dispatchEvent(new CustomEvent('meeting-created', { detail: { meeting: payload } })); 
      sendFlashMessage(this.$t('meeting_created_success', 'Schůzka byla úspěšně vytvořena'), 'success');
      this.closeModal();
    }
  },
  mounted() {
    document.addEventListener('open-central-modal', this.openModal);
    window.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isVisible) {
        this.closeModal();
      }
    });
  },
  beforeUnmount() { // Use beforeUnmount in Vue 3
    document.removeEventListener('open-central-modal', this.openModal);
    window.removeEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isVisible) {
        this.closeModal(); // Ensure listener cleanup matches registration
      }
    });
    document.body.style.overflow = '';
  }
};
</script>

<style scoped>
.central-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9990; /* High z-index */
}

.central-modal-container {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 550px; /* Adjust as needed */
  max-height: 90vh;
  min-height: 600px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.central-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
}

.central-modal-header h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.8rem;
  cursor: pointer;
  color: #666;
  padding: 0;
  line-height: 1;
}

.close-btn:hover {
  color: #333;
}

.central-modal-content {
  padding: 20px;
  overflow-y: auto; 
  flex-grow: 1;
}
</style> 