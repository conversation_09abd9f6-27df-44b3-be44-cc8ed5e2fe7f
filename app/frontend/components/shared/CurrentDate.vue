<template>
  <div class="current-date">
    {{ formatDate(currentDate) }}
  </div>
</template>

<script>
import { getLocaleString } from '../../utils/dateFormatter';

export default {
  data() {
    return {
      currentDate: new Date()
    };
  },
  methods: {
    formatDate(date) {
      const locale = getLocaleString(this.$i18n.locale);
      return new Date(date).toLocaleDateString(locale, {
        weekday: 'long',
        day: 'numeric',
        month: 'long',
        year: 'numeric',
      });
    }
  }
};
</script>