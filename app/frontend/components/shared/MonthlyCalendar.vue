<template>
  <div class="calendar">
    <div class="calendar-header">
      <button @click="prevMonth">&lt;</button>
      <h2>{{ monthYear }}</h2>
      <button @click="nextMonth">&gt;</button>
    </div>
    <table>
      <thead>
        <tr>
          <th v-for="day in daysOfWeek" :key="day">{{ day }}</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="week in weeksInMonth" :key="week[0].date">
          <td v-for="day in week" :key="day.date" :class="{ weekend: isWeekend(day.date) }">
            <div>{{ day.date.getDate() }}</div>
            <ul v-if="hasEvent(day.date)">
              <li v-for="event in getEvents(day.date)" :key="event.id">{{ event.t_event_type }} - {{ event.description }}</li>
            </ul>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script>
export default {
  props: {
    events: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      currentDate: new Date()
    };
  },
  computed: {
    monthYear() {
      return this.currentDate.toLocaleDateString('cs-CZ', { month: 'long', year: 'numeric' });
    },
    daysOfWeek() {
      return ['Po', 'Út', 'St', 'Čt', 'Pá', 'So', 'Ne'];
    },
    weeksInMonth() {
      const year = this.currentDate.getFullYear();
      const month = this.currentDate.getMonth();
      const firstDayOfMonth = new Date(year, month, 1);
      const lastDayOfMonth = new Date(year, month + 1, 0);
      const weeks = [];
      let week = [];
      let day = firstDayOfMonth;

      while (day <= lastDayOfMonth) {
        week.push({ date: new Date(day) });
        if (day.getDay() === 0 || day.getDate() === lastDayOfMonth.getDate()) {
          weeks.push(week);
          week = [];
        }
        day.setDate(day.getDate() + 1);
      }

      return weeks;
    }
  },
  methods: {
    isWeekend(date) {
      const day = date.getDay();
      return day === 0 || day === 6; // Sunday or Saturday
    },
    hasEvent(date) {
      const dateString = date.toISOString().split('T')[0];
      return this.events.some(event => new Date(event.start_time).toISOString().split('T')[0] === dateString);
    },
    getEvents(date) {
      const dateString = date.toISOString().split('T')[0];
      return this.events.filter(event => new Date(event.start_time).toISOString().split('T')[0] === dateString);
    },
    prevMonth() {
      this.currentDate.setMonth(this.currentDate.getMonth() - 1);
      this.$emit('month-changed', this.currentDate);
    },
    nextMonth() {
      this.currentDate.setMonth(this.currentDate.getMonth() + 1);
      this.$emit('month-changed', this.currentDate);
    }
  }
};
</script>

<style scoped>
.calendar {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}
.calendar-header {
  display: flex;
  justify-content: space-between;
  width: 100%;
  max-width: 400px;
  margin-bottom: 10px;
}
table {
  width: 100%;
  max-width: 400px;
  border-collapse: collapse;
}
th, td {
  border: 1px solid #ccc;
  padding: 10px;
  text-align: center;
}
.weekend {
  background-color: #f0f8ff;
}
</style>