<template>
  <div class="time-picker">
    <label for="time">Select Time:</label>
    <select v-model="selectedHour" @change="emitTime" id="hour" class="time-dropdown">
      <option v-for="hour in hours" :key="hour" :value="hour">
        {{ hour }}
      </option>
    </select>
    :
    <select v-model="selectedMinute" @change="emitTime" id="minute" class="time-dropdown">
      <option v-for="minute in minutes" :key="minute" :value="minute">
        {{ minute }}
      </option>
    </select>
  </div>
</template>

<script>
export default {
  name: "TimePicker",
  props: {
    value: {
      type: String,
      default: "00:00",
    },
  },
  data() {
    return {
      hours: Array.from({ length: 24 }, (_, i) => String(i).padStart(2, "0")),
      minutes: Array.from({ length: 12 }, (_, i) => String(i * 5).padStart(2, "0")),
      selectedHour: "00",
      selectedMinute: "00",
    };
  },
  watch: {
    value: {
      immediate: true,
      handler(newValue) {
        const [hour, minute] = newValue.split(":");
        this.selectedHour = hour;
        this.selectedMinute = minute;
      },
    },
  },
  methods: {
    emitTime() {
      const time = `${this.selectedHour}:${this.selectedMinute}`;
      this.$emit("input", time);
    },
  },
};
</script>

<style scoped>
.time-picker {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.time-dropdown {
  padding: 0.5rem;
  font-size: 1rem;
}
</style>