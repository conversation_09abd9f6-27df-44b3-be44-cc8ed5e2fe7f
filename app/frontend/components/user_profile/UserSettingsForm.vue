<template>
  <div class="grid lg:grid-cols-3 gap-6 p-4 md:p-0">

    <div class="card">
      <h3 class="form-title px-4 pt-4 pb-2 border-b border-gray-100">{{ $t('user_settings.profile_title', 'Profil') }}</h3>
      <div class="card-content">
          <form @submit.prevent="saveProfile">
            <div class="form-group">
              <label for="email" class="form-label">{{ $t('user_settings.email', 'E-mail') }}</label>
              <input type="email" id="email" :value="email" disabled class="block w-full py-2 px-3 leading-5 text-sm text-gray-500 bg-gray-50 cursor-not-allowed border border-gray-200 rounded-md shadow-sm" />
            </div>
            <div class="form-group">
              <label for="title_prefix" class="form-label">{{ $t('user_settings.title_prefix', 'Titul před j<PERSON>') }}</label>
              <input type="text" id="title_prefix" v-model="form.title_prefix" :placeholder="$t('user_settings.title_prefix_placeholder', 'Ing.')" class="form-input" />
            </div>
            <div class="form-group">
              <label for="first_name" class="form-label">{{ $t('first_name', 'Jméno') }}</label>
              <input type="text" id="first_name" v-model="form.first_name" :placeholder="$t('user_settings.first_name_placeholder', 'Jan')" required class="form-input" />
            </div>
            <div class="form-group">
              <label for="last_name" class="form-label">{{ $t('last_name', 'Příjmení') }}</label>
              <input type="text" id="last_name" v-model="form.last_name" :placeholder="$t('user_settings.last_name_placeholder', 'Novák')" required class="form-input" />
            </div>
            <div class="form-group">
              <label for="title_suffix" class="form-label">{{ $t('user_settings.title_suffix', 'Titul za jménem') }}</label>
              <input type="text" id="title_suffix" v-model="form.title_suffix" :placeholder="$t('user_settings.title_suffix_placeholder', 'Ph.D.')" class="form-input" />
            </div>
            <div class="form-group">
              <div class="form-actions flex flex-row gap-2 justify-end">
                <button type="submit" class="btn btn-primary" :disabled="submitting.profile">
                  {{ submitting.profile ? $t('saving', 'Ukládání...') : $t('user_settings.save_profile_button', 'Uložit profil') }}
                </button>
              </div>
            </div>
          </form>
      </div>
    </div>

    <div class="card">
      <h3 class="form-title px-4 pt-4 pb-2 border-b border-gray-100">
        {{ $t('user_settings.country_settings_title', 'Nastavení země') }}
      </h3>
      <div class="p-4">
        <div class="flex items-start gap-2 p-1 rounded text-sm text-gray-600">
          <div class="flex items-center justify-center w-6 h-6">
            <Info size="16" />
          </div>
          <p>
            {{ $t('user_settings.help_country', 'Podle nastavené země vám vybereme odpovídajíci svátky.') }}
          </p>
        </div>
      </div>
      <div class="card-content">
        <label class="form-label">{{ $t('user_settings.country', 'Krajina') }}</label>

        <CountrySelector />
        <div class="form-group mt-4">
          <label class="form-label">{{ $t('user_settings.language', 'Jazyk') }}</label>
          <select v-model="selectedLanguage" @change="changeLanguage" class="form-select">
            <option v-for="locale in availableLocales" :key="locale.code" :value="locale.code">
              {{ locale.name }}
            </option>
          </select>
        </div>
      </div>
    </div>

    <div class="card">
      <h3 class="form-title px-4 pt-4 pb-2 border-b border-gray-100">
        {{ $t('user_settings.work_time', 'Nastavení používateľa') }}
      </h3>

      <div class="p-4">
        <div class="flex items-start gap-2 p-1 rounded text-sm text-gray-600">
          <div class="flex items-center justify-center w-6 h-6">
            <Info size="16" />
          </div>
          <p>
            {{ $t('user_settings.help_worktime', 'Zde si upravíte osobní nastavení, které bude platit pro všechny vaše kontrakty.') }}
          </p>
        </div>
      </div>

      <div class="card-content">
        <form @submit.prevent="saveWorkTime">
          <div class="form-group">
            <label for="start_time" class="form-label">{{ $t('user_settings.usual_start_time', 'Obvyklý začátek práce') }}</label>
            <input type="text" id="start_time" v-model="form.start_time_display" :placeholder="$t('user_settings.time_placeholder_1', '8:00')" required class="form-input" />
          </div>
          <div class="form-group">
            <label for="end_time" class="form-label">{{ $t('user_settings.usual_end_time', 'Obvyklý konec práce') }}</label>
            <input type="text" id="end_time" v-model="form.end_time_display" :placeholder="$t('user_settings.time_placeholder_2', '16:00')" required class="form-input" />
          </div>
          <div class="form-group">
            <label for="break_start" class="form-label">{{ $t('user_settings.usual_break_start', 'Obvyklý začátek přestávky') }}</label>
            <input type="text" id="break_start" v-model="form.break_start_display" :placeholder="$t('user_settings.time_placeholder_3', '12:00')" required class="form-input" />
          </div>
          <div class="form-group">
             <div class="form-actions flex flex-row gap-2 justify-end">
                <button type="submit" class="btn btn-primary" :disabled="submitting.workTime">
                  {{ submitting.workTime ? $t('saving', 'Ukládání...') : $t('user_settings.save_settings_button', 'Uložit nastavení') }}
                </button>
                <button type="button" @click="$emit('cancel')" class="btn btn-outline">{{ $t('cancel', 'Zrušit') }}</button>
             </div>
          </div>
        </form>
      </div>
    </div>

    <div class="card">
      <h3 class="form-title px-4 pt-4 pb-2 border-b border-gray-100">{{ $t('user_settings.change_password_title', 'Změna hesla') }}</h3>
      <div class="card-content">
        <form @submit.prevent="savePassword">
          <div class="form-group">
            <label for="current_password" class="form-label">{{ $t('user_settings.current_password', 'Současné heslo') }}</label>
            <input type="password" id="current_password" v-model="form.current_password" required class="form-input" autocomplete="current-password" />
          </div>
          <div class="form-group">
            <label for="password" class="form-label">{{ $t('user_settings.new_password', 'Nové heslo') }}</label>
            <input type="password" id="password" v-model="form.password" required class="form-input" autocomplete="new-password" />
            <i class="form-hint">{{ $t('user_settings.min_6_chars', '(Minimálně 6 znaků)') }}</i>
          </div>
          <div class="form-group">
            <label for="password_confirmation" class="form-label">{{ $t('user_settings.confirm_new_password', 'Potvrzení nového hesla') }}</label>
            <input type="password" id="password_confirmation" v-model="form.password_confirmation" required class="form-input" autocomplete="new-password" />
          </div>
          <div class="form-group">
            <div class="form-actions flex flex-row gap-2 justify-end">
              <button type="submit" class="btn btn-primary" :disabled="submitting.password">
                {{ submitting.password ? $t('saving', 'Ukládání...') : $t('user_settings.change_password_button', 'Změnit heslo') }}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>

  </div>
</template>

<script>
import axios from 'axios';
import { Info } from 'lucide-vue-next';
import { mapState } from 'vuex';
import CountrySelector from '../CountrySelector.vue';
import sendFlashMessage from '../../utils/flashMessage.js';

export default {
  components: {
    Info,
    CountrySelector
  },
  computed: {
    ...mapState('userStore', ['email'])
  },
  data() {
    return {
      form: {
        start_time_display: '',
        end_time_display: '',
        break_start_display: '',
        email: '',
        first_name: '',
        last_name: '',
        title_prefix: '',
        title_suffix: '',
        current_password: '',
        password: '',
        password_confirmation: ''
      },
      submitting: {
        workTime: false,
        profile: false,
        password: false
      },
      currentLocale: this.getCurrentLocale(),
      selectedLanguage: this.getCurrentLocale(),
      availableLocales: [
        { code: 'cs', name: 'Čeština' },
        { code: 'sk', name: 'Slovenčina' }
      ]
    };
  },
  mounted() {
    this.fetchData();
    this.fetchLanguageSetting();
    // Fetch user data to ensure email is loaded from store
    this.$store.dispatch('userStore/fetchUserData');
  },
  methods: {
    async fetchData() {
      try {
        const settingsResponse = await axios.get('/user_settings');
        console.log('Settings API Response:', settingsResponse.data);
        const settings = settingsResponse.data.user_setting || settingsResponse.data;
        
        if (settings.start_time) {
          this.form.start_time_display = this.formatTime(settings.start_time);
        }
        if (settings.end_time) {
          this.form.end_time_display = this.formatTime(settings.end_time);
        }
        if (settings.break_start) {
          this.form.break_start_display = this.formatTime(settings.break_start);
        }
        console.log('Formatted work time values:', this.form);
      } catch (error) {
        console.error('Error fetching user settings:', error);
      }

      try {
        const profileResponse = await axios.get('/user_profile'); 
        console.log('Profile API Response:', profileResponse.data);
        const profile = profileResponse.data.user_profile || profileResponse.data; 
        
        this.form.email = profile.email || '';
        this.form.first_name = profile.first_name || '';
        this.form.last_name = profile.last_name || '';
        this.form.title_prefix = profile.title_prefix || '';
        this.form.title_suffix = profile.title_suffix || '';
        console.log('Loaded profile values:', this.form);
      } catch (error) {
        console.error('Error fetching user profile:', error);
         if (error.response && error.response.status === 404) {
           console.warn('User profile endpoint (/user_profile) not found. Needs backend implementation.');
         }
      }
    },

    async fetchLanguageSetting() {
      try {
        const response = await axios.get('/user_settings');
        const settings = response.data.user_setting || response.data;
        if (settings.language_code) {
          this.selectedLanguage = settings.language_code;
        }
      } catch (error) {
        console.error('Error fetching language setting:', error);
      }
    },
    
    formatTime(timeStr) {
      if (!timeStr) return '';
      
      try {
        if (timeStr.includes('T') || timeStr.includes('-') || timeStr.includes('+')) {
          const date = new Date(timeStr);
          if (!isNaN(date.getTime())) {
            const hours = date.getHours().toString().padStart(2, '0');
            const minutes = date.getMinutes().toString().padStart(2, '0');
            return `${hours}:${minutes}`;
          }
        }
        
        if (timeStr.includes(':')) {
          const parts = timeStr.split(':');
          if (parts.length >= 2) {
            const hours = parseInt(parts[0], 10).toString().padStart(2, '0');
            const minutes = parseInt(parts[1], 10).toString().padStart(2, '0');
            if (!isNaN(hours) && !isNaN(minutes)) {
              return `${hours}:${minutes}`;
            }
          }
        }
        
        return timeStr;
      } catch (e) {
        console.error('Error formatting time:', e);
        return timeStr;
      }
    },
    
    parseTimeInput(value) {
      if (!value) return null;
      
      const cleaned = value.replace(/[^\d.,]/g, '');
      let hours, minutes;
      
      if (value.includes(':')) {
        [hours, minutes] = value.split(':').map(part => parseInt(part, 10));
      } else if (cleaned.includes('.') || cleaned.includes(',')) {
        const parts = cleaned.split(/[.,]/);
        hours = parseInt(parts[0], 10);
        minutes = parseInt(parts[1], 10);
      } else {
        if (cleaned.length === 3) {
          hours = parseInt(cleaned[0], 10);
          minutes = parseInt(cleaned.slice(1), 10);
        } else if (cleaned.length === 4) {
          hours = parseInt(cleaned.slice(0, 2), 10);
          minutes = parseInt(cleaned.slice(2), 10);
        } else {
          hours = parseInt(cleaned, 10);
          minutes = 0;
        }
      }
      
      if (isNaN(hours) || isNaN(minutes) || hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
        return null;
      }
      
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
    },
    
    async saveWorkTime() {
      this.submitting.workTime = true;
      try {
        const startTime = this.parseTimeInput(this.form.start_time_display);
        const endTime = this.parseTimeInput(this.form.end_time_display);
        const breakStart = this.parseTimeInput(this.form.break_start_display);

        if (!startTime || !endTime || !breakStart) {
          console.error('Invalid time format');
          alert(this.$t('user_settings.alert_invalid_time_format', 'Neplatný formát času pro pracovní dobu. Použijte např. 8:00, 8.00, 0800.'));
          this.submitting.workTime = false; 
          return;
        }

        const formData = {
          user_setting: {
            start_time: startTime,
            end_time: endTime,
            break_start: breakStart
          }
        };

        console.log('Submitting Work Time data:', formData);
        const response = await axios.put('/user_settings', formData);
        this.$emit('saved', { type: 'workTime', data: response.data });
        alert(this.$t('user_settings.alert_work_time_saved', 'Nastavení pracovní doby bylo uloženo.')); 
      } catch (error) {
        console.error('Error updating work time settings:', error);
        alert(this.$t('user_settings.alert_error_saving_work_time', 'Chyba při ukládání pracovní doby.')); 
      } finally {
        this.submitting.workTime = false;
      }
    },

    async saveProfile() {
      this.submitting.profile = true;
      try {
        if (!this.form.first_name || !this.form.last_name) {
           alert(this.$t('user_settings.alert_first_last_name_required', 'Jméno a příjmení jsou povinné.'));
           this.submitting.profile = false;
           return;
        }

        const formData = {
          user_profile: {
            first_name: this.form.first_name,
            last_name: this.form.last_name,
            title_prefix: this.form.title_prefix,
            title_suffix: this.form.title_suffix
          }
        };

        console.log('Submitting Profile data:', formData);
        const response = await axios.put('/user_profile', formData); 
        this.$emit('saved', { type: 'profile', data: response.data });
        alert(this.$t('user_settings.alert_profile_updated', 'Profil byl úspěšně aktualizován.')); 
      } catch (error) {
        console.error('Error updating profile:', error);
        let errorMessage = this.$t('user_settings.alert_error_saving_profile', 'Chyba při ukládání profilu.');
        if (error.response && error.response.data && error.response.data.errors) {
           errorMessage += '\n' + error.response.data.errors.join('\n');
        }
        alert(errorMessage); 
      } finally {
        this.submitting.profile = false;
      }
    },

    async savePassword() {
      this.submitting.password = true;
      try {
         // Basic validation
        if (!this.form.current_password || !this.form.password || !this.form.password_confirmation) {
           alert(this.$t('user_settings.alert_all_password_fields_required', 'Všechna pole pro změnu hesla jsou povinná.'));
           this.submitting.password = false;
           return;
        }
        if (this.form.password !== this.form.password_confirmation) {
          alert(this.$t('user_settings.alert_passwords_do_not_match', 'Nové heslo a potvrzení se neshodují.'));
           this.submitting.password = false;
           return;
        }
        if (this.form.password.length < 6) { 
           alert(this.$t('user_settings.alert_password_too_short', 'Nové heslo musí mít alespoň 6 znaků.'));
           this.submitting.password = false;
           return;
        }


        const formData = {
          current_password: this.form.current_password,
          password: this.form.password,
          password_confirmation: this.form.password_confirmation
        };

        const response = await axios.put('/api/v1/auth/change_password', formData); 
        this.$emit('saved', { type: 'password', data: response.data });
         alert(this.$t('user_settings.alert_password_changed_success', 'Heslo bylo úspěšně změněno.')); 
        
         this.form.current_password = '';
         this.form.password = '';
         this.form.password_confirmation = '';
      } catch (error) {
        console.error('Error updating password:', error);
        // Use backend error message directly as per CLAUDE.md guidelines
        const errorMessage = error.response?.data?.error || this.$t('user_settings.alert_error_changing_password', 'Chyba při změně hesla.');
        alert(errorMessage);
      } finally {
        this.submitting.password = false;
      }
    },

    getCurrentLocale() {
      const pathSegments = window.location.pathname.split('/').filter(Boolean);
      const availableLocales = ['cs', 'sk']; 

      if (pathSegments.length > 0 && availableLocales.includes(pathSegments[0])) {
        return pathSegments[0];
      }

      const cookies = document.cookie.split(';');
      for (let cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'locale' && availableLocales.includes(value)) {
          return value;
        }
      }
      
      return 'cs'; 
    },

    changeLanguage() {
      const locale = this.selectedLanguage;
      document.cookie = `locale=${locale};path=/;max-age=31536000;SameSite=Lax`;

      axios.put('/user_settings', {
        user_setting: { language_code: locale }
      })
      .then(() => {
        const { origin, pathname, search, hash } = window.location;
        const pathWithoutLocale = pathname.replace(/^\/(cs|sk)/, '');
        const newPath = `/${locale}${pathWithoutLocale || '/'}`;
        window.location.href = origin + newPath + search + hash;
        sendFlashMessage(this.$t('user_settings.language_updated', 'Nastavení jazyka bylo aktualizováno'), 'success');
      })
      .catch(error => {
        console.error('Error updating language setting:', error);
        sendFlashMessage(this.$t('user_settings.error_updating_language', 'Chyba pri aktualizování jazyka'), 'error');
      });
    },
  }
};
</script>
