<template>
  <div class="service-contract-form">
    <div v-if="loading" class="loading-state">
      <Clock :size="20" class="animate-spin text-gray-500" />
      <span class="text-sm text-gray-500">{{ $t('loading', 'Načítání...') }}</span>
    </div>

    <form v-else @submit.prevent="submitForm" class="space-y-4">
      <!-- Title -->
      <div class="form-group">
        <label for="title" class="form-label required">
          {{ $t('works.service_contract_title', 'Název zakázky') }}
        </label>
        <input
          id="title"
          v-model="form.title"
          type="text"
          class="form-input"
          :class="{ 'error': errors.title }"
          :placeholder="$t('works.service_contract_title_placeholder', 'Zadejte název zakázky')"
          required
        />
        <div v-if="errors.title" class="error-message">{{ errors.title }}</div>
      </div>

      <!-- Description -->
      <div class="form-group">
        <label for="description" class="form-label">
          {{ $t('works.description', 'Popis') }}
        </label>
        <textarea
          id="description"
          v-model="form.description"
          class="form-textarea"
          :class="{ 'error': errors.description }"
          rows="3"
          :placeholder="$t('works.description_placeholder', 'Volitelný popis zakázky')"
        ></textarea>
        <div v-if="errors.description" class="error-message">{{ errors.description }}</div>
      </div>

      <!-- Status -->
      <div class="form-group">
        <label for="status" class="form-label">
          {{ $t('works.status', 'Stav') }}
        </label>
        <select
          id="status"
          v-model="form.status"
          class="form-select"
          :class="{ 'error': errors.status }"
        >
          <option value="unprocessed">{{ $t('works.status.unprocessed', 'Nezpracována') }}</option>
          <option value="scheduled">{{ $t('works.status.scheduled', 'Naplánována') }}</option>
          <option value="in_progress">{{ $t('works.status.in_progress', 'Probíhá') }}</option>
          <option value="completed">{{ $t('works.status.completed', 'Dokončena') }}</option>
          <option value="cancelled">{{ $t('works.status.cancelled', 'Zrušena') }}</option>
        </select>
        <div v-if="errors.status" class="error-message">{{ errors.status }}</div>
      </div>

      <!-- Client (if available) -->
      <div class="form-group" v-if="clients && clients.length > 0">
        <label for="client_id" class="form-label">
          {{ $t('works.client', 'Klient') }}
        </label>
        <select
          id="client_id"
          v-model="form.client_id"
          class="form-select"
          :class="{ 'error': errors.client_id }"
        >
          <option value="">{{ $t('works.no_client', 'Bez klienta') }}</option>
          <option v-for="client in clients" :key="client.id" :value="client.id">
            {{ client.name }}
          </option>
        </select>
        <div v-if="errors.client_id" class="error-message">{{ errors.client_id }}</div>
      </div>

      <!-- Error Messages -->
      <div v-if="generalError" class="error-banner">
        {{ generalError }}
      </div>

      <!-- Form Actions -->
      <div class="form-actions">
        <button
          type="button"
          @click="$emit('cancel')"
          class="btn btn-outline"
          :disabled="submitting"
        >
          {{ $t('cancel', 'Zrušit') }}
        </button>
        <button
          type="submit"
          class="btn btn-primary"
          :disabled="submitting || !isFormValid"
        >
          <Clock v-if="submitting" :size="16" class="animate-spin" />
          {{ submitting ? $t('saving', 'Ukládání...') : (contract ? $t('update', 'Aktualizovat') : $t('create', 'Vytvořit')) }}
        </button>
      </div>
    </form>
  </div>
</template>

<script>
import axios from 'axios';
import { Clock } from 'lucide-vue-next';

export default {
  name: 'ServiceContractForm',
  components: {
    Clock
  },
  props: {
    contract: {
      type: Object,
      default: null
    }
  },
  emits: ['cancel', 'saved'],
  data() {
    return {
      loading: false,
      submitting: false,
      clients: [],
      form: {
        title: '',
        description: '',
        status: 'unprocessed',
        client_id: ''
      },
      errors: {},
      generalError: null
    };
  },
  computed: {
    isFormValid() {
      return this.form.title.trim().length > 0;
    }
  },
  mounted() {
    this.initializeForm();
    this.fetchClients();
  },
  methods: {
    initializeForm() {
      if (this.contract) {
        this.form = {
          title: this.contract.title || '',
          description: this.contract.description || '',
          status: this.contract.status || 'unprocessed',
          client_id: this.contract.client_id || ''
        };
      } else {
        // Generate default title for new contracts
        this.form.title = this.generateDefaultTitle();
      }
    },

    generateDefaultTitle() {
      const today = new Date();
      const year = today.getFullYear();
      const month = String(today.getMonth() + 1).padStart(2, '0');
      const companyId = this.$store?.state?.company?.id || 1;
      const timestamp = Date.now();
      return `${year}-${month}-${companyId}-${timestamp}`;
    },

    async fetchClients() {
      try {
        // Assuming there's a clients API endpoint
        // const response = await axios.get('/api/v1/clients');
        // this.clients = response.data;
        this.clients = []; // For now, no clients
      } catch (error) {
        console.error('Error fetching clients:', error);
        this.clients = [];
      }
    },

    async submitForm() {
      this.submitting = true;
      this.errors = {};
      this.generalError = null;

      try {
        const url = this.contract 
          ? `/api/v1/service_contracts/${this.contract.id}`
          : '/api/v1/service_contracts';
        
        const method = this.contract ? 'patch' : 'post';
        
        const response = await axios[method](url, {
          service_contract: this.form
        });

        this.$emit('saved', response.data);
      } catch (error) {
        if (error.response?.data?.errors) {
          this.errors = error.response.data.errors;
        } else {
          this.generalError = error.response?.data?.error || 
            this.$t('works.save_error', 'Chyba při ukládání zakázky');
        }
      } finally {
        this.submitting = false;
      }
    }
  }
};
</script>

<style scoped>
.service-contract-form {
  max-width: 100%;
}

.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 2rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.form-label.required::after {
  content: ' *';
  color: #ef4444;
}

.form-input,
.form-textarea,
.form-select {
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.form-input.error,
.form-textarea.error,
.form-select.error {
  border-color: #ef4444;
}

.form-textarea {
  resize: vertical;
  min-height: 4rem;
}

.error-message {
  font-size: 0.75rem;
  color: #ef4444;
  margin-top: 0.25rem;
}

.error-banner {
  padding: 0.75rem;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 0.375rem;
  color: #dc2626;
  font-size: 0.875rem;
}

.form-actions {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.375rem;
  transition: all 0.2s;
  cursor: pointer;
  border: none;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #2563eb;
}

.btn-outline {
  border: 1px solid #d1d5db;
  color: #374151;
  background-color: white;
}

.btn-outline:hover:not(:disabled) {
  background-color: #f9fafb;
}
</style>
