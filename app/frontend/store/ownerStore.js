import axios from 'axios';

export default {
  namespaced: true,
  state: {
    employees: [],
    events: []
  },
  getters: {
    employees: state => state.employees,
    events: state => state.events
  },
  mutations: {
    setEmployees(state, employees) {
      state.employees = employees;
    },
    setEvents(state, events) {
      state.events = events;
    },
    EVENT_CONFIRMED(state, eventId) {
      state.events = state.events.filter(e => e.id !== eventId);
    },
    UPDATE_EMPLOYEE_STATUS(state, { employeeId, updates }) {
      console.log('🔍 [Mutation] Looking for employee ID:', employeeId);
      console.log('🔍 [Mutation] Available employees:', state.employees.map(e => ({id: e.id, name: e.name, email: e.email})));
      console.log('🔍 [Mutation] Employee IDs only:', state.employees.map(e => e.id));
      const employee = state.employees.find(emp => emp.id === String(employeeId));
      console.log('🔍 [Mutation] Found employee:', employee ? employee.name : 'NOT FOUND');
      if (employee) {
        // Add animation indicator
        updates.justUpdated = true;
        Object.assign(employee, updates);
        
        // Force reactivity by replacing array
        state.employees = [...state.employees];
        
        // Remove animation indicator after 3 seconds
        setTimeout(() => {
          if (employee.justUpdated) {
            employee.justUpdated = false;
            state.employees = [...state.employees];
          }
        }, 3000);
      }
    },
  },
  actions: {
    async fetchEmployees({ commit }) {
      try {
        const response = await axios.get('/api/v1/employees');
        commit('setEmployees', response.data);
      } catch (error) {
        console.error('Error fetching employees:', error.response ? error.response.data : error.message);
      }
    },
    async fetchEvents({ commit }, status = 'pending') {
      try {
        const response = await axios.get('/api/v1/events', { params: {status} });
        commit('setEvents', response.data);
      } catch (error) {
        console.error('Error fetching pending events:', error.response ? error.response.data : error.message);
      }
    },
    async confirmEvent({ commit }, eventId) {
      try {
        const response = await axios.post(`/api/v1/events/${eventId}/confirm`);
        if (response.data.success) {
          commit('EVENT_CONFIRMED', eventId);
        }
        return response.data;
      } catch (error) {
        console.error('Error confirming event:', error);
        throw error;
      }
    },
    
    // Real-time update actions
    handleTeamStatusUpdate({ commit, dispatch }, data) {
      console.log('🔄 [Store] Processing team status update:', data);
      // Handle different event types
      switch (data.event_type) {
        case 'daily_log_started':
          commit('UPDATE_EMPLOYEE_STATUS', {
            employeeId: data.employee_id,
            updates: { 
              working: true,
              dailyLogStartedAt: data.daily_log_started_at
            }
          });
          break;
        
        case 'daily_log_ended':
          commit('UPDATE_EMPLOYEE_STATUS', {
            employeeId: data.employee_id,
            updates: { 
              working: false, 
              current_work: null,
              dailyLogEndedAt: data.daily_log_ended_at
            }
          });
          break;
        
        case 'work_activity_started':
          console.log('🏠 [Store] Updating employee status for work_activity_started');
          console.log('🏠 [Store] Employee ID:', data.employee_id);
          console.log('🏠 [Store] Current work:', data.current_work);
          commit('UPDATE_EMPLOYEE_STATUS', {
            employeeId: data.employee_id,
            updates: {
              working: true,
              current_work: data.current_work,
              activityStartedAt: data.activity_started_at
            }
          });
          break;
        
        case 'work_activity_ended':
          commit('UPDATE_EMPLOYEE_STATUS', {
            employeeId: data.employee_id,
            updates: { working: false, current_work: null }
          });
          break;
      }
    }
  }
};