class FeedbackMailer < ApplicationMailer
  default to: '<EMAIL>'

  # Sends feedback summary to central inbox using Resend delivery
  def submitted(feedback_id)
    @feedback = Feedback.find(feedback_id)
    @company = @feedback.company
    @user = @feedback.user

    subject = I18n.t('mailer.feedback_mailer.submitted.subject', default: 'Nová zpětná vazba z aplikace')

    mail(
      to: '<EMAIL>',
      subject: subject
    )
  end
end

