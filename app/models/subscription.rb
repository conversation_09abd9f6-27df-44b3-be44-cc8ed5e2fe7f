class Subscription < ApplicationRecord
  acts_as_tenant(:company)
  belongs_to :company
  belongs_to :plan
  belongs_to :trial_activated_by, class_name: 'User', optional: true

  # Attributes: user_id, plan_id, start_date, expire_date, status (e.g., 'active', 'expired')

  validates :start_date, presence: true
  validates :expire_date, presence: true

  # Scope to fetch active subscriptions
  scope :active, -> { where('start_date <= ? AND expire_date >= ? AND status = ?', Date.today, Date.today, 'active') }
  scope :trials, -> { where(is_trial: true) }

  # Check if this subscription is active
  def active?
    status == 'active' && start_date <= Date.today && expire_date >= Date.today
  end

  def self.can_activate_trial?(user, company)
    !company.subscriptions.trials.exists?(trial_activated_by: user)
  end

  def self.activate_trial(user, company)
    return false unless can_activate_trial?(user, company)

    plan = Plan.find_by(name: 'plus')
    return false unless plan

    create!(
      company: company,
      plan: plan,
      start_date: Date.today,
      expire_date: Date.today + 30.days,
      status: 'active',
      is_trial: true,
      trial_activated_by: user
    )
  end
  
  # Check if subscription is Plus tier or higher
  def plus_or_higher?
    return false unless plan
    %w[plus premium].include?(plan.name.downcase)
  end

  # Trial end date - only for trial subscriptions
  def trial_end
    return nil unless is_trial?
    expire_date
  end

  # Current period start date
  def current_period_start
    start_date
  end

  # Current period end date
  def current_period_end
    expire_date
  end

  # Check if this is a trial subscription
  def is_trial?
    is_trial
  end
end