# ABOUTME: AdminPolicy handles access control for admin interface functionality
# ABOUTME: Implements multiple security layers to ensure only authorized users can access admin features
class AdminPolicy < ApplicationPolicy
  def access?
    user&.admin_user?
  end
  
  def manage_companies?
    access?
  end
  
  def manage_subscriptions?
    access?
  end
  
  def view_all_companies?
    access?
  end
  
  def view_all_subscriptions?
    access?
  end
end