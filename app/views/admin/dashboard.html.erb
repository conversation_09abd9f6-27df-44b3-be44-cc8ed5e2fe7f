<% content_for :page_title, t('admin.dashboard.title', default: 'Admin Dashboard') %>
<% content_for :page_subtitle, t('admin.dashboard.subtitle', default: 'System overview and management') %>

<!-- Dashboard Statistics -->
<div class="stats-grid">
  <div class="stat-card">
    <div class="stat-value"><%= @stats[:total_companies] %></div>
    <div class="stat-label"><%= t('admin.dashboard.total_companies', default: 'Total Companies') %></div>
  </div>
  
  <div class="stat-card">
    <div class="stat-value"><%= @stats[:total_users] %></div>
    <div class="stat-label"><%= t('admin.dashboard.total_users', default: 'Total Users') %></div>
  </div>
  
  <div class="stat-card">
    <div class="stat-value"><%= @stats[:active_subscriptions] %></div>
    <div class="stat-label"><%= t('admin.dashboard.active_subscriptions', default: 'Active Subscriptions') %></div>
  </div>
  
  <div class="stat-card">
    <div class="stat-value"><%= @stats[:trial_subscriptions] %></div>
    <div class="stat-label"><%= t('admin.dashboard.trial_subscriptions', default: 'Trial Subscriptions') %></div>
  </div>
  
  <div class="stat-card">
    <div class="stat-value"><%= @stats[:total_contracts] %></div>
    <div class="stat-label"><%= t('admin.dashboard.total_contracts', default: 'Total Contracts') %></div>
  </div>
  
  <div class="stat-card">
    <div class="stat-value"><%= @stats[:recent_registrations] %></div>
    <div class="stat-label"><%= t('admin.dashboard.recent_registrations', default: 'New Users (7 days)') %></div>
  </div>
</div>

<!-- Quick Actions -->
<div class="actions-grid">
  <%= link_to admin_companies_path(locale: I18n.locale), class: "stat-card action-card" do %>
    <div class="action-card-content">
      <span class="action-card-icon">🏢</span>
      <div>
        <h3 class="action-card-title">
          <%= t('admin.dashboard.manage_companies', default: 'Manage Companies') %>
        </h3>
        <p class="action-card-description">
          <%= t('admin.dashboard.view_company_details', default: 'View company details, owners, and employees') %>
        </p>
      </div>
    </div>
  <% end %>
  
  <%= link_to admin_subscriptions_path(locale: I18n.locale), class: "stat-card action-card" do %>
    <div class="action-card-content">
      <span class="action-card-icon">💳</span>
      <div>
        <h3 class="action-card-title">
          <%= t('admin.dashboard.manage_subscriptions', default: 'Manage Subscriptions') %>
        </h3>
        <p class="action-card-description">
          <%= t('admin.dashboard.view_subscription_details', default: 'View and manage subscription plans and billing') %>
        </p>
      </div>
    </div>
  <% end %>
</div>

<!-- Recent Companies -->
<div class="data-table">
  <div class="table-header">
    <h3><%= t('admin.dashboard.recent_companies', default: 'Recent Companies') %></h3>
  </div>
  <div class="table-content">
    <% if @recent_companies.any? %>
      <% @recent_companies.each do |company_data| %>
        <div class="table-row">
          <div>
            <div style="font-weight: 600; margin-bottom: 0.25rem;">
              <%= company_data[:company].name %>
            </div>
            <div style="font-size: 0.875rem; color: #6b7280;">
              <% if company_data[:owner] %>
                Owner: <%= company_data[:owner].email %>
              <% else %>
                No owner assigned
              <% end %>
            </div>
            <div style="font-size: 0.875rem; color: #6b7280;">
              Employees: <%= company_data[:employee_count] %>
            </div>
          </div>
          <div style="text-align: right;">
            <div style="font-size: 0.875rem; color: #6b7280; margin-bottom: 0.25rem;">
              Created: <%= company_data[:company].created_at.strftime("%b %d, %Y") %>
            </div>
            <% if company_data[:plan] %>
              <span style="background: #dbeafe; color: #1e40af; padding: 0.25rem 0.5rem; border-radius: 0.25rem; font-size: 0.75rem;">
                <%= company_data[:plan].name.titleize %>
              </span>
            <% else %>
              <span style="background: #f3f4f6; color: #6b7280; padding: 0.25rem 0.5rem; border-radius: 0.25rem; font-size: 0.75rem;">
                Free
              </span>
            <% end %>
          </div>
        </div>
      <% end %>
    <% else %>
      <div class="table-row">
        <div style="text-align: center; color: #6b7280; font-style: italic;">
          No companies found
        </div>
      </div>
    <% end %>
  </div>
</div>

<script>
  // Add hover effects to action cards
  document.querySelectorAll('.stat-card[href]').forEach(card => {
    card.addEventListener('mouseenter', () => {
      card.style.transform = 'translateY(-2px)';
      card.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
    });
    
    card.addEventListener('mouseleave', () => {
      card.style.transform = 'translateY(0)';
      card.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
    });
  });
  
  // Auto-refresh system time
  setInterval(() => {
    const now = new Date();
    const timeString = now.toLocaleTimeString();
    const timeElement = document.querySelector('.admin-topbar span');
    if (timeElement) {
      timeElement.innerHTML = '<%= t('admin.system_time', default: 'System Time') %>: ' + timeString;
    }
  }, 1000);
</script>
