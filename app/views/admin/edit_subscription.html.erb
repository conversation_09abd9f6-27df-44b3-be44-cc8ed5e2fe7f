<% content_for :page_title, t('admin.subscriptions.edit_subscription', default: 'Edit Subscription') %>
<% content_for :page_subtitle, t('admin.subscriptions.modify_subscription', default: 'Modify subscription details') %>

<!-- Back to Companies -->
<div style="margin-bottom: 2rem;">
  <%= link_to admin_companies_path(locale: I18n.locale), class: "btn btn-secondary" do %>
    ← <%= t('admin.companies.back_to_companies', default: 'Back to Companies') %>
  <% end %>
</div>

<!-- Subscription Form -->
<div class="data-table" style="max-width: 600px;">
  <div class="table-header">
    <h3><%= t('admin.subscriptions.subscription_details', default: 'Subscription Details') %></h3>
  </div>
  
  <div style="padding: 2rem;">
    <%= form_with model: @subscription, url: update_admin_subscription_path(@subscription, locale: I18n.locale), method: :patch, local: true do |form| %>
      <% if @subscription.errors.any? %>
        <div class="alert alert-error" style="margin-bottom: 1.5rem;">
          <h4><%= pluralize(@subscription.errors.count, "error") %> prohibited this subscription from being saved:</h4>
          <ul style="margin-top: 0.5rem;">
            <% @subscription.errors.full_messages.each do |message| %>
              <li><%= message %></li>
            <% end %>
          </ul>
        </div>
      <% end %>

      <!-- Company (Read-only) -->
      <div style="margin-bottom: 1.5rem;">
        <%= form.label :company_id, t('admin.subscriptions.company', default: 'Company'), 
                       style: "display: block; font-weight: 600; margin-bottom: 0.5rem;" %>
        <div style="padding: 0.75rem; background: #f9fafb; border: 1px solid #e5e7eb; border-radius: 0.375rem;">
          <strong><%= @company.name %></strong> (ID: <%= @company.id %>)
        </div>
        <%= form.hidden_field :company_id %>
      </div>

      <!-- Plan Selection -->
      <div style="margin-bottom: 1.5rem;">
        <%= form.label :plan_id, t('admin.subscriptions.plan', default: 'Plan'), 
                       style: "display: block; font-weight: 600; margin-bottom: 0.5rem;" %>
        <%= form.select :plan_id, 
                       options_from_collection_for_select(@plans, :id, :name, @subscription.plan_id),
                       { prompt: 'Select a plan...' },
                       { style: "width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.375rem;" } %>
      </div>

      <!-- Start Date -->
      <div style="margin-bottom: 1.5rem;">
        <%= form.label :start_date, t('admin.subscriptions.start_date', default: 'Start Date'), 
                       style: "display: block; font-weight: 600; margin-bottom: 0.5rem;" %>
        <%= form.date_field :start_date, 
                           style: "width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.375rem;" %>
      </div>

      <!-- Expire Date -->
      <div style="margin-bottom: 1.5rem;">
        <%= form.label :expire_date, t('admin.subscriptions.expire_date', default: 'Expire Date'), 
                       style: "display: block; font-weight: 600; margin-bottom: 0.5rem;" %>
        <%= form.date_field :expire_date, 
                           style: "width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.375rem;" %>
      </div>

      <!-- Status -->
      <div style="margin-bottom: 1.5rem;">
        <%= form.label :status, t('admin.subscriptions.status', default: 'Status'), 
                       style: "display: block; font-weight: 600; margin-bottom: 0.5rem;" %>
        <%= form.select :status, 
                       options_for_select([
                         ['Active', 'active'],
                         ['Trialing', 'trialing'],
                         ['Canceled', 'canceled'],
                         ['Expired', 'expired']
                       ], @subscription.status),
                       {},
                       { style: "width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.375rem;" } %>
      </div>

      <!-- Is Trial -->
      <div style="margin-bottom: 2rem;">
        <label style="display: flex; align-items: center; cursor: pointer;">
          <%= form.check_box :is_trial, style: "margin-right: 0.5rem;" %>
          <span style="font-weight: 600;"><%= t('admin.subscriptions.is_trial', default: 'Trial Subscription') %></span>
        </label>
      </div>

      <!-- Actions -->
      <div style="display: flex; gap: 1rem; margin-bottom: 1rem;">
        <%= form.submit t('admin.subscriptions.update_subscription', default: 'Update Subscription'), 
                       class: "btn btn-primary" %>
        <%= link_to t('admin.common.cancel', default: 'Cancel'), 
                   admin_companies_path(locale: I18n.locale), 
                   class: "btn btn-secondary" %>
      </div>
      
      <!-- Delete Action -->
      <div style="border-top: 1px solid #e5e7eb; padding-top: 1rem;">
        <%= link_to t('admin.subscriptions.delete_subscription', default: 'Delete Subscription'), 
                   destroy_admin_subscription_path(@subscription, locale: I18n.locale), 
                   method: :delete,
                   data: { confirm: 'Are you sure? This action cannot be undone.' },
                   class: "btn",
                   style: "background: #dc2626; color: white;" %>
      </div>
    <% end %>
  </div>
</div>
