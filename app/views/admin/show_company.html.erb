<% content_for :page_title, @company.name %>
<% content_for :page_subtitle, t('admin.companies.company_details', default: 'Company details and management') %>

<!-- Back to Companies -->
<div style="margin-bottom: 2rem;">
  <%= link_to admin_companies_path(locale: I18n.locale), class: "btn btn-secondary" do %>
    ← <%= t('admin.companies.back_to_companies', default: 'Back to Companies') %>
  <% end %>
</div>

<!-- Company Overview -->
<div class="stats-grid" style="margin-bottom: 2rem;">
  <div class="stat-card">
    <div style="display: flex; align-items: center; margin-bottom: 1rem;">
      <span style="font-size: 2rem; margin-right: 1rem;">🏢</span>
      <div>
        <div style="font-size: 1.125rem; font-weight: 600;"><%= @company.name %></div>
        <div style="color: #6b7280; font-size: 0.875rem;">Company ID: <%= @company.id %></div>
      </div>
    </div>
  </div>
  
  <div class="stat-card">
    <div class="stat-value"><%= @employees.count %></div>
    <div class="stat-label"><%= t('admin.companies.total_employees', default: 'Total Employees') %></div>
  </div>
  
  <div class="stat-card">
    <div style="display: flex; align-items: center; margin-bottom: 1rem;">
      <span style="font-size: 2rem; margin-right: 1rem;">📅</span>
      <div>
        <div style="font-size: 1.125rem; font-weight: 600;">
          <%= @company.created_at.strftime("%B %d, %Y") %>
        </div>
        <div style="color: #6b7280; font-size: 0.875rem;">Registration Date</div>
      </div>
    </div>
  </div>
  
  <div class="stat-card">
    <div style="display: flex; align-items: center; margin-bottom: 1rem;">
      <span style="font-size: 2rem; margin-right: 1rem;">💳</span>
      <div>
        <div style="font-size: 1.125rem; font-weight: 600;">
          <% if @plan %>
            <%= @plan.name.titleize %>
          <% else %>
            Free Plan
          <% end %>
        </div>
        <div style="color: #6b7280; font-size: 0.875rem;">Current Plan</div>
      </div>
    </div>
  </div>
</div>

<!-- Owner Information -->
<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">
  <div class="data-table">
    <div class="table-header">
      <h3><%= t('admin.companies.owner_information', default: 'Owner Information') %></h3>
    </div>
    <div style="padding: 1.5rem;">
      <% if @owner %>
        <div style="margin-bottom: 1rem;">
          <div style="font-weight: 600; margin-bottom: 0.25rem;">Name</div>
          <div style="color: #6b7280;">
            <%= "#{@owner.user_profile&.first_name} #{@owner.user_profile&.last_name}".strip %>
            <% if "#{@owner.user_profile&.first_name} #{@owner.user_profile&.last_name}".strip.blank? %>
              <em>No name provided</em>
            <% end %>
          </div>
        </div>
        
        <div style="margin-bottom: 1rem;">
          <div style="font-weight: 600; margin-bottom: 0.25rem;">Email</div>
          <div style="color: #6b7280;"><%= @owner.email %></div>
        </div>
        
        <div style="margin-bottom: 1rem;">
          <div style="font-weight: 600; margin-bottom: 0.25rem;">User ID</div>
          <div style="color: #6b7280;"><%= @owner.id %></div>
        </div>
        
        <div>
          <div style="font-weight: 600; margin-bottom: 0.25rem;">Joined</div>
          <div style="color: #6b7280;"><%= @owner.created_at.strftime("%B %d, %Y") %></div>
        </div>
      <% else %>
        <div style="text-align: center; color: #dc2626; font-style: italic; padding: 2rem;">
          No owner assigned to this company
        </div>
      <% end %>
    </div>
  </div>
  
  <!-- Subscription Information -->
  <div class="data-table">
    <div class="table-header">
      <h3><%= t('admin.companies.subscription_information', default: 'Subscription Information') %></h3>
    </div>
    <div style="padding: 1.5rem;">
      <% if @subscription %>
        <div style="margin-bottom: 1rem;">
          <div style="font-weight: 600; margin-bottom: 0.25rem;">Status</div>
          <div>
            <% status = @subscription.status %>
            <% status_color = case status
                 when 'active' then 'background: #dcfce7; color: #16a34a;'
                 when 'trialing' then 'background: #fef3c7; color: #d97706;'
                 when 'canceled' then 'background: #fecaca; color: #dc2626;'
                 else 'background: #f3f4f6; color: #6b7280;'
               end %>
            <span style="<%= status_color %> padding: 0.25rem 0.5rem; border-radius: 0.25rem; font-size: 0.875rem; font-weight: 500;">
              <%= status.titleize %>
            </span>
          </div>
        </div>
        
        <% if @subscription.trial_end %>
          <div style="margin-bottom: 1rem;">
            <div style="font-weight: 600; margin-bottom: 0.25rem;">Trial End</div>
            <div style="color: #6b7280;"><%= @subscription.trial_end.strftime("%B %d, %Y") %></div>
          </div>
        <% end %>
        
        <% if @subscription.current_period_start %>
          <div style="margin-bottom: 1rem;">
            <div style="font-weight: 600; margin-bottom: 0.25rem;">Current Period</div>
            <div style="color: #6b7280;">
              <%= @subscription.current_period_start.strftime("%b %d") %> - 
              <%= @subscription.current_period_end&.strftime("%b %d, %Y") %>
            </div>
          </div>
        <% end %>
        
        <div>
          <div style="font-weight: 600; margin-bottom: 0.25rem;">Subscription ID</div>
          <div style="color: #6b7280; font-family: monospace; font-size: 0.875rem;"><%= @subscription.id %></div>
        </div>
      <% else %>
        <div style="text-align: center; color: #6b7280; font-style: italic; padding: 2rem;">
          No active subscription
        </div>
      <% end %>
    </div>
  </div>
</div>

<!-- Employees List -->
<div class="data-table">
  <div class="table-header">
    <h3><%= t('admin.companies.employees', default: 'Employees') %> (<%= @employees.count %>)</h3>
  </div>
  
  <% if @employees.any? %>
    <div style="padding: 1rem 1.5rem; background: #f9fafb; border-bottom: 1px solid #e5e7eb;">
      <div style="display: grid; grid-template-columns: 2fr 2fr 1fr 1fr; gap: 1rem; font-weight: 600; font-size: 0.875rem; color: #374151;">
        <div>Name</div>
        <div>Email</div>
        <div>Status</div>
        <div>Joined</div>
      </div>
    </div>
    
    <div class="table-content" style="max-height: 400px;">
      <% @employees.each do |contract| %>
        <div class="table-row" style="padding: 1rem 1.5rem;">
          <div style="display: grid; grid-template-columns: 2fr 2fr 1fr 1fr; gap: 1rem; align-items: center;">
            <div>
              <div style="font-weight: 500;">
                <%= "#{contract.first_name} #{contract.last_name}".strip %>
                <% if "#{contract.first_name} #{contract.last_name}".strip.blank? %>
                  <em style="color: #6b7280;">No name</em>
                <% end %>
              </div>
            </div>
            
            <div style="color: #6b7280;">
              <%= contract.email %>
            </div>
            
            <div>
              <% if contract.terminated? %>
                <span style="background: #fecaca; color: #dc2626; padding: 0.25rem 0.5rem; border-radius: 0.25rem; font-size: 0.75rem;">
                  Terminated
                </span>
              <% elsif contract.suspended? %>
                <span style="background: #fef3c7; color: #d97706; padding: 0.25rem 0.5rem; border-radius: 0.25rem; font-size: 0.75rem;">
                  Suspended
                </span>
              <% else %>
                <span style="background: #dcfce7; color: #16a34a; padding: 0.25rem 0.5rem; border-radius: 0.25rem; font-size: 0.75rem;">
                  Active
                </span>
              <% end %>
            </div>
            
            <div style="font-size: 0.875rem; color: #6b7280;">
              <%= contract.created_at.strftime("%b %d, %Y") %>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  <% else %>
    <div class="table-row">
      <div style="text-align: center; color: #6b7280; font-style: italic; padding: 2rem;">
        No employees found
      </div>
    </div>
  <% end %>
</div>
