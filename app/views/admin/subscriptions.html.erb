<% content_for :page_title, t('admin.subscriptions.title', default: 'Subscription Management') %>
<% content_for :page_subtitle, t('admin.subscriptions.subtitle', default: 'Manage subscription plans and billing') %>

<!-- Subscriptions Summary -->
<div class="stats-grid" style="margin-bottom: 2rem;">
  <div class="stat-card">
    <div class="stat-value"><%= @total_subscriptions %></div>
    <div class="stat-label"><%= t('admin.subscriptions.total_subscriptions', default: 'Total Subscriptions') %></div>
  </div>

  <div class="stat-card">
    <div class="stat-value"><%= @subscriptions.count { |s| s[:status] == 'active' } %></div>
    <div class="stat-label"><%= t('admin.subscriptions.active_subscriptions', default: 'Active Subscriptions') %></div>
  </div>

  <div class="stat-card">
    <div class="stat-value"><%= @subscriptions.count { |s| s[:is_trial] } %></div>
    <div class="stat-label"><%= t('admin.subscriptions.trial_subscriptions', default: 'Trial Subscriptions') %></div>
  </div>

  <div class="stat-card">
    <div class="stat-value"><%= @subscriptions.count { |s| s[:status] == 'expired' } %></div>
    <div class="stat-label"><%= t('admin.subscriptions.expired_subscriptions', default: 'Expired Subscriptions') %></div>
  </div>
</div>

<!-- Subscriptions Table -->
<div class="data-table">
  <div class="table-header">
    <h3><%= t('admin.subscriptions.all_subscriptions', default: 'All Subscriptions') %></h3>
  </div>

  <div style="padding: 1rem 1.5rem; background: #f9fafb; border-bottom: 1px solid #e5e7eb;">
    <div class="admin-subscriptions-grid" style="font-weight: 600; font-size: 0.875rem; color: #374151;">
      <div><%= t('admin.subscriptions.company', default: 'Company') %></div>
      <div><%= t('admin.subscriptions.plan', default: 'Plan') %></div>
      <div style="text-align: center;"><%= t('admin.subscriptions.status', default: 'Status') %></div>
      <div style="text-align: center;"><%= t('admin.subscriptions.trial', default: 'Trial') %></div>
      <div style="text-align: center;"><%= t('admin.subscriptions.start_date', default: 'Start Date') %></div>
      <div style="text-align: center;"><%= t('admin.subscriptions.expire_date', default: 'Expire Date') %></div>
      <div style="text-align: center;"><%= t('admin.subscriptions.actions', default: 'Actions') %></div>
    </div>
  </div>

  <div class="table-content" style="max-height: 600px;">
    <% if @subscriptions.any? %>
      <% @subscriptions.each do |subscription| %>
        <div class="table-row" style="padding: 1rem 1.5rem;">
          <div class="admin-subscriptions-grid" style="align-items: center;">
            <!-- Company -->
            <div style="min-width: 0; overflow: hidden;">
              <div style="font-weight: 600; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                <%= link_to subscription[:company].name, admin_company_path(subscription[:company].id, locale: I18n.locale),
                           style: "color: #4f46e5; text-decoration: none;" %>
              </div>
              <div style="font-size: 0.75rem; color: #6b7280;">
                ID: <%= subscription[:company].id %>
              </div>
            </div>

            <!-- Plan -->
            <div style="min-width: 0; overflow: hidden;">
              <% if subscription[:plan] %>
                <span style="background: #4f46e5; color: white; padding: 0.25rem 0.5rem; border-radius: 0.25rem; font-size: 0.75rem; font-weight: 500;">
                  <%= subscription[:plan].name.titleize %>
                </span>
              <% else %>
                <span style="color: #6b7280; font-style: italic;">No plan</span>
              <% end %>
            </div>

            <!-- Status -->
            <div style="text-align: center;">
              <% status_color = case subscription[:status]
                   when 'active' then 'background: #dcfce7; color: #16a34a;'
                   when 'trialing' then 'background: #fef3c7; color: #d97706;'
                   when 'canceled' then 'background: #fecaca; color: #dc2626;'
                   when 'expired' then 'background: #f3f4f6; color: #6b7280;'
                   else 'background: #f3f4f6; color: #6b7280;'
                 end %>
              <span style="<%= status_color %> padding: 0.25rem 0.5rem; border-radius: 0.25rem; font-size: 0.75rem; font-weight: 500;">
                <%= subscription[:status].titleize %>
              </span>
            </div>

            <!-- Trial -->
            <div style="text-align: center;">
              <% if subscription[:is_trial] %>
                <span style="background: #fef3c7; color: #d97706; padding: 0.25rem 0.5rem; border-radius: 0.25rem; font-size: 0.75rem;">
                  Yes
                </span>
              <% else %>
                <span style="color: #6b7280;">No</span>
              <% end %>
            </div>

            <!-- Start Date -->
            <div style="font-size: 0.875rem; color: #6b7280; text-align: center;">
              <%= subscription[:start_date].strftime("%b %d, %Y") %>
            </div>

            <!-- Expire Date -->
            <div style="font-size: 0.875rem; color: #6b7280; text-align: center;">
              <%= subscription[:expire_date].strftime("%b %d, %Y") %>
            </div>

            <!-- Actions -->
            <div style="text-align: center;">
              <%= link_to edit_admin_subscription_path(subscription[:id], locale: I18n.locale),
                         class: "btn btn-sm",
                         style: "background: #4f46e5; color: white; padding: 0.25rem 0.5rem; font-size: 0.75rem; text-decoration: none; border-radius: 0.25rem;" do %>
                Edit
              <% end %>
            </div>
          </div>
        </div>
      <% end %>
    <% else %>
      <div class="table-row">
        <div style="text-align: center; color: #6b7280; font-style: italic; padding: 2rem;">
          No subscriptions found
        </div>
      </div>
    <% end %>
  </div>
</div>

<style>
  .admin-subscriptions-grid {
    display: grid;
    grid-template-columns: 200px 120px 80px 60px 100px 100px 80px;
    gap: 1rem;
  }

  @media (max-width: 768px) {
    .admin-subscriptions-grid {
      grid-template-columns: 1fr;
      gap: 0.5rem;
    }

    .admin-subscriptions-grid > div {
      padding: 0.25rem 0;
      border-bottom: 1px solid #f3f4f6;
    }

    .admin-subscriptions-grid > div:last-child {
      border-bottom: none;
    }
  }
</style>

<style>
  ul {
    list-style: none;
    padding: 0;
  }
  
  ul li {
    position: relative;
    padding-left: 1.5rem;
    margin-bottom: 0.5rem;
  }
  
  ul li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #16a34a;
    font-weight: bold;
  }
</style>
