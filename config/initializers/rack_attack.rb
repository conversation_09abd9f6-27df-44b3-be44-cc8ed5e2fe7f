# config/initializers/rack_attack.rb
# Security: Rate limiting for JWT authentication endpoints

class Rack::Attack
  # Configure Redis as the cache store for rate limiting
  # Use the same Redis connection pool as the rest of the application
  # This ensures consistent Redis usage and prevents connection issues
  
  # Build namespace manually since RedisNamespacing might not be loaded yet
  base_namespace = ENV.fetch('REDIS_NAMESPACE', 'attendifyapp')
  rate_limit_namespace = "#{base_namespace}:rate_limit"
  
  # Use the global Redis connection pool instead of creating a new connection
  # This prevents the "undefined method 'with' for Hash" error in tests
  Rack::Attack.cache.store = ActiveSupport::Cache::RedisCacheStore.new(
    redis: Redis.current,  # Use the ConnectionPool from redis.rb initializer
    namespace: rate_limit_namespace
  )

  # Allow localhost in development
  safelist('allow-localhost') do |req|
    '127.0.0.1' == req.ip || '::1' == req.ip if Rails.env.development?
  end

  # Allow local network in development for cross-machine testing
  safelist('allow-local-network') do |req|
    # Allow common private network ranges
    req.ip.start_with?('192.168.', '10.', '172.16.', '172.17.', '172.18.', '172.19.', 
                        '172.20.', '172.21.', '172.22.', '172.23.', '172.24.', 
                        '172.25.', '172.26.', '172.27.', '172.28.', '172.29.', 
                        '172.30.', '172.31.') if Rails.env.development?
  end

  # CRITICAL: Rate limiting for JWT authentication endpoints
  # Protect against brute force attacks on authentication
  
  # JWT Login endpoint - Chunk 10 implementation
  # Limit: 5 requests per 20 seconds per IP
  throttle('jwt_login/ip', limit: 5, period: 20.seconds) do |req|
    if req.path == '/api/v1/auth/jwt_login' && req.post?
      req.ip
    end
  end

  # JWT Refresh Token endpoint - Chunk 12 implementation  
  # Limit: 10 requests per 1 minute per IP
  throttle('jwt_refresh/ip', limit: 10, period: 1.minute) do |req|
    if req.path == '/api/v1/auth/refresh_token' && req.post?
      req.ip
    end
  end

  # JWT Registration endpoint - Chunk 57 implementation
  # Limit: 3 registration attempts per 5 minutes per IP
  throttle('jwt_register/ip', limit: 3, period: 5.minutes) do |req|
    if req.path == '/api/v1/auth/jwt_register' && req.post?
      req.ip
    end
  end

  # Email confirmation endpoints - Chunk 57 implementation
  # Limit: 5 requests per 10 minutes per IP for resend confirmation
  throttle('resend_confirmation/ip', limit: 5, period: 10.minutes) do |req|
    if req.path == '/api/v1/auth/resend_confirmation' && req.post?
      req.ip
    end
  end

  # Password reset endpoints - Chunk 56 implementation  
  # Limit: 3 requests per 10 minutes per IP for password reset requests
  throttle('password_reset_request/ip', limit: 3, period: 10.minutes) do |req|
    if req.path == '/api/v1/auth/request_password_reset' && req.post?
      req.ip
    end
  end

  # PHASE 1 CRITICAL SECURITY: JWT Invitation endpoints - Chunk 58 implementation
  # Protect against invitation spam attacks identified by Gemini analysis
  
  # JWT Send Invitation endpoint - Per IP rate limiting
  # Limit: 10 invitations per minute per IP
  throttle('jwt_invitation/ip', limit: 10, period: 1.minute) do |req|
    if req.path == '/api/v1/auth/send_invitation' && req.post?
      req.ip
    end
  end
  
  # JWT Send Invitation endpoint - Per authenticated user rate limiting
  # Limit: 20 invitations per 10 minutes per sender
  throttle('jwt_invitation/sender', limit: 20, period: 10.minutes) do |req|
    if req.path == '/api/v1/auth/send_invitation' && req.post?
      # Extract user ID from JWT for rate limiting by sender
      auth_header = req.get_header('HTTP_AUTHORIZATION')
      if auth_header && auth_header.start_with?('Bearer ')
        begin
          token = auth_header.split(' ').last
          payload = JwtService.decode(token) if defined?(JwtService)
          payload['user_id'] if payload && payload['user_id']
        rescue
          # If JWT decoding fails, fall back to IP-based limiting only
          nil
        end
      end
    end
  end
  
  # JWT Send Invitation endpoint - Per recipient email rate limiting
  # Limit: 3 invitations per hour per recipient email (prevent email spam)
  throttle('jwt_invitation/recipient', limit: 3, period: 1.hour) do |req|
    if req.path == '/api/v1/auth/send_invitation' && req.post?
      # Extract email from request body for rate limiting by recipient
      begin
        body = req.body.read
        req.body.rewind  # Reset body for normal processing
        parsed_body = JSON.parse(body) if body.present?
        parsed_body['email'] if parsed_body && parsed_body['email']
      rescue
        # If parsing fails, fall back to IP-based limiting only
        nil
      end
    end
  end

  # General API rate limiting for authenticated endpoints
  # Protects against API abuse from legitimate users
  throttle('api/ip', limit: 300, period: 5.minutes) do |req|
    if req.path.start_with?('/api/')
      req.ip
    end
  end

  # Feedback submissions - prevent abuse
  # Allow unauthenticated; throttle per IP and per email
  throttle('feedbacks/ip', limit: 10, period: 1.minute) do |req|
    if req.path == '/api/v1/feedbacks' && req.post?
      req.ip
    end
  end

  throttle('feedbacks/email', limit: 3, period: 10.minutes) do |req|
    if req.path == '/api/v1/feedbacks' && req.post?
      begin
        body = req.body.read
        req.body.rewind
        parsed = JSON.parse(body) rescue nil
        if parsed
          # Support either top-level or nested under feedback
          (parsed['user_email'] || parsed.dig('feedback', 'user_email')).to_s.downcase
        end
      rescue
        nil
      end
    end
  end

  # Aggressive rate limiting for failed authentication attempts
  # Track by IP and block rapid-fire authentication failures
  throttle('auth_failures/ip', limit: 3, period: 5.minutes) do |req|
    if (req.path == '/api/v1/auth/jwt_login' || req.path.include?('sign_in')) && req.post?
      # This will be incremented by Rails on auth failures
      req.ip if req.env['rack.attack.auth_failure']
    end
  end

  # Security logging for blocked requests
  # Log all rate limit violations for security monitoring
  ActiveSupport::Notifications.subscribe('rack.attack') do |name, start, finish, request_id, payload|
    req = payload[:request]
    Rails.logger.warn "[SECURITY] Rack::Attack blocked request: #{req.env['rack.attack.match_type']} - #{req.ip} - #{req.request_method} #{req.fullpath}"
    
    # Log to AuthHealthCheck for centralized security monitoring
    if defined?(AuthHealthCheck)
      AuthHealthCheck.log_security_event('rate_limit_violation', {
        ip: req.ip,
        path: req.fullpath,
        method: req.request_method,
        match_type: req.env['rack.attack.match_type'],
        matched_data: req.env['rack.attack.matched']
      })
    end
  end

  # Custom response for rate limited requests
  # Return 429 Too Many Requests with Retry-After header
  self.throttled_responder = lambda do |req|
    match_data = req.env['rack.attack.match_data']
    now = match_data[:epoch_time]
    
    headers = {
      'Content-Type' => 'application/json',
      'Retry-After' => (match_data[:period] - (now % match_data[:period])).to_s
    }
    
    [429, headers, [{ error: 'Too Many Requests', message: 'Rate limit exceeded. Please try again later.' }.to_json]]
  end
end

# Integration with Rails application middleware
Rails.application.config.middleware.use Rack::Attack