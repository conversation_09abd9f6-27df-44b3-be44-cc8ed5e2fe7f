class CreateFeedbacks < ActiveRecord::Migration[7.0]
  def change
    create_table :feedbacks do |t|
      t.references :company, null: true, foreign_key: true, index: true
      t.references :user, null: true, foreign_key: true, index: true
      t.string :page_url, null: false
      t.integer :category, null: false, default: 0
      t.text :message, null: false
      t.string :user_plan
      t.string :user_email, null: false
      t.string :user_company_name
      t.integer :status, null: false, default: 0

      t.timestamps
    end

    add_index :feedbacks, [:company_id, :created_at]
    add_index :feedbacks, :status
    add_index :feedbacks, :category
  end
end

