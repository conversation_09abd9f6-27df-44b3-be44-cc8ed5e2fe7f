# Vite Configuration Architecture

## Overview

This document covers the Vite configuration architecture for the AttendifyApp, including critical troubleshooting information for HMR (Hot Module Replacement) setup.

## Configuration Files

### Primary Configuration Files

1. **`vite.config.ts`** - Main Vite configuration
2. **`config/vite.json`** - ViteRuby environment-specific configuration
3. **`Procfile.dev`** - Development server process configuration

### Key Configuration Settings

#### vite.config.ts
```typescript
export default defineConfig({
  plugins: [
    RubyPlugin(),
    vue(),
    consoleLoggerPlugin(),
    VueMcp()
  ],
  server: {
    host: '0.0.0.0',
    port: 3036,
    strictPort: true,
    hmr: {
      overlay: true,
      host: '************',
      port: 3036,
      clientPort: 3036
    },
    cors: true
  }
})
```

#### config/vite.json
```json
{
  "all": {
    "sourceCodeDir": "app/frontend",
    "watchAdditionalPaths": []
  },
  "development": {
    "autoBuild": true,
    "publicOutputDir": "vite-dev",
    "port": 3036,
    "host": "************",
    "assetHost": "http://************:3036"
  }
}
```

#### Procfile.dev
```
vite: VITE_RUBY_HOST=************ VITE_RUBY_PORT=3036 VITE_RUBY_ASSET_HOST=http://************:3036 bin/vite dev --force
web: bin/rails s -b 0.0.0.0 --log-to-stdout
```

## CRITICAL: Environment Variable Configuration

### The VITE_RUBY_HOST Issue

**CRITICAL CONFIGURATION REQUIREMENT**: The `VITE_RUBY_HOST` environment variable MUST match the actual IP address where Vite dev server is running.

#### Problem Scenario
If you have `VITE_RUBY_HOST=0.0.0.0` in your shell environment (e.g., `~/.bashrc`), Rails will attempt to connect to the Vite dev server at `0.0.0.0:3036` instead of the actual IP address. This causes:

1. Rails cannot detect the Vite dev server is running
2. `ViteRuby.instance.dev_server_running?` returns `false`
3. Rails falls back to **building assets on every request** instead of using HMR
4. Slow page loads and loss of hot module replacement functionality

#### Solution
Ensure the environment variable matches your server's IP:

```bash
# In ~/.bashrc or equivalent shell config
export VITE_RUBY_HOST=************  # Use your actual IP, NOT 0.0.0.0
```

### Verification Commands

Check if Rails can detect the Vite dev server:
```bash
bundle exec rails runner "puts ViteRuby.instance.dev_server_running?"
```

Check current configuration:
```bash
bundle exec rails runner "puts ViteRuby.instance.config.host; puts ViteRuby.instance.config.port"
```

Check environment variables:
```bash
env | grep -E "VITE|HOST"
```

## HMR vs Asset Building Detection

### Expected Behavior (HMR Working)
When HMR is working correctly:
- Rails detects Vite dev server is running
- Assets are served directly from Vite dev server at `http://************:3036/vite-dev/`
- Changes to frontend code trigger instant updates in browser
- No asset building logs in Rails console

### Problem Behavior (Asset Building)
When Rails can't connect to Vite dev server:
- Rails logs show "Building with Vite ⚡️"
- Asset compilation happens on every page load
- Slow response times (15+ seconds)
- Loss of hot module replacement functionality

## Network Configuration

The application runs on a specific IP address (`************`) rather than localhost to allow:
- External device access for mobile testing
- Proper HMR functionality across the network
- Consistent asset serving between development environments

## Troubleshooting Checklist

1. **Verify Vite dev server is running**: Check `http://************:3036/vite-dev/`
2. **Check environment variables**: Ensure `VITE_RUBY_HOST=************`
3. **Restart shell session**: After changing `~/.bashrc`, restart terminal
4. **Clear caches**: Run `bundle exec vite clobber` if needed
5. **Verify Rails detection**: Run `ViteRuby.instance.dev_server_running?`

## Cache Management

When switching between git branches or after configuration changes:
```bash
# Clear all Vite caches
bundle exec vite clobber

# Clear Rails caches
bundle exec rails tmp:clear

# Clear node modules cache
rm -rf node_modules/.vite
```

## Development Workflow

1. Start development servers: `foreman start -f Procfile.dev`
2. Verify HMR is working (no "Building with Vite" logs)
3. Check asset URLs point to `http://************:3036/vite-dev/assets/`
4. Test hot reload by making frontend changes