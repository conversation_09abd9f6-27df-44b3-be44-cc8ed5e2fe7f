# PWA Development Controls

## Overview

This system provides proper service worker control during development to prevent endless refresh loops while still allowing PWA testing when needed.

## The Problem

Previously, the service worker was aggressively registered on every page load in development, fighting against DevTools "Unregister service workers" checkbox and causing endless refresh loops when code changed.

## The Solution

### Smart Registration System

1. **Respects DevTools Settings**: If no active service worker exists and PWA isn't explicitly enabled, respects the DevTools unregister state
2. **localStorage Controls**: Provides manual override controls via localStorage
3. **Prevents Loops**: Enhanced update detection prevents automatic reloads in development
4. **Development Utilities**: Console utilities for easy PWA debugging

## How to Use

### Default Behavior

- **First visit**: Service worker registers normally (if P<PERSON> enabled in backend)
- **After DevTools unregister**: Respects the unregister and won't re-register automatically
- **Code changes**: Won't cause endless refresh loops

### Manual Controls

Open browser console and use these commands:

```javascript
// Show all available commands
AttendifyPWAUtils.help()

// Enable PWA in development (override DevTools unregister)
AttendifyPWAUtils.enablePWA()

// Disable P<PERSON> in development
AttendifyPWAUtils.disablePWA()

// Enable automatic service worker updates on code changes
AttendifyPWAUtils.enableAutoUpdate()

// Disable automatic updates (manual refresh required)
AttendifyPWAUtils.disableAutoUpdate()

// Unregister all service workers
AttendifyPWAUtils.unregisterAll()

// Check current status
AttendifyPWAUtils.status()
```

### Development Workflow

1. **Normal Development** (no PWA testing):
   - Use DevTools "Unregister service workers" checkbox
   - Page refreshes won't cause endless loops
   - Service worker stays unregistered

2. **PWA Testing**:
   - Run `AttendifyPWAUtils.enablePWA()` 
   - Refresh page to register service worker
   - Test PWA features
   - When done: `AttendifyPWAUtils.disablePWA()`

3. **Active PWA Development**:
   - Enable PWA: `AttendifyPWAUtils.enablePWA()`
   - Enable auto-updates: `AttendifyPWAUtils.enableAutoUpdate()`
   - Service worker updates automatically on code changes
   - No endless refresh loops

## Technical Details

### localStorage Keys

- `attendify_enable_pwa_dev`: Controls PWA registration in development
  - `'true'`: Force enable PWA regardless of DevTools state
  - `'false'`: Force disable PWA 
  - `null/undefined`: Respect DevTools state

- `attendify_pwa_auto_update_dev`: Controls automatic service worker updates
  - `'true'`: Auto-update service worker on changes
  - `'false'`: Manual update control (default)

### Production Behavior

- All development controls are disabled in production
- Service worker registers and updates normally
- User prompted for updates as expected
- No localStorage overrides affect production

## Troubleshooting

### Still Getting Refresh Loops?
1. Run `AttendifyPWAUtils.unregisterAll()`
2. Run `AttendifyPWAUtils.disablePWA()`
3. Refresh page - loops should stop

### Want to Test PWA Features?
1. Run `AttendifyPWAUtils.enablePWA()`  
2. Refresh page
3. Service worker registers without loops
4. Test PWA functionality

### Service Worker Not Updating?
1. Check: `AttendifyPWAUtils.status()`
2. If auto-update disabled, manually refresh or run `AttendifyPWAUtils.enableAutoUpdate()`

This system gives you complete control over service worker behavior in development while maintaining proper production behavior.