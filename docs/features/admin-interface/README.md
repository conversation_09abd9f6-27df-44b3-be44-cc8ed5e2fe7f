# Admin Interface - Implementation Plan

**Project**: Admin Interface  
**Linear Issue**: TYM-89  
**Status**: Planning  
**Last Updated**: 2025-08-07

## Overview

Strategic admin interface for user and subscription management within the SPA architecture. Provides secure access to company management, subscription administration, and essential platform oversight tools.

## Architecture Principles

### Security-First Design
- **No special Admin User model** - leverage existing User model
- **Multiple hardcoded validation layers** for maximum security
- **Zero admin functionality exposure** to regular users
- **Complete isolation** from regular user workflows

### SPA Integration
- **Within SPA architecture** but outside normal user flow
- **Separate admin routes** with dedicated protection
- **Admin-specific components** and layouts
- **Secure API endpoints** with admin-only access

## Security Implementation

### Hardcoded Security Layers

```ruby
# app/models/user.rb
ADMIN_COMPANY_IDS = [1, 2].freeze # Hardcoded admin company IDs
ADMIN_EMAILS = ['<EMAIL>', '<EMAIL>'].freeze

def admin_user?
  return false unless ADMIN_EMAILS.include?(email.downcase)
  return false unless user_profile&.first_name == 'Admin'
  return false unless user_profile&.last_name == 'User'
  
  company_user_roles.joins(:company)
                   .where(companies: { id: ADMIN_COMPANY_IDS })
                   .exists?
end
```

### Authorization Policy

```ruby
# app/policies/admin_policy.rb
class AdminPolicy < ApplicationPolicy
  def access?
    user&.admin_user?
  end
  
  def manage_companies?
    access?
  end
  
  def manage_subscriptions?
    access?
  end
end
```

## Phase 1: Basic Admin Access & Company Management

### Backend Implementation

#### 1. User Model Enhancement
- [ ] Add `ADMIN_COMPANY_IDS` constant
- [ ] Add `ADMIN_EMAILS` constant  
- [ ] Implement `admin_user?` method with validation layers
- [ ] Add admin detection to user serialization

#### 2. Admin Policy Creation
- [ ] Create `app/policies/admin_policy.rb`
- [ ] Implement access control methods
- [ ] Add company and subscription management permissions

#### 3. Admin API Controller
- [ ] Create `app/controllers/api/v1/admin_controller.rb`
- [ ] Implement authentication and authorization
- [ ] Add companies listing endpoint
- [ ] Include owner contacts and employee counts

```ruby
# GET /api/v1/admin/companies
def companies
  companies = Company.includes(:users, :current_subscription, :current_plan)
                    .with_tenant_disabled do
    Company.all.map do |company|
      {
        id: company.id,
        name: company.name,
        owner: company.users_with_role('owner').first,
        employee_count: company.contracts.count,
        subscription: company.current_subscription,
        plan: company.current_plan
      }
    end
  end
  
  render json: { companies: companies }
end
```

### Frontend Implementation

#### 1. Router Configuration
- [ ] Add admin routes to `app/frontend/router/index.js`
- [ ] Implement route protection with `requiresAdmin` meta
- [ ] Create admin route guards

```javascript
{
  path: '/:locale(cs|sk|en)/admin',
  component: () => import('../layouts/AdminLayout.vue'),
  meta: { requiresAuth: true, requiresAdmin: true },
  children: [
    {
      path: 'dashboard',
      name: 'adminDashboard',
      component: () => import('../views/admin/AdminDashboardView.vue')
    },
    {
      path: 'companies',
      name: 'adminCompanies', 
      component: () => import('../views/admin/AdminCompaniesView.vue')
    }
  ]
}
```

#### 2. Admin Layout Component
- [ ] Create `app/frontend/layouts/AdminLayout.vue`
- [ ] Implement admin navigation
- [ ] Add admin-specific styling

#### 3. Sidebar Integration
- [ ] Update `app/frontend/components/Sidebar.vue`
- [ ] Add conditional admin link rendering
- [ ] Implement admin detection in computed properties

#### 4. Company Management View
- [ ] Create `app/frontend/views/admin/AdminCompaniesView.vue`
- [ ] Implement company listing table
- [ ] Display owner contacts and employee counts
- [ ] Add basic company management actions

## Phase 2: Subscription Management

### Backend Implementation

#### 1. Subscription API Endpoints
- [ ] Add subscription listing to admin controller
- [ ] Implement subscription CRUD operations
- [ ] Add subscription analytics endpoint

```ruby
# GET /api/v1/admin/subscriptions
def subscriptions
  subscriptions = Subscription.includes(:company, :plan)
                             .with_tenant_disabled { Subscription.all }
  render json: { subscriptions: subscriptions }
end

# POST /api/v1/admin/subscriptions
def create_subscription
  # Implementation for creating subscriptions
end

# PATCH /api/v1/admin/subscriptions/:id
def update_subscription
  # Implementation for updating subscriptions
end
```

#### 2. Subscription Analytics
- [ ] Add revenue calculations
- [ ] Implement plan usage statistics
- [ ] Add subscription status analytics

### Frontend Implementation

#### 1. Subscription Management View
- [ ] Create `app/frontend/views/admin/AdminSubscriptionsView.vue`
- [ ] Implement subscription listing table
- [ ] Add subscription creation forms
- [ ] Implement subscription modification interface

#### 2. Subscription Forms
- [ ] Create subscription creation modal
- [ ] Implement subscription editing forms
- [ ] Add subscription cancellation confirmation

## Technical Architecture

### API Design
- **Base Path**: `/api/v1/admin`
- **Authentication**: JWT with admin validation
- **Authorization**: AdminPolicy enforcement
- **Multi-tenancy**: Disabled for admin operations using `with_tenant_disabled`

### Component Structure
```
app/frontend/
├── layouts/
│   └── AdminLayout.vue
├── views/admin/
│   ├── AdminDashboardView.vue
│   ├── AdminCompaniesView.vue
│   └── AdminSubscriptionsView.vue
└── components/admin/
    ├── CompanyTable.vue
    ├── SubscriptionTable.vue
    └── SubscriptionForm.vue
```

### Route Protection
- Router guards check `user.is_admin` flag
- API endpoints validate admin access
- Frontend components conditionally render admin features

## Implementation Checklist

### Phase 1: Basic Admin Access
- [ ] User model admin detection
- [ ] Admin policy creation
- [ ] Admin API controller
- [ ] Admin routes and layout
- [ ] Sidebar admin link
- [ ] Company management view

### Phase 2: Subscription Management
- [ ] Subscription API endpoints
- [ ] Subscription management view
- [ ] Subscription forms and modals
- [ ] Subscription analytics

### Testing & Security
- [ ] Admin access unit tests
- [ ] API endpoint security tests
- [ ] Frontend route protection tests
- [ ] End-to-end admin workflow tests

## Future Backlog (Out of Current Scope)

- Advanced analytics and reporting
- User activity monitoring and audit logs
- User impersonation and support tools
- Bulk operations and data export
- System health monitoring dashboard

## Security Considerations

1. **Multiple Validation Layers**: Email, profile, and company validation
2. **API Protection**: All admin endpoints require admin authorization
3. **Frontend Guards**: Router-level protection prevents access
4. **Audit Logging**: Track all admin actions (future enhancement)
5. **Zero Exposure**: No admin functionality visible to regular users

## Success Criteria

- [ ] Admin access control with multiple security layers
- [ ] Company listing with owner contacts and employee counts
- [ ] Subscription management interface (view, add, modify, cancel)
- [ ] Admin link visible only to authorized users
- [ ] No admin functionality accessible to regular users
- [ ] All admin actions properly authorized

---

**Next Steps**: Begin Phase 1 implementation with User model enhancement and admin detection.
