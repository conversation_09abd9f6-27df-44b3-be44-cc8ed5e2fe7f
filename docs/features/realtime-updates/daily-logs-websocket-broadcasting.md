# Daily Logs WebSocket Broadcasting Implementation

## Overview

This document describes the WebSocket broadcasting implementation for daily logs (time tracking) that enables real-time team status updates across all employees and managers in a company.

## Problem Solved

Previously, when employees used the TimeTracking component to start/end their work day, other team members wouldn't see these status changes in real-time. The daily logs controller (`/daily_logs` endpoint) lacked WebSocket broadcasting functionality, unlike the daily activities controller (`/api/v1/daily_activities/*`).

## Solution Implemented

### 1. DailyLogsController Broadcasting

Added WebSocket broadcasting to `app/controllers/daily_logs_controller.rb`:

- **create method**: Broadcasts `daily_log_started` event when employee starts work
- **update method**: Broadcasts `daily_log_ended` event when employee ends work
- **broadcast_team_status_update method**: Private method that handles the broadcasting logic

### 2. TeamStatusChannel Permissions

Updated `app/channels/team_status_channel.rb` to allow all employees (not just managers) to subscribe:

```ruby
def can_view_team_status?
  # All employees and managers can see team status updates
  current_user.contracts.exists?(company: current_tenant) ||
  current_user.has_role?(:owner, current_tenant) ||
  current_user.has_role?(:supervisor, current_tenant) ||
  current_user.has_role?(:admin, current_tenant)
end
```

### 3. Frontend Event Handling

Updated `app/frontend/store/ownerStore.js` to include timestamp fields:

- `daily_log_started`: Updates `working: true` and `dailyLogStartedAt`
- `daily_log_ended`: Updates `working: false` and `dailyLogEndedAt`

## Broadcasting Flow

1. Employee clicks "Start Work" in TimeTracking component
2. TimeTracking.vue sends POST request to `/daily_logs`
3. DailyLogsController creates daily log and broadcasts `daily_log_started` event
4. TeamStatusChannel broadcasts to all subscribed company members
5. Mainbox.vue receives the update and calls `handleTeamStatusUpdate`
6. ownerStore updates the employee status with real-time data
7. UI reflects the change immediately with pulse animation

## Event Data Structure

### daily_log_started Event
```javascript
{
  type: 'team_status_update',
  event_type: 'daily_log_started',
  employee_id: contractId,
  employee_name: "First Last",
  working: true,
  daily_log_started_at: "2025-08-08T10:30:00Z",
  daily_log_ended_at: null
}
```

### daily_log_ended Event
```javascript
{
  type: 'team_status_update',
  event_type: 'daily_log_ended',
  employee_id: contractId,
  employee_name: "First Last",
  working: false,
  daily_log_started_at: "2025-08-08T10:30:00Z",
  daily_log_ended_at: "2025-08-08T18:30:00Z"
}
```

## Broadcasting Matrix

Now all combinations work:
- ✅ Managers → Employees (both see updates)
- ✅ Employees → Employees (employees see each other)
- ✅ Employees → Managers (managers see employee updates)

## Testing

### Manual Testing Steps

1. Open the app in two browser tabs with different user accounts
2. In first tab, log in as an employee and navigate to TimeTracking
3. In second tab, log in as another employee or manager and open Mainbox
4. In first tab, click "Start Work"
5. Second tab should immediately show the employee as "working" with pulse animation
6. In first tab, click "End Work"
7. Second tab should immediately show the employee as "not working"

### Console Debugging

Check browser console for WebSocket messages:
```javascript
// Should see these logs when updates are received:
[Mainbox] Received team status update: {event_type: 'daily_log_started'...}
🔄 [Store] Processing team status update: {event_type: 'daily_log_started'...}
```

Check Rails logs for broadcasting:
```bash
tail -f log/development.log | grep TeamStatusChannel
# Should see: [TeamStatusChannel] Broadcasting daily_log_started for employee...
```

## Files Modified

1. `app/controllers/daily_logs_controller.rb` - Added broadcasting to create/update methods
2. `app/channels/team_status_channel.rb` - Updated permissions to allow all employees
3. `app/frontend/store/ownerStore.js` - Added timestamp handling for daily log events

## Related Linear Issue

- **TYM-94**: Fix TimeTracking WebSocket Broadcasting - Daily log start/end not updating team status

## Future Improvements

- Add offline queue for broadcasting when connection is lost
- Implement presence tracking to show who's currently viewing the dashboard
- Add sound notifications for status changes (configurable)
- Include more detailed work information in broadcasts