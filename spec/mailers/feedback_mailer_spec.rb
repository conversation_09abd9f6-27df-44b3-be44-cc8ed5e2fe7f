require 'rails_helper'

RSpec.describe Feedback<PERSON>ailer, type: :mailer do
  describe '#submitted' do
    it 'sends to central inbox with context' do
      company = create(:company)
      user = create(:user, email: '<EMAIL>')
      feedback = nil
      ActsAsTenant.with_tenant(company) do
        feedback = create(:feedback, page_url: '/works', message: 'Hello', user_email: '<EMAIL>', company: company, user: user, user_plan: 'plus', user_company_name: company.name)
      end

      mail = FeedbackMailer.submitted(feedback.id)

      expect(mail.to).to include('<EMAIL>')
      expect(mail.subject).to include(I18n.t('mailer.feedback_mailer.submitted.subject', default: 'Nová zpětná vazba z aplikace'))
      expect(mail.body.encoded).to include('Hello')
      expect(mail.body.encoded).to include('/works')
      expect(mail.body.encoded).to include('plus')
      expect(mail.body.encoded).to include(company.name)
      expect(mail.body.encoded).to include('<EMAIL>')
    end
  end
end

