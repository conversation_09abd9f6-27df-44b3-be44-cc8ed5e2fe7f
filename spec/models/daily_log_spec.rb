require 'rails_helper'

RSpec.describe DailyLog, type: :model do
  include ActiveSupport::Testing::TimeHelpers

  let(:company) { create(:company) }
  let(:contract) { create(:contract, :with_user, :skip_invitation, company: company) }
  let(:user) { contract.user }
  # Keep the base daily_log let for other tests, but don't rely on it for time validations
  let(:daily_log) { build(:daily_log, contract: contract, user: user, company: company) }

  describe 'validations' do
    # Basic requirement validations
    it 'requires a company' do
      log = build(:daily_log, contract: contract, user: user, company: nil)
      expect(log).not_to be_valid
      expect(log.errors[:company]).to include("musí existovat")
    end

    it 'requires a contract' do
      log = build(:daily_log, user: user, company: company, contract: nil)
      expect(log).not_to be_valid
      expect(log.errors[:contract]).to include("musí existovat")
    end

    # == Use corrected time validation context ==
    context 'time validations' do
      let(:now) { Time.zone.now }

      around do |example|
        travel_to now do
          example.run
        end
      end

      it 'validates start_time is not in the future', pending: "Depends on model validation fixes" do
        invalid_log = build(:daily_log, contract: contract, user: user, company: company, start_time: now + 1.hour, end_time: now + 2.hours)
        expect(invalid_log.valid?).to be false
        expect(invalid_log.errors[:start_time]).to eq(["Začátek záznamu nemůže být v budoucnosti."])
      end

      it 'validates end_time is after start_time', pending: "Depends on model validation fixes" do
        log_start = now - 4.hours
        log_end = now - 2.hours
        invalid_log = build(:daily_log, contract: contract, user: user, company: company, start_time: log_end, end_time: log_start)
        expect(invalid_log.valid?).to be false
        expect(invalid_log.errors[:end_time]).to eq(["Konec záznamu nemůže být před začátkem."])
      end

      it 'prevents overlapping time periods', pending: "Depends on model :on => :create validation fixes" do
        create(:daily_log,
          contract: contract,
          user: user,
          company: company,
          start_time: now - 4.hours,
          end_time: now - 2.hours
        )

        overlapping_log = build(:daily_log,
          contract: contract,
          user: user,
          company: company,
          start_time: now - 3.hours - 30.minutes,
          end_time: now - 1.hour - 30.minutes
        )

        expect {
          overlapping_log.save!
        }.to raise_error(ActiveRecord::RecordInvalid, /Časové období se překrývá s existujícím záznamem/)
      end

      it 'prevents multiple logs for the same day', pending: "Depends on model :on => :create validation fixes" do
         log_start_time = now.beginning_of_day + 9.hours
         log_end_time = log_start_time + 2.hours

        create(:daily_log,
          contract: contract,
          user: user,
          company: company,
          start_time: log_start_time,
          end_time: log_end_time
        )

        same_day_log = build(:daily_log,
          contract: contract,
          user: user,
          company: company,
          start_time: log_start_time + 3.hours,
          end_time: log_start_time + 5.hours
        )

        expect {
          same_day_log.save!
        }.to raise_error(ActiveRecord::RecordInvalid, /Denní záznam pro tento den již existuje/)
      end
    end # end context 'time validations'

    context 'event overlap validation' do
      it 'prevents creating a log when an event exists for the same day', pending: "Depends on model :on => :create validation fixes" do
        event = create(:event,
          contract: contract,
          user: user,
          company: company,
          start_time: Time.current.beginning_of_day,
          end_time: Time.current.end_of_day
        )

        log_with_event = build(:daily_log,
          contract: contract,
          user: user,
          company: company,
          start_time: Time.current.beginning_of_day + 4.hours,
          end_time: Time.current.beginning_of_day + 8.hours
        )

        expect {
          log_with_event.save!
        }.to raise_error(ActiveRecord::RecordInvalid, /Nelze vytvořit záznam práce pro den, kdy již existuje událost/)
      end
    end # end context 'event overlap validation'

  end # end describe 'validations'

  describe 'time calculations' do
    let(:start_time) { Time.current - 4.hours }
    let(:end_time) { Time.current - 2.hours }

    it 'calculates duration correctly' do
      log = create(:daily_log, contract: contract, user: user, company: company, start_time: start_time, end_time: end_time)
      expect(log.duration).to eq(7200) # 2 hours in seconds
    end

    describe '#duration_in_text' do
      it 'returns nil when start_time or end_time is missing' do
        log = build(:daily_log, contract: contract, user: user, company: company, start_time: nil, end_time: nil, workday: nil)
        expect(log.duration_in_text).to be_nil
      end

      it 'formats duration less than a minute correctly' do
        log = build(:daily_log, contract: contract, user: user, company: company, start_time: start_time, end_time: start_time + 30.seconds)
        expect(log.duration_in_text).to eq("30 s")
      end

      it 'formats duration less than an hour correctly' do
        log = build(:daily_log, contract: contract, user: user, company: company, start_time: start_time, end_time: start_time + 45.minutes)
        expect(log.duration_in_text).to eq("45 min.")
      end

      it 'formats duration with hours and minutes correctly' do
        log = build(:daily_log, contract: contract, user: user, company: company, start_time: start_time, end_time: start_time + 2.hours + 30.minutes)
        expect(log.duration_in_text).to eq("2 hod. 30 min.")
      end
    end
  end # end describe 'time calculations'

  describe 'status transitions' do
    it 'can be reopened' do
      log = create(:daily_log,
        contract: contract,
        user: user,
        company: company,
        start_time: Time.current - 4.hours,
        end_time: Time.current - 2.hours
      )

      log.reopen
      expect(log.end_time).to be_nil
    end

    it 'can be closed' do
      log = create(:daily_log,
        contract: contract,
        user: user,
        company: company,
        start_time: Time.current - 4.hours,
        end_time: nil # Working log
      )

      log.close
      expect(log.end_time).to be_present
    end
  end # end describe 'status transitions'

  describe 'scopes' do
    let!(:working_log) { create(:daily_log, contract: contract, user: user, company: company, start_time: Time.current - 1.hour, end_time: nil) }
    let!(:finished_log) { create(:daily_log, contract: contract, user: user, company: company, start_time: Time.current - 2.hours, end_time: Time.current - 1.hour) }

    describe '.is_working' do
      it 'returns logs with no end_time' do
        expect(DailyLog.is_working).to contain_exactly(working_log)
      end
    end

    describe '.is_finished' do
      it 'returns logs with end_time' do
        expect(DailyLog.is_finished).to contain_exactly(finished_log)
      end
    end

    describe '.at_work' do
      it 'returns logs that are currently being worked on (no end_time)' do
        expect(DailyLog.at_work).to contain_exactly(working_log)
      end
    end
  end # end describe 'scopes'

  describe 'callbacks' do
    it 'sets workday based on start_time' do
      time = Time.current
      log = create(:daily_log, contract: contract, user: user, company: company, start_time: time, end_time: time + 1.hour)
      expect(log.workday).to eq(time.wday)
    end

    it 'sets duration when end_time is present on save' do
      start_time = Time.current - 2.hours
      end_time = Time.current - 1.hour
      log = build(:daily_log, contract: contract, user: user, company: company, start_time: start_time, end_time: nil)
      expect(log.duration).to be_nil # Duration not set before save
      log.end_time = end_time
      log.save!
      expect(log.duration).to eq(3600) # 1 hour in seconds
    end

    it 'sets duration to nil when reopened', pending: "Depends on model set_duration callback fix" do
       log = create(:daily_log, contract: contract, user: user, company: company, start_time: Time.current - 2.hours, end_time: Time.current - 1.hour)
       expect(log.duration).to eq(3600)
       log.end_time = nil
       log.save! # This should trigger the set_duration callback
       log.reload # Ensure we check persisted state
       expect(log.duration).to be_nil
    end
  end # end describe 'callbacks'

  describe 'JSON serialization' do
    it 'includes duration_in_text in JSON output' do
      log = create(:daily_log,
        contract: contract,
        user: user,
        company: company,
        start_time: Time.current - 2.hours,
        end_time: Time.current - 1.hour
      )

      json = log.as_json
      expect(json).to have_key('duration_in_text')
      expect(json['duration_in_text']).to eq("1 hod. 0 min.")
    end

    it 'handles nil duration_in_text in JSON output' do
      log = create(:daily_log,
        contract: contract,
        user: user,
        company: company,
        start_time: Time.current - 2.hours,
        end_time: nil # Working log
      )

      json = log.as_json
      expect(json).to have_key('duration_in_text')
      expect(json['duration_in_text']).to be_nil
    end
  end # end describe 'JSON serialization'

  describe 'associations' do
    describe 'daily_activities dependent destroy' do
      it 'destroys associated daily_activities when daily_log is destroyed' do
        # Create an open daily log (no end_time) so we can add activities
        log = create(:daily_log, contract: contract, user: user, company: company, end_time: nil)
        activity1 = create(:daily_activity, daily_log: log, user: user, company: company, contract: contract)
        activity2 = create(:daily_activity, daily_log: log, user: user, company: company, contract: contract)
        
        activity_ids = [activity1.id, activity2.id]
        
        expect {
          log.destroy!
        }.to change { DailyActivity.where(id: activity_ids).count }.from(2).to(0)
      end
      
      it 'prevents foreign key constraint violation on deletion' do
        # Create an open daily log (no end_time) so we can add activities
        log = create(:daily_log, contract: contract, user: user, company: company, end_time: nil)
        create(:daily_activity, daily_log: log, user: user, company: company, contract: contract)
        
        expect {
          log.destroy!
        }.not_to raise_error
      end
    end
    
    describe 'breaks dependent destroy' do
      it 'destroys associated break when daily_log is destroyed' do
        log_start = 4.hours.ago
        log_end = 1.hour.ago
        log = create(:daily_log, contract: contract, user: user, company: company, start_time: log_start, end_time: log_end)
        # Break model sets user and contract from daily_log in before_validation callback
        # Break must be within daily log timeframe
        # Only one break per day is allowed by Break model validation
        break_record = create(:break, daily_log: log, start_time: log_start + 1.hour, end_time: log_start + 1.hour + 30.minutes)
        
        break_id = break_record.id
        
        expect {
          log.destroy!
        }.to change { Break.where(id: break_id).count }.from(1).to(0)
      end
    end
  end # end describe 'associations'

end # end RSpec.describe