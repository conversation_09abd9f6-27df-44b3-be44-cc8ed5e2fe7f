# ABOUTME: AdminPolicy spec tests admin access control with multiple security layers
# ABOUTME: Verifies that only users meeting all admin criteria can access admin functions
require 'rails_helper'

RSpec.describe AdminPolicy, type: :policy do
  let(:company1) { create(:company, id: 1) }
  let(:company2) { create(:company, id: 2) }
  let(:other_company) { create(:company, id: 999) }
  
  let(:admin_user) do
    create(:user, email: '<EMAIL>').tap do |user|
      UserProfile.create!(user: user, first_name: 'Admin', last_name: 'User')
      create(:company_user_role, user: user, company: company1, role: create(:role, name: 'owner'))
    end
  end
  
  let(:mario_admin) do
    create(:user, email: '<EMAIL>').tap do |user|
      UserProfile.create!(user: user, first_name: 'Admin', last_name: 'User')
      create(:company_user_role, user: user, company: company2, role: create(:role, name: 'owner'))
    end
  end
  
  let(:regular_user) do
    create(:user, email: '<EMAIL>').tap do |user|
      UserProfile.create!(user: user, first_name: 'Regular', last_name: 'User')
      create(:company_user_role, user: user, company: other_company, role: create(:role, name: 'owner'))
    end
  end
  
  let(:wrong_email_user) do
    create(:user, email: '<EMAIL>').tap do |user|
      UserProfile.create!(user: user, first_name: 'Admin', last_name: 'User')
      create(:company_user_role, user: user, company: company1, role: create(:role, name: 'owner'))
    end
  end
  
  let(:wrong_company_user) do
    create(:user, email: '<EMAIL>').tap do |user|
      UserProfile.create!(user: user, first_name: 'Admin', last_name: 'User')
      create(:company_user_role, user: user, company: other_company, role: create(:role, name: 'owner'))
    end
  end
  
  let(:wrong_profile_user) do
    create(:user, email: '<EMAIL>').tap do |user|
      UserProfile.create!(user: user, first_name: 'Regular', last_name: 'User')
      create(:company_user_role, user: user, company: company1, role: create(:role, name: 'owner'))
    end
  end

  describe '#access?' do
    subject { described_class.new(user: user).access? }

    context 'when user meets all admin criteria' do
      context 'with <EMAIL> email' do
        let(:user) { admin_user }
        it { is_expected.to be_truthy }
      end
      
      context 'with <EMAIL> email' do
        let(:user) { mario_admin }
        it { is_expected.to be_truthy }
      end
    end

    context 'when user fails admin criteria' do
      context 'with wrong email' do
        let(:user) { wrong_email_user }
        it { is_expected.to be_falsey }
      end
      
      context 'with wrong company' do
        let(:user) { wrong_company_user }
        it { is_expected.to be_falsey }
      end
      
      context 'with wrong profile name' do
        let(:user) { wrong_profile_user }
        it { is_expected.to be_falsey }
      end
      
      context 'with regular user' do
        let(:user) { regular_user }
        it { is_expected.to be_falsey }
      end
      
      context 'with nil user' do
        it 'raises authorization context missing error' do
          expect { described_class.new(user: nil).access? }.to raise_error(ActionPolicy::AuthorizationContextMissing)
        end
      end
    end
  end

  describe '#manage_companies?' do
    subject { described_class.new(user: user).manage_companies? }
    
    context 'when user has admin access' do
      let(:user) { admin_user }
      it { is_expected.to be_truthy }
    end
    
    context 'when user does not have admin access' do
      let(:user) { regular_user }
      it { is_expected.to be_falsey }
    end
  end

  describe '#manage_subscriptions?' do
    subject { described_class.new(user: user).manage_subscriptions? }
    
    context 'when user has admin access' do
      let(:user) { admin_user }
      it { is_expected.to be_truthy }
    end
    
    context 'when user does not have admin access' do
      let(:user) { regular_user }
      it { is_expected.to be_falsey }
    end
  end
end