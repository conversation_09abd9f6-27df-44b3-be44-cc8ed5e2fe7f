# ABOUTME: API v1 DailyLogs controller tests
# ABOUTME: Tests for daily log CRUD operations and cascade deletion functionality

require 'rails_helper'

RSpec.describe "Api::V1::DailyLogs", type: :request do
  let(:user) { create(:user) }
  let(:company) { create(:company) }
  let(:contract) { create(:contract, user: user, company: company, status: :active) }
  
  # Setup role for the user
  let(:role) { create(:role, name: 'employee') }
  let!(:user_role) { create(:company_user_role, user: user, company: company, role: role) }

  before do
    ActsAsTenant.current_tenant = company
  end

  describe "DELETE /api/v1/daily_logs/:id" do
    context "when daily log has associated activities" do
      # Create an open daily log (no end_time) so we can add activities
      let!(:daily_log) { create(:daily_log, user: user, company: company, contract: contract, end_time: nil) }
      let!(:activity1) { create(:daily_activity, daily_log: daily_log, user: user, company: company, contract: contract) }
      let!(:activity2) { create(:daily_activity, daily_log: daily_log, user: user, company: company, contract: contract) }
      # For breaks, need to create a completed daily log with proper timeframe
      let!(:daily_log_with_break) { create(:daily_log, user: user, company: company, contract: contract, start_time: 4.hours.ago, end_time: 1.hour.ago) }
      let!(:break1) { create(:break, daily_log: daily_log_with_break, start_time: 3.hours.ago, end_time: 2.hours.ago) }
      
      it "successfully deletes daily log and cascades to activities" do
        token = jwt_token_for(user)
        
        expect {
          delete "/api/v1/daily_logs/#{daily_log.id}",
                headers: auth_headers(token).merge({ 'Accept' => 'application/json' })
        }.to change { DailyLog.count }.by(-1)
         .and change { DailyActivity.count }.by(-2)
        
        expect(response).to have_http_status(:ok)
        json = JSON.parse(response.body)
        expect(json['message']).to be_present
      end
      
      it "successfully deletes daily log with breaks" do
        token = jwt_token_for(user)
        
        expect {
          delete "/api/v1/daily_logs/#{daily_log_with_break.id}",
                headers: auth_headers(token).merge({ 'Accept' => 'application/json' })
        }.to change { DailyLog.count }.by(-1)
         .and change { Break.count }.by(-1)
        
        expect(response).to have_http_status(:ok)
        json = JSON.parse(response.body)
        expect(json['message']).to be_present
      end
      
      it "prevents foreign key constraint violation" do
        token = jwt_token_for(user)
        
        expect {
          delete "/api/v1/daily_logs/#{daily_log.id}",
                headers: auth_headers(token).merge({ 'Accept' => 'application/json' })
        }.not_to raise_error
        
        expect(response).to have_http_status(:ok)
      end
    end
    
    context "when daily log has no associated activities" do
      let!(:daily_log) { create(:daily_log, user: user, company: company, contract: contract) }
      
      it "successfully deletes daily log" do
        token = jwt_token_for(user)
        
        expect {
          delete "/api/v1/daily_logs/#{daily_log.id}",
                headers: auth_headers(token).merge({ 'Accept' => 'application/json' })
        }.to change { DailyLog.count }.by(-1)
        
        expect(response).to have_http_status(:ok)
        json = JSON.parse(response.body)
        expect(json['message']).to be_present
      end
    end
    
    context "when daily log doesn't exist in user's contract scope" do
      it "returns 404" do
        token = jwt_token_for(user)
        
        delete "/api/v1/daily_logs/999999",
              headers: auth_headers(token).merge({ 'Accept' => 'application/json' })
        
        expect(response).to have_http_status(:not_found)
        json = JSON.parse(response.body)
        expect(json['error']).to be_present
      end
    end
    
    context "when daily log belongs to different contract" do
      let(:other_user) { create(:user) }
      let(:other_contract) { create(:contract, user: other_user, company: company, status: :active) }
      let!(:other_daily_log) { create(:daily_log, user: other_user, company: company, contract: other_contract) }
      let!(:other_role) { create(:company_user_role, user: other_user, company: company, role: role) }
      
      it "returns 404 (not found within user's contract scope)" do
        token = jwt_token_for(user)
        
        delete "/api/v1/daily_logs/#{other_daily_log.id}",
              headers: auth_headers(token).merge({ 'Accept' => 'application/json' })
        
        # The user's contract exists but the daily log doesn't belong to their contract
        # This returns 404 from set_daily_log method
        expect(response).to have_http_status(:not_found)
      end
    end
    
    context "when user has no contract" do
      let(:user_without_contract) { create(:user) }
      let!(:role_without_contract) { create(:company_user_role, user: user_without_contract, company: company, role: role) }
      let!(:daily_log) { create(:daily_log, user: user, company: company, contract: contract) }
      
      it "returns 403 (no contract)" do
        token = jwt_token_for(user_without_contract)
        
        delete "/api/v1/daily_logs/#{daily_log.id}",
              headers: auth_headers(token).merge({ 'Accept' => 'application/json' })
        
        expect(response).to have_http_status(:forbidden)
        json = JSON.parse(response.body)
        expect(json['error']).to be_present
      end
    end
    
    context "without authentication" do
      let!(:daily_log) { create(:daily_log, user: user, company: company, contract: contract) }
      
      it "returns 401" do
        delete "/api/v1/daily_logs/#{daily_log.id}",
              headers: { 'Accept' => 'application/json' }
        
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
end