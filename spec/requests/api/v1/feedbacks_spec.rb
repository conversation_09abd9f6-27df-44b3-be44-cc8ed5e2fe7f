require 'rails_helper'

RSpec.describe 'Api::V1::Feedbacks', type: :request do
  include JwtHelpers

  let(:headers) { { 'CONTENT_TYPE' => 'application/json' } }

  describe 'POST /api/v1/feedbacks' do
    let(:company) { create(:company) }
    let(:user)    { create(:user) }

    context 'anonymous submission' do
      it 'is not allowed and returns 401' do
        payload = {
          feedback: {
            page_url: '/works',
            category: 'bug_report',
            message: 'It crashes on click'
          }
        }

        post '/api/v1/feedbacks', params: payload.to_json, headers: headers

        expect(response).to have_http_status(:unauthorized)
      end
    end

    context 'authenticated submission with tenant context' do
      let!(:role) { create(:role, name: 'employee') }

      before do
        create(:company_user_role, company: company, user: user, role: role)
      end

      it 'captures company/user and user_plan' do
        # Give the company a Plus subscription so current_plan is plus
        plan = create(:plan, :plus)
        create(:subscription, company: company, plan: plan, status: 'active')

        token = JwtService.encode(user_id: user.id, company_id: company.id)
        auth_headers = headers.merge('Authorization' => "Bearer #{token}")

        payload = {
          feedback: {
            page_url: '/bookings',
            category: 'missing_feature',
            message: 'Please add calendar sync'
          }
        }

        post '/api/v1/feedbacks', params: payload.to_json, headers: auth_headers

        expect(response).to have_http_status(:created)
        fb = Feedback.last
        expect(fb.company_id).to eq(company.id)
        expect(fb.user_id).to eq(user.id)
        expect(fb.user_plan).to eq('plus')
        expect(fb.user_company_name).to eq(company.name)
      end
    end

    context 'validation errors' do
      it 'returns 401 when unauthenticated' do
        payload = { feedback: { page_url: '/works', category: 'bug_report', message: 'ok' } }
        post '/api/v1/feedbacks', params: payload.to_json, headers: headers
        expect(response).to have_http_status(:unauthorized)
      end

      it 'returns 422 when message exceeds 300 chars (authenticated)' do
        long_message = 'x' * 301
        # Ensure user is a member of the company for valid tenant context
        role = create(:role, name: 'employee')
        create(:company_user_role, company: company, user: user, role: role)

        token = JwtService.encode(user_id: user.id, company_id: company.id)
        auth_headers = headers.merge('Authorization' => "Bearer #{token}")
        payload = { feedback: { page_url: '/works', category: 'bug_report', message: long_message } }
        post '/api/v1/feedbacks', params: payload.to_json, headers: auth_headers
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end

    context 'rate limiting' do
      it 'returns 429 when too many requests by email' do
        payload = { feedback: { page_url: '/works', category: 'bug_report', message: 'ok', user_email: '<EMAIL>' } }
        3.times { post '/api/v1/feedbacks', params: payload.to_json, headers: headers }
        post '/api/v1/feedbacks', params: payload.to_json, headers: headers
        expect(response.status).to eq(429)
      end
    end
  end
end

